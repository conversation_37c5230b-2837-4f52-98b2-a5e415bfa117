﻿#include "fine_fitting.h"
#include "zhunjunyunsanci.h"

// Helper function to convert Matrix* to double[][3] for zhunjunyunsanci_2
void convert_matrix_to_array(Matrix* mat, double array[][3]) {
    for (int i = 0; i < mat->rows; i++) {
        for (int j = 0; j < 3; j++) {
            array[i][j] = get_matrix_element(mat, i, j);
        }
    }
}

// Wrapper function for zhunjunyunsanci_2 that accepts Matrix* arguments
void zhunjunyunsanci_2_wrapper(int n, Matrix* P, Matrix** Nikx, Matrix** Niky, Matrix** Nikz, Matrix** p_u_x, Matrix** p_u_y, Matrix** p_u_z) {
    // Convert Matrix P to double array
    double (*P_array)[3] = malloc(n * sizeof(*P_array));
    if (!P_array) {
        fprintf(stderr, "Memory allocation failed in zhunjunyunsanci_2_wrapper\n");
        return;
    }
    convert_matrix_to_array(P, P_array);
    
    // Call the original function
    UniformCubicResult result = zhunjunyunsanci_2(n, P_array);
    
    // Copy results back to Matrix format
    int rows = result.rows;
    int nik_size = 4 * (n - 3); // 计算Nik系数的实际数量
    
    // 正确创建不同大小的矩阵
    if (*Nikx != NULL) {
        free_matrix(*Nikx);
        free_matrix(*Niky);
        free_matrix(*Nikz);
    }
    
    *Nikx = create_matrix(4, n - 3);  // 修改为正确的维度
    *Niky = create_matrix(4, n - 3);  // 修改为正确的维度
    *Nikz = create_matrix(4, n - 3);  // 修改为正确的维度
    *p_u_x = create_matrix(rows, 1);
    *p_u_y = create_matrix(rows, 1);
    *p_u_z = create_matrix(rows, 1);
    
    // 确保不越界并正确复制数据
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < n - 3; j++) {
            if (result.Nikx != NULL) {
                set_matrix_element(*Nikx, i, j, result.Nikx[i + j * 4]);
            }
            if (result.Niky != NULL) {
                set_matrix_element(*Niky, i, j, result.Niky[i + j * 4]);
            }
            if (result.Nikz != NULL) {
                set_matrix_element(*Nikz, i, j, result.Nikz[i + j * 4]);
            }
        }
    }
    
    // 确保不越界
    for (int i = 0; i < rows && result.p_u_x != NULL; i++) {
        set_matrix_element(*p_u_x, i, 0, result.p_u_x[i]);
        set_matrix_element(*p_u_y, i, 0, result.p_u_y[i]);
        set_matrix_element(*p_u_z, i, 0, result.p_u_z[i]);
    }
    
    // Free the resources
    free(P_array);
    free_uniform_cubic_result(&result);
}
// Wrapper function for zhunjunyunsanci_2 that accepts Matrix* arguments
void zhunjunyunsanci_wrapper(int n, Matrix* P, Matrix** Nikx, Matrix** Niky, Matrix** Nikz, Matrix** p_u_x, Matrix** p_u_y, Matrix** p_u_z) {
    // Convert Matrix P to double array
    double (*P_array)[3] = malloc(n * sizeof(*P_array));
    if (!P_array) {
        fprintf(stderr, "Memory allocation failed in zhunjunyunsanci_2_wrapper\n");
        return;
    }
    convert_matrix_to_array(P, P_array);

    // Call the original function
    UniformCubicResultz result = zhunjunyunsanci(n, P_array);

    // Copy results back to Matrix format
    int rows = result.rows;
    int nik_size = 4 * (n - 3); // 计算Nik系数的实际数量

    // 正确创建不同大小的矩阵
    if (Nikx && *Nikx != NULL) {
        free_matrix(*Nikx);
        free_matrix(*Niky);
        free_matrix(*Nikz);
    }

    if (Nikx) *Nikx = create_matrix(4, n - 3);
    if (Niky) *Niky = create_matrix(4, n - 3);
    if (Nikz) *Nikz = create_matrix(4, n - 3);
    *p_u_x = create_matrix(rows, 1);
    *p_u_y = create_matrix(rows, 1);
    *p_u_z = create_matrix(rows, 1);

    // 确保不越界并正确复制数据
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < n - 3; j++) {
            if (result.Nikx != NULL) {
                if (Nikx) set_matrix_element(*Nikx, i, j, result.Nikx[i + j * 4]);
            }
            if (result.Niky != NULL) {
                if (Niky)set_matrix_element(*Niky, i, j, result.Niky[i + j * 4]);
            }
            if (result.Nikz != NULL) {
                if (Nikz)set_matrix_element(*Nikz, i, j, result.Nikz[i + j * 4]);
            }
        }
    }

    // 确保不越界
    for (int i = 0; i < rows && result.p_u_x != NULL; i++) {
        set_matrix_element(*p_u_x, i, 0, result.p_u_x[i]);
        set_matrix_element(*p_u_y, i, 0, result.p_u_y[i]);
        set_matrix_element(*p_u_z, i, 0, result.p_u_z[i]);
    }

    // Free the resources
    free(P_array);
    free_uniform_cubic_resultz(&result);
}
// Helper function to convert Matrix* to double[][3] for shujuchuli
void shujuchuli_wrapper(Matrix* P, int rows, Matrix** K, Matrix** D1, Matrix** D2, Matrix** N, Matrix** Bt) {
    //// 打印P的值
    //printf("\n--- 打印P矩阵值 (调用shujuchuli前) ---\n");
    //printf("P矩阵大小: %d 行 x %d 列\n", P->rows, P->cols);
    //for (int i = 0; i < P->rows; i++) {
    //    printf("P[%d]: ", i);
    //    for (int j = 0; j < P->cols; j++) {
    //        printf("%f ", get_matrix_element(P, i, j));
    //    }
    //    printf("\n");
    //}
    //printf("--- P矩阵打印结束 ---\n\n");

    double (*P_array)[3] = malloc(rows * sizeof(*P_array));
    if (!P_array) {
        fprintf(stderr, "Memory allocation failed in shujuchuli_wrapper\n");
        return;
    }
    convert_matrix_to_array(P, P_array);

    
    // Call the original function
    ShujuResult result = shujuchuli(P_array, rows, rows);
    
    // 检查结果是否有效
    if (result.length <= 0 || !result.K || !result.D1 || !result.D2 || !result.N || !result.Bt) {
        fprintf(stderr, "shujuchuli返回的结果无效\n");
        free(P_array);
        return;
    }
    
    // Copy results back to Matrix format - 注意每个结果的真实大小
    *K = create_matrix(result.length, 1);
    *D1 = create_matrix(result.length, 3);
    *D2 = create_matrix(result.length, 3);
    *N = create_matrix(result.length, 3);
    *Bt = create_matrix(result.length, 3);
    
    // 确保不越界并正确复制数据
    for (int i = 0; i < result.length; i++) {
        set_matrix_element(*K, i, 0, result.K[i]);
        
        set_matrix_element(*D1, i, 0, result.D1[i].x);
        set_matrix_element(*D1, i, 1, result.D1[i].y);
        set_matrix_element(*D1, i, 2, result.D1[i].z);
        
        set_matrix_element(*D2, i, 0, result.D2[i].x);
        set_matrix_element(*D2, i, 1, result.D2[i].y);
        set_matrix_element(*D2, i, 2, result.D2[i].z);
        
        set_matrix_element(*N, i, 0, result.N[i].x);
        set_matrix_element(*N, i, 1, result.N[i].y);
        set_matrix_element(*N, i, 2, result.N[i].z);
        
        set_matrix_element(*Bt, i, 0, result.Bt[i].x);
        set_matrix_element(*Bt, i, 1, result.Bt[i].y);
        set_matrix_element(*Bt, i, 2, result.Bt[i].z);
    }
    
    // Free the resources
    free(P_array);
    free_shuju_result(result);
}

//  fine_fitting Ӧ MATLABе fine_fitting 
void fine_fitting(Matrix* arc, Matrix** pos, Matrix** Nikx, Matrix** Niky, Matrix** Nikz, Matrix** X,int diedai) {
    if (!arc || !pos || !Nikx || !Niky || !Nikz || !X) {
        fprintf(stderr, "Invalid input parameters in fine_fitting\n");
        return;
    }

    // 初始化输出矩阵为NULL
    *pos = NULL;
    *Nikx = NULL;
    *Niky = NULL;
    *Nikz = NULL;
    *X = NULL;

    int num = arc->rows;
    int quotient, remainder;
    my_divmod(num, 4, &quotient, &remainder);

    int Px_rows, Py_rows, Pz_rows;
    if (remainder < 1) {
        Px_rows = quotient - 1;
    }
    else {
        Px_rows = quotient;
    }

    Matrix* Px = create_matrix(Px_rows, 1);
    Matrix* Py = create_matrix(Px_rows, 1);
    Matrix* Pz = create_matrix(Px_rows, 1);

    if (!Px || !Py || !Pz) {
        fprintf(stderr, "Failed to create P matrices\n");
        free_matrix(Px);
        free_matrix(Py);
        free_matrix(Pz);
        return;
    }

    if (remainder < 1) {
        for (int i = 1; i < quotient; i++) {
            set_matrix_element(Px, i - 1, 0, get_matrix_element(arc, i * 4 - 1, 0));
            set_matrix_element(Py, i - 1, 0, get_matrix_element(arc, i * 4 - 1, 1));
            set_matrix_element(Pz, i - 1, 0, get_matrix_element(arc, i * 4 - 1, 2));
        }
    }
    else {
        for (int i = 1; i < quotient + 1; i++) {
            set_matrix_element(Px, i - 1, 0, get_matrix_element(arc, i * 4 - 1, 0));
            set_matrix_element(Py, i - 1, 0, get_matrix_element(arc, i * 4 - 1, 1));
            set_matrix_element(Pz, i - 1, 0, get_matrix_element(arc, i * 4 - 1, 2));
        }
    }

    Matrix* first_x = create_matrix(1, 1);
    Matrix* first_y = create_matrix(1, 1);
    Matrix* first_z = create_matrix(1, 1);
    Matrix* last_x = create_matrix(1, 1);
    Matrix* last_y = create_matrix(1, 1);
    Matrix* last_z = create_matrix(1, 1);

    set_matrix_element(first_x, 0, 0, get_matrix_element(arc, 0, 0));
    set_matrix_element(first_y, 0, 0, get_matrix_element(arc, 0, 1));
    set_matrix_element(first_z, 0, 0, get_matrix_element(arc, 0, 2));
    set_matrix_element(last_x, 0, 0, get_matrix_element(arc, num - 1, 0));
    set_matrix_element(last_y, 0, 0, get_matrix_element(arc, num - 1, 1));
    set_matrix_element(last_z, 0, 0, get_matrix_element(arc, num - 1, 2));

    Px = vcat_step(first_x, Px);
    Px = vcat_step(Px, last_x);
    Py = vcat_step(first_y, Py);
    Py = vcat_step(Py, last_y);
    Pz = vcat_step(first_z, Pz);
    Pz = vcat_step(Pz, last_z);

    // ϲPx, Py, Pz
    Matrix* P = create_matrix(Px->rows, 3);
    for (int i = 0; i < Px->rows; i++) {
        set_matrix_element(P, i, 0, get_matrix_element(Px, i, 0));
        set_matrix_element(P, i, 1, get_matrix_element(Py, i, 0));
        set_matrix_element(P, i, 2, get_matrix_element(Pz, i, 0));
    }

    // ͷڴ
    free_matrix(Px);
    free_matrix(Py);
    free_matrix(Pz);

    Matrix* M = copy_matrix(P);

    Matrix* p_u_x = NULL;
    Matrix* p_u_y = NULL;
    Matrix* p_u_z = NULL;

    for (int g = 0; g < diedai; g++) {
        int n = M->rows;
        
        // 在每次循环中重新分配这些矩阵，防止内存泄漏
        if (p_u_x != NULL) {
            free_matrix(p_u_x);
            free_matrix(p_u_y);
            free_matrix(p_u_z);
            p_u_x = NULL;
            p_u_y = NULL;
            p_u_z = NULL;
        }
        
        // 在每次迭代中都重新计算 Nikx, Niky, Nikz，因为 P 在变化
        if (*Nikx != NULL) {
            free_matrix(*Nikx);
            free_matrix(*Niky);
            free_matrix(*Nikz);
            *Nikx = NULL;
            *Niky = NULL;
            *Nikz = NULL;
        }
        zhunjunyunsanci_2_wrapper(n, P, Nikx, Niky, Nikz, &p_u_x, &p_u_y, &p_u_z);

       //************************************************************************************************ 
        //Matrix* chazhi = create_matrix((int)(p_u_x->rows), n);
        //for (int m = 0; m < n; m++) {
        //    for (int i = 0; i < p_u_x->rows; i = i + 1) {
        //        double diff = sqrt(pow(get_matrix_element(M, m, 0) - get_matrix_element(p_u_x, i, 0), 2) +
        //            pow(get_matrix_element(M, m, 1) - get_matrix_element(p_u_y, i, 0), 2) +
        //            pow(get_matrix_element(M, m, 2) - get_matrix_element(p_u_z, i, 0), 2));
        //        set_matrix_element(chazhi, i, m, diff);
        //    }
        //}

        //// ֵСֵԼӦ i 
        //Matrix* columnIndexes = create_matrix(n, 1);
        //for (int m = 0; m < n; m++) {
        //    double min_val = get_matrix_element(chazhi, 0, m);
        //    int min_index = 0;
        //    for (int i = 1; i < chazhi->rows; i++) {
        //        if (get_matrix_element(chazhi, i, m) < min_val) {
        //            min_val = get_matrix_element(chazhi, i, m);
        //            min_index = i;
        //        }
        //    }
        //    set_matrix_element(columnIndexes, m, 0, min_index);
        //}
         
        Matrix* columnIndexes = create_matrix(n, 1);
        for (int m = 0; m < n; m++) {
            double min_val = -1.0;
            int min_index = 0;
            double Mx = get_matrix_element(M, m, 0);
            double My = get_matrix_element(M, m, 1);
            double Mz = get_matrix_element(M, m, 2);
            for (int i = 0; i < p_u_x->rows; i++) {
                double dx = Mx - get_matrix_element(p_u_x, i, 0);
                double dy = My - get_matrix_element(p_u_y, i, 0);
                double dz = Mz - get_matrix_element(p_u_z, i, 0);
               // double diff = sqrt(dx * dx + dy * dy + dz * dz);
                double diff = dx * dx + dy * dy + dz * dz;

                if (i == 0 || diff < min_val) {
                    min_val = diff;
                    min_index = i;
                }
            }
            set_matrix_element(columnIndexes, m, 0, min_index);
        }
//**********************************************************************************************
        // λ
        Matrix* s_x = create_matrix(n, 1);
        Matrix* s_y = create_matrix(n, 1);
        Matrix* s_z = create_matrix(n, 1);
        for (int t = 0; t < n; t++) {
            int index = (int)get_matrix_element(columnIndexes, t, 0);
            set_matrix_element(s_x, t, 0, get_matrix_element(M, t, 0) - get_matrix_element(p_u_x, index, 0));
            set_matrix_element(s_y, t, 0, get_matrix_element(M, t, 1) - get_matrix_element(p_u_y, index, 0));
            set_matrix_element(s_z, t, 0, get_matrix_element(M, t, 2) - get_matrix_element(p_u_z, index, 0));
        }
        Matrix* s_x_y_z = create_matrix(n, 3);
        for (int i = 0; i < n; i++) {
            set_matrix_element(s_x_y_z, i, 0, get_matrix_element(s_x, i, 0));
            set_matrix_element(s_x_y_z, i, 1, get_matrix_element(s_y, i, 0));
            set_matrix_element(s_x_y_z, i, 2, get_matrix_element(s_z, i, 0));
        }
        free_matrix(s_x);
        free_matrix(s_y);
        free_matrix(s_z);

        Matrix* p_u_x_1, * p_u_y_1, * p_u_z_1;
        Matrix* Nikx1=NULL; Matrix* Niky1 = NULL; Matrix* Nikz1 = NULL;


        zhunjunyunsanci_wrapper(n, s_x_y_z, NULL, NULL, NULL, &p_u_x_1, &p_u_y_1, &p_u_z_1);
       // zhunjunyunsanci_3(n, s_x_y_z, &p_u_x_1, &p_u_y_1, &p_u_z_1);
        free_matrix(s_x_y_z);
        free_matrix(Nikx1);
		free_matrix(Niky1);
		free_matrix(Nikz1);

        for (int t = 0; t < n; t++) {
            int index = (int)get_matrix_element(columnIndexes, t, 0);
            double a = get_matrix_element(P, t, 0) + 0.4 * get_matrix_element(p_u_x_1, index, 0);
            double b = get_matrix_element(P, t, 1) + 0.4 * get_matrix_element(p_u_y_1, index, 0);
            double c = get_matrix_element(P, t, 2) + 0.4 * get_matrix_element(p_u_z_1, index, 0);
            set_matrix_element(P, t, 0, a);
            set_matrix_element(P, t, 1, b);
            set_matrix_element(P, t, 2, c);
        }


       // free_matrix(chazhi);
        free_matrix(columnIndexes);
        free_matrix(p_u_x_1);
        free_matrix(p_u_y_1);
        free_matrix(p_u_z_1);
    }

    *pos = create_matrix(p_u_x->rows, 3);
    if (!*pos) {
        fprintf(stderr, "Failed to create pos matrix\n");
        return;
    }

    // 确保数据被正确复制到pos矩阵
    for (int i = 0; i < p_u_x->rows; i++) {
        set_matrix_element(*pos, i, 0, get_matrix_element(p_u_x, i, 0));
        set_matrix_element(*pos, i, 1, get_matrix_element(p_u_y, i, 0));
        set_matrix_element(*pos, i, 2, get_matrix_element(p_u_z, i, 0));
    }

    // 确保Nikx, Niky, Nikz矩阵被正确创建和填充
    if (!*Nikx || !*Niky || !*Nikz) {
        fprintf(stderr, "Failed to create Nik matrices\n");
        free_matrix(*pos);
        *pos = NULL;
        return;
    }

    // 创建并填充X矩阵
    Matrix* K = NULL, * D1 = NULL, * D2 = NULL, * N = NULL, * Bt = NULL;
    shujuchuli_wrapper(P, P->rows, &K, &D1, &D2, &N, &Bt);

    if (!K || !D1 || !D2 || !N || !Bt) {
        fprintf(stderr, "Failed to create matrices in shujuchuli_wrapper\n");
        free_matrix(*pos);
        free_matrix(*Nikx);
        free_matrix(*Niky);
        free_matrix(*Nikz);
        *pos = NULL;
        *Nikx = NULL;
        *Niky = NULL;
        *Nikz = NULL;
        return;
    }

    // 创建X矩阵
    int X_cols = K->cols + D1->cols + D2->cols + N->cols + Bt->cols + p_u_x->cols + p_u_y->cols + p_u_z->cols;
    *X = create_matrix(K->rows, X_cols);

    if (!*X) {
        fprintf(stderr, "Failed to create X matrix\n");
        free_matrix(*pos);
        free_matrix(*Nikx);
        free_matrix(*Niky);
        free_matrix(*Nikz);
        free_matrix(K);
        free_matrix(D1);
        free_matrix(D2);
        free_matrix(N);
        free_matrix(Bt);
        *pos = NULL;
        *Nikx = NULL;
        *Niky = NULL;
        *Nikz = NULL;
        return;
    }

    // 复制数据到X矩阵
    int col_offset = 0;
    
    // 复制K矩阵
    for (int i = 0; i < K->rows; i++) {
        for (int j = 0; j < K->cols; j++) {
            set_matrix_element(*X, i, col_offset + j, get_matrix_element(K, i, j));
        }
    }
    col_offset += K->cols;

    // 复制D1矩阵
    for (int i = 0; i < D1->rows; i++) {
        for (int j = 0; j < D1->cols; j++) {
            set_matrix_element(*X, i, col_offset + j, get_matrix_element(D1, i, j));
        }
    }
    col_offset += D1->cols;

    // 复制D2矩阵
    for (int i = 0; i < D2->rows; i++) {
        for (int j = 0; j < D2->cols; j++) {
            set_matrix_element(*X, i, col_offset + j, get_matrix_element(D2, i, j));
        }
    }
    col_offset += D2->cols;

    // 复制N矩阵
    for (int i = 0; i < N->rows; i++) {
        for (int j = 0; j < N->cols; j++) {
            set_matrix_element(*X, i, col_offset + j, get_matrix_element(N, i, j));
        }
    }
    col_offset += N->cols;

    // 复制Bt矩阵
    for (int i = 0; i < Bt->rows; i++) {
        for (int j = 0; j < Bt->cols; j++) {
            set_matrix_element(*X, i, col_offset + j, get_matrix_element(Bt, i, j));
        }
    }
    col_offset += Bt->cols;

    // 复制p_u_x矩阵
    for (int i = 0; i < p_u_x->rows; i++) {
        for (int j = 0; j < p_u_x->cols; j++) {
            set_matrix_element(*X, i, col_offset + j, get_matrix_element(p_u_x, i, j));
        }
    }
    col_offset += p_u_x->cols;

    // 复制p_u_y矩阵
    for (int i = 0; i < p_u_y->rows; i++) {
        for (int j = 0; j < p_u_y->cols; j++) {
            set_matrix_element(*X, i, col_offset + j, get_matrix_element(p_u_y, i, j));
        }
    }
    col_offset += p_u_y->cols;

    // 复制p_u_z矩阵
    for (int i = 0; i < p_u_z->rows; i++) {
        for (int j = 0; j < p_u_z->cols; j++) {
            set_matrix_element(*X, i, col_offset + j, get_matrix_element(p_u_z, i, j));
        }
    }

    // 清理临时矩阵
    free_matrix(P);
    free_matrix(M);
    free_matrix(p_u_x);
    free_matrix(p_u_y);
    free_matrix(p_u_z);
    free_matrix(K);
    free_matrix(D1);
    free_matrix(D2);
    free_matrix(N);
    free_matrix(Bt);
}