//#define _CRT_SECURE_NO_WARNINGS
//#include <stdio.h>
//#include <stdlib.h>
//#include <string.h>
//#include "shujuchuli.h"
//
//// Function to read data from a file
//double** read_data_from_file(const char* filename, int* rows_out, int* cols_out) {
//    FILE* file = fopen(filename, "r");
//    if (!file) {
//        fprintf(stderr, "Error opening file: %s\n", filename);
//        return NULL;
//    }
//
//    // Count lines and columns
//    char line[1024];
//    int rows = 0;
//    int cols = 0;
//
//    // Get the first line to determine column count
//    if (fgets(line, sizeof(line), file)) {
//        char* token = strtok(line, "\t");
//        while (token) {
//            cols++;
//            token = strtok(NULL, "\t");
//        }
//        rows = 1; // Count the first line
//    }
//
//    // Count remaining lines
//    while (fgets(line, sizeof(line), file)) {
//        rows++;
//    }
//
//    printf("File contains %d rows and %d columns\n", rows, cols);
//
//    // Reset file position to beginning
//    rewind(file);
//
//    // Allocate memory for data
//    double** data = (double**)malloc(rows * sizeof(double*));
//    if (!data) {
//        fprintf(stderr, "Memory allocation failed\n");
//        fclose(file);
//        return NULL;
//    }
//
//    for (int i = 0; i < rows; i++) {
//        data[i] = (double*)malloc(cols * sizeof(double));
//        if (!data[i]) {
//            fprintf(stderr, "Memory allocation failed for row %d\n", i);
//            // Free previously allocated memory
//            for (int j = 0; j < i; j++) {
//                free(data[j]);
//            }
//            free(data);
//            fclose(file);
//            return NULL;
//        }
//    }
//
//    // Read data from file
//    int row = 0;
//    while (fgets(line, sizeof(line), file) && row < rows) {
//        char* token = strtok(line, "\t");
//        int col = 0;
//
//        while (token && col < cols) {
//            data[row][col] = atof(token);
//            col++;
//            token = strtok(NULL, "\t");
//        }
//
//        row++;
//    }
//
//    fclose(file);
//    *rows_out = rows;
//    *cols_out = cols;
//    return data;
//}
//
//// Function to convert position data to the format needed by shujuchuli
//double (*extract_position_data(double** data, int rows, int cols))[3] {
//    // Allocate memory for position data (x, y, z coordinates)
//    double (*position_data)[3] = (double(*)[3])malloc(rows * sizeof(double[3]));
//    if (!position_data) {
//        fprintf(stderr, "Memory allocation failed for position data\n");
//        return NULL;
//    }
//
//    // Extract position data (first 3 columns)
//    for (int i = 0; i < rows; i++) {
//        position_data[i][0] = data[i][0]; // x
//        position_data[i][1] = data[i][1]; // y
//        position_data[i][2] = data[i][2]; // z
//    }
//
//    return position_data;
//}
//
//// Main function to test shujuchuli
//int main() {
//    // Read data from file
//    int rows, cols;
//    double** raw_data = read_data_from_file("Raw_Data.txt", &rows, &cols);
//    if (!raw_data) {
//        return 1;
//    }
//
//    printf("Successfully read %d rows and %d columns of data\n", rows, cols);
//
//    // Extract position data
//    double (*position_data)[3] = extract_position_data(raw_data, rows, cols);
//    if (!position_data) {
//        // Free raw data
//        for (int i = 0; i < rows; i++) {
//            free(raw_data[i]);
//        }
//        free(raw_data);
//        return 1;
//    }
//
//    // Test with 242 control points, which should give us ~5019 K values in MATLAB
//    int n = 242;
//    printf("\n--- Testing shujuchuli with %d control points (to match MATLAB) ---\n", n);
//
//    // Call shujuchuli
//    ShujuResult result = shujuchuli(position_data, n, rows);
//
//    // Validate result
//    if (result.K == NULL || result.length <= 0) {
//        printf("Error: shujuchuli failed to return valid K values for n=%d\n", n);
//
//        // Free memory
//        for (int i = 0; i < rows; i++) {
//            free(raw_data[i]);
//        }
//        free(raw_data);
//        free(position_data);
//        return 1;
//    }
//
//    // Display result statistics
//    printf("Result contains %d K values\n", result.length);
//    printf("MATLAB should produce 5019 K values\n");
//
//    // Display K values in batches (too many to show all at once)
//    printf("First 20 K values:\n");
//    for (int i = 0; i < 20 && i < result.length; i++) {
//        printf("K[%d] = %.10f\n", i, result.K[i]);
//    }
//
//    printf("\nMiddle 10 K values (around index %d):\n", result.length / 2);
//    for (int i = result.length / 2 - 5; i < result.length / 2 + 5 && i < result.length; i++) {
//        printf("K[%d] = %.10f\n", i, result.K[i]);
//    }
//
//    printf("\nLast 20 K values:\n");
//    for (int i = result.length - 20; i < result.length && i >= 0; i++) {
//        printf("K[%d] = %.10f\n", i, result.K[i]);
//    }
//
//    // Save ALL K values to a file for comparison with MATLAB
//    char filename[100];
//    sprintf(filename, "shujuchuli_result_n%d.txt", n);
//    FILE* outfile = fopen(filename, "w");
//    if (outfile) {
//        fprintf(outfile, "K values (length = %d):\n", result.length);
//        for (int i = 0; i < result.length; i++) {
//            fprintf(outfile, "%.10f\n", result.K[i]);
//        }
//        fclose(outfile);
//        printf("\nSaved ALL %d K values to %s for comparison with MATLAB\n",
//            result.length, filename);
//    }
//
//    // Free result
//    free_shuju_result(result);
//
//    // Free memory
//    for (int i = 0; i < rows; i++) {
//        free(raw_data[i]);
//    }
//    free(raw_data);
//    free(position_data);
//
//    printf("\nTest completed successfully\n");
//    return 0;
//}