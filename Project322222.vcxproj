<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{53627659-3a99-4bbc-9680-22a223cef847}</ProjectGuid>
    <RootNamespace>Project322222</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <EnableASAN>true</EnableASAN>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="adaptive_dbscan03.h" />
    <ClInclude Include="Bridge_arc_arc_1.h" />
    <ClInclude Include="Bridge_arc_arc_2.h" />
    <ClInclude Include="bsplan_pf_1.h" />
    <ClInclude Include="calculateArcLength.h" />
    <ClInclude Include="cal_curvature.h" />
    <ClInclude Include="chongfudian02.h" />
    <ClInclude Include="Copy_2_of_Offline_Trajectory_Planning.h" />
    <ClInclude Include="cunihe_01.h" />
    <ClInclude Include="curv_break.h" />
    <ClInclude Include="diff_u.h" />
    <ClInclude Include="dongtaixuanqu_controlPoints.h" />
    <ClInclude Include="findMinIndex.h" />
    <ClInclude Include="find_nearest_point.h" />
    <ClInclude Include="fine_fitting.h" />
    <ClInclude Include="math_utils.h" />
    <ClInclude Include="matrix.h" />
    <ClInclude Include="nihe_qulv.h" />
    <ClInclude Include="Nik_to_control.h" />
    <ClInclude Include="projectPointsOnPlane.h" />
    <ClInclude Include="QC_Inverse_Kinematics.h" />
    <ClInclude Include="Quasi_Bspline_K.h" />
    <ClInclude Include="Quasi_Bspline_K_1.h" />
    <ClInclude Include="QUAT.h" />
    <ClInclude Include="quaternProd.h" />
    <ClInclude Include="R3toS3.h" />
    <ClInclude Include="rotm2eul_new.h" />
    <ClInclude Include="select_points_uniformly.h" />
    <ClInclude Include="shujuchuli.h" />
    <ClInclude Include="shujulvbo.h" />
    <ClInclude Include="shujulvbo01.h" />
    <ClInclude Include="speed_planning.h" />
    <ClInclude Include="s_snew.h" />
    <ClInclude Include="s_timeround.h" />
    <ClInclude Include="Trans_quatoMatrix_new.h" />
    <ClInclude Include="Trans_ZYXeulertoquat.h" />
    <ClInclude Include="U_matrix.h" />
    <ClInclude Include="velocity_bspline_u.h" />
    <ClInclude Include="Velocity_Taylor_1.h" />
    <ClInclude Include="zhunjunyunsanci.h" />
    <ClInclude Include="zhunjunyunsanci_2.h" />
    <ClInclude Include="zhunjunyunsanci_3.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="adaptive_dbscan03.c" />
    <ClCompile Include="Bridge_arc_arc_1.c" />
    <ClCompile Include="Bridge_arc_arc_2.c" />
    <ClCompile Include="bsplan_pf_1.c" />
    <ClCompile Include="calculateArcLength.c" />
    <ClCompile Include="cal_curvature.c" />
    <ClCompile Include="chongfudian02.c" />
    <ClCompile Include="Copy_2_of_Offline_Trajectory_Planning.c" />
    <ClCompile Include="cunihe_01.c" />
    <ClCompile Include="curv_break.c" />
    <ClCompile Include="diff_u.c" />
    <ClCompile Include="dongtaixuanqu_controlPoints.c" />
    <ClCompile Include="findMinIndex.c" />
    <ClCompile Include="main666.c" />
    <ClCompile Include="projectPointsOnPlane.c" />
    <ClCompile Include="QC_Inverse_Kinematics.c" />
    <ClCompile Include="Quasi_Bspline_K_1.c" />
    <ClCompile Include="R3toS3.c" />
    <ClCompile Include="rotm2eul_new.c" />
    <ClCompile Include="test_bsplan_pf_1.c" />
    <ClCompile Include="test_Nik_to_control.c" />
    <ClCompile Include="find_nearest_point.c" />
    <ClCompile Include="fine_fitting.c" />
    <ClCompile Include="math_utils.c" />
    <ClCompile Include="matrix.c" />
    <ClCompile Include="nihe_qulv.c" />
    <ClCompile Include="Nik_to_control.c" />
    <ClCompile Include="Quasi_Bspline_K.c" />
    <ClCompile Include="QUAT.c" />
    <ClCompile Include="quaternProd.c" />
    <ClCompile Include="select_points_uniformly.c" />
    <ClCompile Include="shujuchuli.c" />
    <ClCompile Include="shujulvbo.c" />
    <ClCompile Include="shujulvbo01.c" />
    <ClCompile Include="speed_planning.c" />
    <ClCompile Include="s_snew.c" />
    <ClCompile Include="s_timeround.c" />
    <ClCompile Include="aaaaaaaatest_Copy_2_of_Offline_Trajectory_Planning.c" />
    <ClCompile Include="test_fine_fitting.c" />
    <ClCompile Include="test_QUAT.c" />
    <ClCompile Include="test_select_points.c" />
    <ClCompile Include="test_shujuchuli.c" />
    <ClCompile Include="test_zhunjunyunsanci_3.c" />
    <ClCompile Include="Trans_quatoMatrix_new.c" />
    <ClCompile Include="Trans_ZYXeulertoquat.c" />
    <ClCompile Include="U_matrix.c" />
    <ClCompile Include="velocity_bspline_u.c" />
    <ClCompile Include="Velocity_Taylor_1.c" />
    <ClCompile Include="zhunjunyunsanci.c" />
    <ClCompile Include="zhunjunyunsanci_2.c" />
    <ClCompile Include="zhunjunyunsanci_3.c" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="bridge_results.txt" />
    <Text Include="euler_angles.txt" />
    <Text Include="input.txt" />
    <Text Include="orientation_data.txt" />
    <Text Include="output.txt" />
    <Text Include="position_inter.txt" />
    <Text Include="Raw_Data.txt" />
    <Text Include="Raw_Data1.txt" />
    <Text Include="Raw_Data2.txt" />
    <Text Include="Raw_Data3.txt" />
    <Text Include="shujuchuli_result_n242.txt" />
    <Text Include="temp.txt" />
    <Text Include="test_arc_input.txt" />
    <Text Include="test_Nikx_output.txt" />
    <Text Include="test_Niky_output.txt" />
    <Text Include="test_Nikz_output.txt" />
    <Text Include="test_pos_output.txt" />
    <Text Include="test_X_output.txt" />
    <Text Include="will_fit_pos.txt" />
    <Text Include="z_inter.txt" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>