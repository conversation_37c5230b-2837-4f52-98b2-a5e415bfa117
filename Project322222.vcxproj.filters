﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="cal_curvature.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="dongtaixuanqu_controlPoints.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="nihe_qulv.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Quasi_Bspline_K.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="shujuchuli.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="zhunjunyunsanci_2.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="cunihe_01.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Copy_2_of_Offline_Trajectory_Planning.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="calculateArcLength.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="select_points_uniformly.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="shujulvbo.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="chongfudian02.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="shujulvbo01.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="adaptive_dbscan03.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="matrix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="curv_break.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="find_nearest_point.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="fine_fitting.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="math_utils.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="zhunjunyunsanci_3.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="QUAT.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Trans_ZYXeulertoquat.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="quaternProd.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Nik_to_control.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="bsplan_pf_1.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="velocity_bspline_u.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="speed_planning.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="s_timeround.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="s_snew.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="diff_u.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Bridge_arc_arc_1.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="projectPointsOnPlane.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="findMinIndex.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="U_matrix.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Quasi_Bspline_K_1.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Bridge_arc_arc_2.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Velocity_Taylor_1.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="R3toS3.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Trans_quatoMatrix_new.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="rotm2eul_new.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="QC_Inverse_Kinematics.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="zhunjunyunsanci.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="cal_curvature.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="dongtaixuanqu_controlPoints.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_Nik_to_control.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="nihe_qulv.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Quasi_Bspline_K.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="shujuchuli.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="zhunjunyunsanci_2.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="cunihe_01.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="aaaaaaaatest_Copy_2_of_Offline_Trajectory_Planning.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Copy_2_of_Offline_Trajectory_Planning.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="calculateArcLength.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="select_points_uniformly.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_select_points.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="shujulvbo.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="chongfudian02.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="shujulvbo01.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="adaptive_dbscan03.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="matrix.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_shujuchuli.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="curv_break.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="find_nearest_point.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="fine_fitting.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="math_utils.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="zhunjunyunsanci_3.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="QUAT.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Trans_ZYXeulertoquat.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="quaternProd.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_fine_fitting.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Nik_to_control.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="bsplan_pf_1.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="velocity_bspline_u.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="speed_planning.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="s_timeround.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="s_snew.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="diff_u.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_bsplan_pf_1.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_QUAT.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Bridge_arc_arc_1.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="projectPointsOnPlane.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="findMinIndex.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="U_matrix.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Quasi_Bspline_K_1.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Bridge_arc_arc_2.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Velocity_Taylor_1.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="R3toS3.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Trans_quatoMatrix_new.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="main666.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="rotm2eul_new.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="QC_Inverse_Kinematics.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="test_zhunjunyunsanci_3.c">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="zhunjunyunsanci.c">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Text Include="Raw_Data.txt" />
    <Text Include="Raw_Data1.txt" />
    <Text Include="temp.txt" />
    <Text Include="will_fit_pos.txt" />
    <Text Include="bridge_results.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="shujuchuli_result_n242.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="test_arc_input.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="test_Nikx_output.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="test_Niky_output.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="test_Nikz_output.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="test_pos_output.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="test_X_output.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="orientation_data.txt" />
    <Text Include="euler_angles.txt" />
    <Text Include="input.txt" />
    <Text Include="output.txt" />
    <Text Include="Raw_Data2.txt" />
    <Text Include="Raw_Data3.txt" />
    <Text Include="position_inter.txt">
      <Filter>资源文件</Filter>
    </Text>
    <Text Include="z_inter.txt" />
  </ItemGroup>
</Project>