﻿#pragma once

#define BRIDGE_ARC_H

#include <math.h>
#include <stdlib.h>

// 三维向量结构
typedef struct {
    double x;
    double y;
    double z;
} Vector3D_Bridge_arc_arc_2;

// 复数三维向量结构
typedef struct {
    double real_x;
    double real_y;
    double real_z;
    double imag_x;
    double imag_y;
    double imag_z;
} ComplexVector3D_Bridge_arc_arc_2;

// 控制点结构，用于存储control_point_3
typedef struct {
    Vector3D_Bridge_arc_arc_2 P0;
    Vector3D_Bridge_arc_arc_2 P1;
    Vector3D_Bridge_arc_arc_2 P2;
    Vector3D_Bridge_arc_arc_2 P3;
    Vector3D_Bridge_arc_arc_2 P4;
} ControlPoint3_Bridge_arc_arc_2;

// 桥接结果结构
typedef struct {
    double K1;
    double K2;
    double U1;
    double U2;
    Vector3D_Bridge_arc_arc_2 D1;
    Vector3D_Bridge_arc_arc_2 D2;
    Vector3D_Bridge_arc_arc_2 N1;
    Vector3D_Bridge_arc_arc_2 N2;
    Vector3D_Bridge_arc_arc_2 B1;
    Vector3D_Bridge_arc_arc_2 B2;
    Vector3D_Bridge_arc_arc_2 P0;
    Vector3D_Bridge_arc_arc_2 P4;
    Vector3D_Bridge_arc_arc_2 De;
    Vector3D_Bridge_arc_arc_2 Ds;
    Vector3D_Bridge_arc_arc_2 Pq;
    Vector3D_Bridge_arc_arc_2 P1;
    Vector3D_Bridge_arc_arc_2 P2;
    Vector3D_Bridge_arc_arc_2 P3;
    Vector3D_Bridge_arc_arc_2 P22;
    Vector3D_Bridge_arc_arc_2 Pc;
    Vector3D_Bridge_arc_arc_2 sy1;
    Vector3D_Bridge_arc_arc_2 sy2;
    Vector3D_Bridge_arc_arc_2 P1P2;  // projected_points(3,:)-projected_points(2,:)
    Vector3D_Bridge_arc_arc_2 P3P2;  // projected_points(4,:)-projected_points(3,:)
    Vector3D_Bridge_arc_arc_2 P4P0;  // projected_points(5,:)-projected_points(1,:)
    ComplexVector3D_Bridge_arc_arc_2 P3_complex; // 复数形式的P3
    ComplexVector3D_Bridge_arc_arc_2 Pc_complex; // 复数形式的Pc
    ComplexVector3D_Bridge_arc_arc_2 P1_complex; // 复数形式的P1（用于第二种情况）
    int is_complex;                 // 标记结果是否包含复数
    double m;
    double n;
    double error;                  // 最后一次迭代的误差
    double* error_values;          // 存储每次迭代的误差值，动态分配
    int num_iterations;            // 实际的迭代次数
    int max_iterations;            // 分配的最大迭代次数
    double** projected_points;     // 投影点，动态分配的二维数组
    int num_projected_points;      // 投影点的数量
    double A;                      // P1P2*P4P0'的点积结果
    double B;                      // P3P2*P4P0'的点积结果
    double C;                      // cross(D1,N1)*cross(P1P2,P3P2)'的点积结果
    double D;                      // cross(D2,N2)*cross(P1P2,P3P2)'的点积结果
    double* M;                     // 存储每次迭代的n值数组，动态分配
    double* M_imag;                // 存储每次迭代的n值的虚部，动态分配
    int* M_is_complex;             // 标记M数组中的元素是否是复数，动态分配
    double* ER;                    // 存储处理后的误差值，复数对应位置设为1000，动态分配
    ControlPoint3_Bridge_arc_arc_2 control_point_3; // 存储控制点数据
    ControlPoint3_Bridge_arc_arc_2 control_point_bridge;  // 添加control_point_bridge
    double** P;                    // 存储最终的P矩阵，动态分配的二维数组
    int P_rows;                    // P矩阵的行数
    int P_cols;                    // P矩阵的列数
    double** X;                    // 存储最终的X矩阵 [K',D1,D2,N,B]，动态分配的二维数组
    int X_rows;                    // X矩阵的行数
    int X_cols;                    // X矩阵的列数
} BridgeResult_Bridge_arc_arc_2;

// 函数声明
Vector3D_Bridge_arc_arc_2 normalize_vector_Bridge_arc_arc_2(Vector3D_Bridge_arc_arc_2 v);
Vector3D_Bridge_arc_arc_2 transform_vector_Bridge_arc_arc_2(const double C[3][3], Vector3D_Bridge_arc_arc_2 v);
double dot_product_Bridge_arc_arc_2(Vector3D_Bridge_arc_arc_2 v1, Vector3D_Bridge_arc_arc_2 v2);
Vector3D_Bridge_arc_arc_2 cross_product_Bridge_arc_arc_2(Vector3D_Bridge_arc_arc_2 v1, Vector3D_Bridge_arc_arc_2 v2);

// 初始化BridgeResult结构体，分配内存
void init_bridge_result_Bridge_arc_arc_2(BridgeResult_Bridge_arc_arc_2* result, int max_iterations);

// 释放BridgeResult结构体中的动态内存
void free_bridge_result_Bridge_arc_arc_2(BridgeResult_Bridge_arc_arc_2* result);

// 创建控制点数组函数
double** create_control_point_array_Bridge_arc_arc_2(const ControlPoint3_Bridge_arc_arc_2* control_point_3);

// 释放控制点数组内存
void free_control_point_array_Bridge_arc_arc_2(double** control_point_array);

BridgeResult_Bridge_arc_arc_2 Bridge_arc_arc_2(const double C1[3][3], const double C2[3][3],
    double** arc_1, int arc_1_rows, int arc_1_cols,
    double** arc_2, int arc_2_rows, int arc_2_cols,
    double u1, double u2);
