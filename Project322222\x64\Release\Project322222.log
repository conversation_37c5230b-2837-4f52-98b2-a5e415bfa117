﻿  adaptive_dbscan03.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\adaptive_dbscan03.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  Bridge_arc_arc_1.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“Bridge_arc_arc_1.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(89,27): warning C4031: 第二个形参表比第一个长
  (编译源文件“Bridge_arc_arc_1.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(89,27): warning C4028: 形参 1 与声明不一致
  (编译源文件“Bridge_arc_arc_1.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.c(213,94): warning C4028: 形参 1 与声明不一致
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.c(213,34): warning C4029: 声明的形参表不同于定义
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.c(707,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.c(1418,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.c(406,39): warning C4101: “original_sy1”: 未引用的局部变量
  Bridge_arc_arc_2.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_2.c(694,28): warning C4566: 由通用字符名称“\uFFFD”表示的字符不能在当前代码页(936)中表示出来
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_2.c(694,28): warning C4566: 由通用字符名称“\u06B5”表示的字符不能在当前代码页(936)中表示出来
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_2.c(694,28): warning C4566: 由通用字符名称“\u046D”表示的字符不能在当前代码页(936)中表示出来
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_2.c(405,39): warning C4101: “original_sy1”: 未引用的局部变量
  bsplan_pf_1.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“bsplan_pf_1.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\bsplan_pf_1.c(204,43): warning C4244: “函数”: 从“double”转换到“int”，可能丢失数据
  calculateArcLength.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“calculateArcLength.c”)
  
  cal_curvature.c
  chongfudian02.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\chongfudian02.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  Copy_2_of_Offline_Trajectory_Planning.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Copy_2_of_Offline_Trajectory_Planning.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“Copy_2_of_Offline_Trajectory_Planning.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(89,27): warning C4031: 第二个形参表比第一个长
  (编译源文件“Copy_2_of_Offline_Trajectory_Planning.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(89,27): warning C4028: 形参 1 与声明不一致
  (编译源文件“Copy_2_of_Offline_Trajectory_Planning.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“Copy_2_of_Offline_Trajectory_Planning.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Copy_2_of_Offline_Trajectory_Planning.c(462,44): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Copy_2_of_Offline_Trajectory_Planning.c(585,40): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Copy_2_of_Offline_Trajectory_Planning.c(730,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Copy_2_of_Offline_Trajectory_Planning.c(1891,21): warning C4013: “Velocity_Taylor_1”未定义；假设外部返回 int
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Copy_2_of_Offline_Trajectory_Planning.c(1946,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  cunihe_01.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\cunihe_01.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\cunihe_01.c(60,82): warning C4047: “函数”:“double (*)[3]”与“Point3D *”的间接级别不同
E:\SUNZHUO\WORK\zhuchengxu_1gai2\cunihe_01.c(60,82): warning C4024: “zhunjunyunsanci_2”: 形参和实参 2 的类型不同
E:\SUNZHUO\WORK\zhuchengxu_1gai2\cunihe_01.c(68,63): warning C4047: “函数”:“double (*)[3]”与“Point3D *”的间接级别不同
E:\SUNZHUO\WORK\zhuchengxu_1gai2\cunihe_01.c(68,63): warning C4024: “shujuchuli”: 形参和实参 1 的类型不同
  curv_break.c
  diff_u.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“diff_u.c”)
  
  dongtaixuanqu_controlPoints.c
  findMinIndex.c
  main666.c
  projectPointsOnPlane.c
  QC_Inverse_Kinematics.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\QC_Inverse_Kinematics.c(331,9): warning C4101: “alp_2”: 未引用的局部变量
E:\SUNZHUO\WORK\zhuchengxu_1gai2\QC_Inverse_Kinematics.c(367,9): warning C4101: “alp_3”: 未引用的局部变量
E:\SUNZHUO\WORK\zhuchengxu_1gai2\QC_Inverse_Kinematics.c(330,9): warning C4101: “alp_1”: 未引用的局部变量
E:\SUNZHUO\WORK\zhuchengxu_1gai2\QC_Inverse_Kinematics.c(368,9): warning C4101: “alp_4”: 未引用的局部变量
  Quasi_Bspline_K_1.c
  R3toS3.c
  rotm2eul_new.c
  test_bsplan_pf_1.c
  正在编译...
  test_Nik_to_control.c
  find_nearest_point.c
  fine_fitting.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“fine_fitting.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\fine_fitting.c(81,53): warning C4047: “函数”:“double *”与“double (*)[3]”的间接级别不同
E:\SUNZHUO\WORK\zhuchengxu_1gai2\fine_fitting.c(81,53): warning C4024: “zhunjunyunsanci”: 形参和实参 2 的类型不同
E:\SUNZHUO\WORK\zhuchengxu_1gai2\fine_fitting.c(210,18): warning C4101: “Py_rows”: 未引用的局部变量
E:\SUNZHUO\WORK\zhuchengxu_1gai2\fine_fitting.c(210,27): warning C4101: “Pz_rows”: 未引用的局部变量
  math_utils.c
  matrix.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“matrix.c”)
  
  nihe_qulv.c
  Nik_to_control.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“Nik_to_control.c”)
  
  Quasi_Bspline_K.c
  QUAT.c
  quaternProd.c
  select_points_uniformly.c
  shujuchuli.c
  shujulvbo.c
  shujulvbo01.c
  speed_planning.c
  s_snew.c
  s_timeround.c
  aaaaaaaatest_Copy_2_of_Offline_Trajectory_Planning.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“aaaaaaaatest_Copy_2_of_Offline_Trajectory_Planning.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(89,27): warning C4031: 第二个形参表比第一个长
  (编译源文件“aaaaaaaatest_Copy_2_of_Offline_Trajectory_Planning.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Bridge_arc_arc_1.h(89,27): warning C4028: 形参 1 与声明不一致
  (编译源文件“aaaaaaaatest_Copy_2_of_Offline_Trajectory_Planning.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“aaaaaaaatest_Copy_2_of_Offline_Trajectory_Planning.c”)
  
  test_fine_fitting.c
  test_QUAT.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\test_QUAT.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  正在编译...
  test_select_points.c
  test_shujuchuli.c
  test_zhunjunyunsanci_3.c
  Trans_quatoMatrix_new.c
  Trans_ZYXeulertoquat.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Trans_ZYXeulertoquat.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  U_matrix.c
  velocity_bspline_u.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“velocity_bspline_u.c”)
  
  Velocity_Taylor_1.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Velocity_Taylor_1.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“Velocity_Taylor_1.c”)
  
E:\SUNZHUO\WORK\zhuchengxu_1gai2\Velocity_Taylor_1.c(110,9): warning C4013: “diff_u”未定义；假设外部返回 int
  zhunjunyunsanci.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\zhunjunyunsanci.c(17,51): warning C4028: 形参 2 与声明不一致
E:\SUNZHUO\WORK\zhuchengxu_1gai2\zhunjunyunsanci.c(48,9): warning C4013: “free_uniform_cubic_result”未定义；假设外部返回 int
  zhunjunyunsanci_2.c
  zhunjunyunsanci_3.c
E:\SUNZHUO\WORK\zhuchengxu_1gai2\zhunjunyunsanci_3.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\SUNZHUO\WORK\zhuchengxu_1gai2\matrix.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“zhunjunyunsanci_3.c”)
  
  正在生成代码
  Previous IPDB not found, fall back to full compilation.
  All 126 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  Project322222.vcxproj -> E:\SUNZHUO\WORK\zhuchengxu_1gai2\x64\Release\Project322222.exe
