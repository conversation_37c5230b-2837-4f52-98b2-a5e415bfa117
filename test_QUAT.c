//#define _CRT_SECURE_NO_WARNINGS
//#include <stdio.h>
//#include <stdlib.h>
//#include <string.h>
//#include "QUAT.h"
//
//int main() {
//    // 文件路径
//    char input_file[2000] = "input.txt";
//    char output_file[2000] = "output.txt";
//    
//    // 打开输入文件
//    FILE* file = fopen(input_file, "r");
//    if (!file) {
//        printf("打开文件失败: %s\n", input_file);
//        return 1;
//    }
//    
//    // 获取文件行数
//    int rows = 0;
//    char line[1024];
//    while (fgets(line, sizeof(line), file)) {
//        rows++;
//    }
//    rewind(file);
//    
//    // 分配内存
//    double* data = (double*)malloc(rows * 3 * sizeof(double));
//    if (!data) {
//        printf("内存分配失败\n");
//        fclose(file);
//        return 1;
//    }
//    
//    // 读取数据
//    int i = 0;
//    while (fgets(line, sizeof(line), file) && i < rows) {
//        if (sscanf(line, "%lf %lf %lf", 
//                &data[i * 3], &data[i * 3 + 1], &data[i * 3 + 2]) != 3) {
//            printf("读取第%d行数据失败\n", i + 1);
//            free(data);
//            fclose(file);
//            return 1;
//        }
//        i++;
//    }
//    fclose(file);
//    
//    // 处理数据
//    Vector3_QUAT* result = QUAT(data, rows, 3);
//    
//    // 保存结果
//    FILE* out = fopen(output_file, "w");
//    if (!out) {
//        printf("无法创建输出文件: %s\n", output_file);
//        free(data);
//        free(result);
//        return 1;
//    }
//    
//    for (i = 0; i < rows; i++) {
//        fprintf(out, "%.6f %.6f %.6f\n", 
//                result[i].x, result[i].y, result[i].z);
//    }
//    fclose(out);
//    
//    printf("处理完成，共%d行数据\n", rows);
//    printf("结果已保存到: %s\n", output_file);
//    
//    // 清理
//    free(data);
//    free(result);
//    
//    return 0;
//}
