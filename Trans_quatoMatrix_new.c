#include "Trans_quatoMatrix_new.h"

void Trans_quatoMatrix(double w, double x, double y, double z, RotationMatrix* output) {
    // Calculate rotation matrix elements
    // First row
    output->matrix[0][0] = 1.0 - 2.0 * (y * y + z * z);
    output->matrix[0][1] = 2.0 * (x * y - z * w);
    output->matrix[0][2] = 2.0 * (x * z + y * w);

    // Second row
    output->matrix[1][0] = 2.0 * (x * y + z * w);
    output->matrix[1][1] = 1.0 - 2.0 * (x * x + z * z);
    output->matrix[1][2] = 2.0 * (y * z - x * w);

    // Third row
    output->matrix[2][0] = 2.0 * (x * z - y * w);
    output->matrix[2][1] = 2.0 * (y * z + x * w);
    output->matrix[2][2] = 1.0 - 2.0 * (x * x + y * y);
} 