#include "Velocity_Taylor_1.h"
#include "Velocity_Bspline_u.h"
#include "matrix.h"
#include "calculateArcLength.h"
#include <stdlib.h>
#include <math.h>
#include <stdio.h>

void Velocity_Taylor_1(double V, Matrix* control_point, double nik_u, double end_u,
    Matrix** U, Matrix** x0, Matrix** y0, Matrix** z0) {
    const double T = 0.004;
    double diff_s = V * T;

    // MATLAB: u_0=nik_u:0.001:end_u;
    int u_0_length = (int)((end_u - nik_u) / 0.001) + 1;
    double* u_0 = (double*)malloc(u_0_length * sizeof(double));
    if (u_0 == NULL) {
        fprintf(stderr, "内存分配失败: u_0\n");
        return;
    }

    for (int i = 0; i < u_0_length; i++) {
        u_0[i] = nik_u + i * 0.001;
    }

    // MATLAB: [~,~,~,x0,y0,z0]=Velocity_Bspline_u(control_point,u_0);
    Matrix* initial_x0 = NULL, * initial_y0 = NULL, * initial_z0 = NULL;
    Matrix* initial_Nikx = NULL, * initial_Niky = NULL, * initial_Nikz = NULL;
    Velocity_Bspline_u(control_point, u_0, u_0_length, &initial_Nikx, &initial_Niky, &initial_Nikz, &initial_x0, &initial_y0, &initial_z0);

    // 检查Velocity_Bspline_u返回的指针
    if (initial_x0 == NULL || initial_y0 == NULL || initial_z0 == NULL) {
        fprintf(stderr, "Velocity_Bspline_u返回了NULL指针\n");
        free(u_0);
        if (initial_Nikx) free_matrix(initial_Nikx);
        if (initial_Niky) free_matrix(initial_Niky);
        if (initial_Nikz) free_matrix(initial_Nikz);
        if (initial_x0) free_matrix(initial_x0);
        if (initial_y0) free_matrix(initial_y0);
        if (initial_z0) free_matrix(initial_z0);
        return;
    }

    // MATLAB: POS=[x0,y0,z0]'; [arcLength,~] = calculateArcLength(POS);
    Point3D01* points = (Point3D01*)malloc(u_0_length * sizeof(Point3D01));
    if (points == NULL) {
        fprintf(stderr, "内存分配失败: points\n");
        free(u_0);
        free_matrix(initial_x0);
        free_matrix(initial_y0);
        free_matrix(initial_z0);
        free_matrix(initial_Nikx);
        free_matrix(initial_Niky);
        free_matrix(initial_Nikz);
        return;
    }

    for (int i = 0; i < u_0_length; i++) {
        points[i].x = get_matrix_element(initial_x0, i, 0);
        points[i].y = get_matrix_element(initial_y0, i, 0);
        points[i].z = get_matrix_element(initial_z0, i, 0);
    }
    ArcLengthResult arc_result = calculateArcLength3D(points, u_0_length);

    free(points);
    free(u_0);
    free_matrix(initial_x0);
    free_matrix(initial_y0);
    free_matrix(initial_z0);
    free_matrix(initial_Nikx);
    free_matrix(initial_Niky);
    free_matrix(initial_Nikz);

    // �ϸ���MATLAB����ʵ������״̬����
    // MATLAB: u=[]; u0=nik_u; z=nik_u;
    double u0 = nik_u;
    double z = nik_u;
    int num_segments = control_point->rows - 3;

    int max_points = 10000;
    double* u = (double*)malloc(max_points * sizeof(double));
    if (u == NULL) {
        fprintf(stderr, "内存分配失败: u\n");
        freeArcLengthResult(&arc_result);
        return;
    }

    int index = 0;

    // MATLAB: while z < end_u
    while (z < end_u) {
        // MATLAB: u=[u,z];
        u[index++] = z;

        // MATLAB: num_now = ceil(nik_u);
        int num_now = (int)ceil(nik_u);
        if (num_now == 0) {
            num_now = 1;
        }

        // MATLAB: z=nik_u;
        z = nik_u;

        // MATLAB: nik_u=nik_u-num_now+1;
        nik_u = nik_u - num_now + 1;

        // MATLAB: [r_d,r_dd]=diff_u(control_point,nik_u,num,num_now);
        Matrix* r_d = NULL;
        Matrix* r_dd = NULL;
        diff_u(control_point, nik_u, num_segments, num_now, &r_d, &r_dd);

        // ���diff_u���ص�ָ��
        if (r_d == NULL || r_dd == NULL) {
            fprintf(stderr, "diff_u������NULLָ��\n");
            free(u);
            freeArcLengthResult(&arc_result);
            return;
        }

        // MATLAB: r_prime=r_d';
        // �����r_prime��MATLAB����r_d��ת��(������)
        // r_d��C���Ѿ������������������ǲ���Ҫת�ã�ֱ�Ӽ��㷶��

        // MATLAB: norm(r_prime)
        double norm_r_prime = 0.0;
        for (int i = 0; i < r_d->cols; i++) {
            norm_r_prime += get_matrix_element(r_d, 0, i) * get_matrix_element(r_d, 0, i);
        }
        norm_r_prime = sqrt(norm_r_prime);

        // MATLAB: r_double_prime=r_dd';
        // MATLAB: r_double_prime' * r_prime (���)
        double dot_product = 0.0;
        for (int i = 0; i < r_d->cols; i++) {
            dot_product += get_matrix_element(r_dd, 0, i) * get_matrix_element(r_d, 0, i);
        }

        // MATLAB: z = z + diff_s/norm(r_prime) - (r_double_prime' * r_prime) * diff_s^2 / (2 * norm(r_prime)^4);
        double term1 = diff_s / norm_r_prime;
        double term2 = (dot_product * diff_s * diff_s) / (2 * pow(norm_r_prime, 4));
        double new_z = z + term1 - term2;

        // ����zΪ��ֵ
        z = new_z;

        // MATLAB: if z>end_u break; end
        if (z > end_u) {
            break;
        }

        // MATLAB: nik_u=z;
        nik_u = z;

        // �ͷ���Դ
        free_matrix(r_d);
        free_matrix(r_dd);

        // ��ֹ�ڴ����
        if (index >= max_points - 1) {
            break;
        }
    }

    // After the while loop, create U matrix from u array
    *U = create_matrix(1, index);
    for (int i = 0; i < index; i++) {
        set_matrix_element(*U, 0, i, u[i]);
    }

    // MATLAB: uu=(((u-u(1))/(u(end)-u(1))))*(end_u-u0)+u0;
    double* uu = (double*)malloc(index * sizeof(double));
    if (uu == NULL) {
        fprintf(stderr, "内存分配失败: uu\n");
        free(u);
        freeArcLengthResult(&arc_result);
        return;
    }

    double u_first = u[0];
    double u_last = u[index - 1];

    for (int i = 0; i < index; i++) {
        uu[i] = (((u[i] - u_first) / (u_last - u_first)) * (end_u - u0)) + u0;
    }

    // MATLAB: [~,~,~,p_u_x,p_u_y,p_u_z]=Velocity_Bspline_u(control_point,uu);
    Matrix* p_u_x = NULL, * p_u_y = NULL, * p_u_z = NULL;
    Matrix* Nikx = NULL, * Niky = NULL, * Nikz = NULL;
    Velocity_Bspline_u(control_point, uu, index, &Nikx, &Niky, &Nikz, &p_u_x, &p_u_y, &p_u_z);

    // Set output matrices
    *x0 = p_u_x;
    *y0 = p_u_y;
    *z0 = p_u_z;

    // Clean up
    free(uu);
    free(u);
    if (Nikx) free_matrix(Nikx);
    if (Niky) free_matrix(Niky);
    if (Nikz) free_matrix(Nikz);
    freeArcLengthResult(&arc_result);
}
