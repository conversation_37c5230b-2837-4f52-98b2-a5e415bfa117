#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include "adaptive_dbscan03.h"
#include "chongfudian02.h"
#include <string.h>
#include <stdint.h>
#include <stdlib.h>
// ��������֮���ŷ����þ���
static double euclidean_distance(double* point1, double* point2, int cols) {
    double sum = 0.0;
    for (int i = 0; i < cols; i++) {
        double diff = point1[i] - point2[i];
        sum += diff * diff;
    }
    return sqrt(sum);
}

// ����������
static double** calculate_distance_matrix(double** points, int rows, int cols) {
    double** D = (double**)malloc(rows * sizeof(double*));
    if (D == NULL) {
        free(D);
        return NULL;
    }
    for (int i = 0; i < rows; i++) {
        D[i] = (double*)malloc(rows * sizeof(double));
        if (D[i] == NULL) {
            free(D[i]);
            return NULL;
        }
    }
    for (int i = 0; i < rows; i++) {
        for (int j = 0; j < rows; j++) {
            D[i][j] = euclidean_distance(points[i], points[j], cols);
        }
    }
    return D;
}
//****************************************************************
//// �������������
//static void sort_array(double* arr, int size) {
//    for (int i = 0; i < size - 1; i++) {
//        for (int j = i + 1; j < size; j++) {
//            if (arr[i] > arr[j]) {
//                double temp = arr[i];
//                arr[i] = arr[j];
//                arr[j] = temp;
//            }
//        }
//    }
//}


//�޸�����
// �ȽϺ���
static int compare_double(const void* a, const void* b) {
    double diff = (*(double*)a) - (*(double*)b);
    return (diff > 0) - (diff < 0); // ����1��0��-1
}

// ������������򣨸�Ч�棩
static void sort_array(double* arr, int size) {
    qsort(arr, size, sizeof(double), compare_double);
}
 
 
 
 

//// �������� - �Ը��������Ч
//static void radix_sort_double(double* arr, int size) {
//    uint64_t* int_arr = (uint64_t*)arr;
//    uint64_t* temp = malloc(size * sizeof(uint64_t));
//
//    // ������������ת����λ
//    for (int i = 0; i < size; i++) {
//        if (int_arr[i] & 0x8000000000000000ULL) {
//            int_arr[i] ^= 0xFFFFFFFFFFFFFFFFULL;
//        }
//        else {
//            int_arr[i] ^= 0x8000000000000000ULL;
//        }
//    }
//
//    // 8�ֻ�������ÿ�δ���8λ
//    for (int shift = 0; shift < 64; shift += 8) {
//        int count[256] = { 0 };
//
//        // ����
//        for (int i = 0; i < size; i++) {
//            count[(int_arr[i] >> shift) & 0xFF]++;
//        }
//
//        // �ۻ�����
//        for (int i = 1; i < 256; i++) {
//            count[i] += count[i - 1];
//        }
//
//        // ����
//        for (int i = size - 1; i >= 0; i--) {
//            int digit = (int_arr[i] >> shift) & 0xFF;
//            temp[--count[digit]] = int_arr[i];
//        }
//
//        memcpy(int_arr, temp, size * sizeof(uint64_t));
//    }
//
//    free(temp);
//}
//
//// �������С�����ò������򣬴������û�������
//static void hybrid_sort(double* arr, int size) {
//    if (size < 32) {
//        // ��������
//        for (int i = 1; i < size; i++) {
//            double key = arr[i];
//            int j = i - 1;
//            while (j >= 0 && arr[j] > key) {
//                arr[j + 1] = arr[j];
//                j--;
//            }
//            arr[j + 1] = key;
//        }
//    }
//    else {
//        radix_sort_double(arr, size);
//    }
//}
//
//// ����������
//static void sort_array(double* arr, int size) {
//    hybrid_sort(arr, size);
//}


//****************************************************************
//// DBSCAN �㷨
//static int* dbscan(double** points, int rows, int cols, double epsilon, int minPts) {
//    int* labels = (int*)calloc(rows, sizeof(int));
//    if (labels == NULL) {
//        free(labels);
//        return NULL;
//    }
//    int cluster_id = 0;
//    for (int i = 0; i < rows; i++) {
//        if (labels[i] != 0) continue;
//        int* neighbors = (int*)malloc(rows * sizeof(int));
//        if (neighbors == NULL) {
//            free(neighbors);
//            return NULL;
//        }
//        int num_neighbors = 0;
//        for (int j = 0; j < rows; j++) {
//            if (euclidean_distance(points[i], points[j], cols) <= epsilon) {
//                neighbors[num_neighbors++] = j;
//            }
//        }
//        if (num_neighbors < minPts) {
//            labels[i] = -1; // ������
//            free(neighbors);
//            continue;
//        }
//        cluster_id++;
//        labels[i] = cluster_id;
//        for (int k = 0; k < num_neighbors; k++) {
//            int neighbor = neighbors[k];
//            if (labels[neighbor] == -1) {
//                labels[neighbor] = cluster_id;
//            }
//            if (labels[neighbor] != 0) continue;
//            labels[neighbor] = cluster_id;
//            int* new_neighbors = (int*)malloc(rows * sizeof(int));
//            if (new_neighbors == NULL) {
//                free(new_neighbors);
//                return NULL;
//            }
//            int new_num_neighbors = 0;
//            for (int j = 0; j < rows; j++) {
//                if (euclidean_distance(points[neighbor], points[j], cols) <= epsilon) {
//                    new_neighbors[new_num_neighbors++] = j;
//                }
//            }
//            if (new_num_neighbors >= minPts && new_neighbors != 0) {
//                memcpy(neighbors + num_neighbors, new_neighbors, new_num_neighbors * sizeof(int));
//                num_neighbors += new_num_neighbors;
//            }
//            free(new_neighbors);
//        }
//        free(neighbors);
//    }
//    return labels;
//}
//
//// ����Ӧ DBSCAN �㷨
//double** adaptive_dbscan03(double** Filterpoints, int rows, int cols, int* newRows) {
//    int minPts = 4;
//    int k = 4;
//
//    double** old_points = (double**)malloc(rows * sizeof(double*));
//    for (int i = 0; i < rows; i++) {
//        old_points[i] = (double*)malloc(3 * sizeof(double));
//        for (int j = 0; j < 3; j++) {
//            old_points[i][j] = Filterpoints[i][j];
//        }
//    }
//
//    double** D = calculate_distance_matrix(old_points, rows, 3);
//    double* epsilons = (double*)malloc(rows * sizeof(double));
//    if (epsilons == NULL) {
//        free(epsilons);
//        return NULL;
//    }
//    for (int i = 0; i < rows; i++) {
//        sort_array(D[i], rows);
//        epsilons[i] = D[i][k];
//    }
//    freeDoubleMatrix(D, rows);
//    double* diff_distances = (double*)malloc((rows - 1) * sizeof(double));
//    if (diff_distances == NULL) {
//        free(diff_distances);
//        return NULL;
//    }
//    for (int i = 0; i < rows - 1; i++) {
//        diff_distances[i] = fabs(epsilons[i + 1] - epsilons[i]);
//    }
//
//    int idx = 0;
//    double max_diff = diff_distances[0];
//    for (int i = 1; i < rows - 1; i++) {
//        if (diff_distances[i] > max_diff) {
//            max_diff = diff_distances[i];
//            idx = i;
//        }
//    }
//    free(diff_distances);
//    double epsilon = 0.05 * epsilons[idx];
//    free(epsilons);
//    int* labels = dbscan(old_points, rows, 3, epsilon, minPts);
//
//    int num = 0;
//    for (int i = 0; i < rows; i++) {
//        if (labels[i] > num) {
//            num = labels[i];
//        }
//    }
//
//    if (num <= 0) {
//        double** newpoints = (double**)malloc(rows * sizeof(double*));
//        if (newpoints == NULL) {
//            free(newpoints);
//            return NULL;
//        }
//        for (int i = 0; i < rows; i++) {
//            newpoints[i] = (double*)malloc(cols * sizeof(double));
//            if (newpoints[i] == NULL) {
//                free(newpoints);
//                return NULL;
//            }
//            for (int j = 0; j < cols; j++) {
//                newpoints[i][j] = Filterpoints[i][j];
//            }
//        }
//        free(labels);
//        freeDoubleMatrix(old_points, rows); // ֻ�ͷ�һ��
//        return newpoints;
//    }
//
//    for (int i = 1; i <= num; i++) {
//        int count = 0;
//        double* sum = (double*)calloc(cols, sizeof(double));
//        if (sum == NULL) {
//            free(sum);
//            return NULL;
//        }
//
//        for (int j = 0; j < rows; j++) {
//            if (labels[j] == i) {
//                for (int k = 0; k < cols; k++) {
//                    sum[k] += Filterpoints[j][k];
//                }
//                count++;
//            }
//        }
//        for (int j = 0; j < cols; j++) {
//            sum[j] /= count;
//        }
//        for (int j = 0; j < rows; j++) {
//            if (labels[j] == i) {
//                for (int k = 0; k < cols; k++) {
//                    Filterpoints[j][k] = sum[k];
//                }
//            }
//        }
//        free(sum);
//    }
//
//    double** new_Filterpoints = (double**)malloc((rows + 2) * sizeof(double*));
//    if (new_Filterpoints == NULL) {
//        free(new_Filterpoints);
//        return NULL;
//    }
//    new_Filterpoints[0] = (double*)malloc(cols * sizeof(double));
//    if (new_Filterpoints[0] == NULL) {
//        free(new_Filterpoints[0]);
//        return NULL;
//    }
//    for (int j = 0; j < cols; j++) {
//        new_Filterpoints[0][j] = Filterpoints[0][j];
//    }
//    for (int i = 0; i < rows; i++) {
//        new_Filterpoints[i + 1] = (double*)malloc(cols * sizeof(double));
//        if (new_Filterpoints[i + 1] == NULL) {
//            free(new_Filterpoints[i + 1]);
//            return NULL;
//        }
//        for (int j = 0; j < cols; j++) {
//            new_Filterpoints[i + 1][j] = Filterpoints[i][j];
//        }
//    }
//    new_Filterpoints[rows + 1] = (double*)malloc(cols * sizeof(double));
//    if (new_Filterpoints[rows + 1] == NULL) {
//        free(new_Filterpoints[rows + 1]);
//        return NULL;
//    }
//    for (int j = 0; j < cols; j++) {
//        new_Filterpoints[rows + 1][j] = Filterpoints[rows - 1][j];
//    }
//
//    double** newPoints = chongfudian02(new_Filterpoints, rows + 2, cols, newRows); // ����Ϊ &newRows
//    free(labels);
//    freeDoubleMatrix(old_points, rows); // ֻ�ͷ�һ��
//    freeDoubleMatrix(new_Filterpoints, rows + 2);
//
//    return newPoints;
//}

double** adaptive_dbscan03(double** Filterpoints, int rows, int cols, int* newRows, double threshold) {
    printf("抖动点过滤: %d个点\n", rows);
    
    if (rows <= 1) {
        *newRows = rows;
        return Filterpoints;
    }
    
    bool* keep = (bool*)malloc(rows * sizeof(bool));
    keep[0] = true; // 第一个点必须保留
    
    //double threshold = 0.00000001; // 最小距离阈值，根据你的数据调整
    
    for (int i = 1; i < rows; i++) {
        // 计算与前一个点的3D距离
        double dx = Filterpoints[i][0] - Filterpoints[i-1][0];
        double dy = Filterpoints[i][1] - Filterpoints[i-1][1]; 
        double dz = Filterpoints[i][2] - Filterpoints[i-1][2];
        double dist = sqrt(dx*dx + dy*dy + dz*dz);
        
        keep[i] = (dist > threshold);
    }
    
    // 强制保留最后一个点
    keep[rows-1] = true;
    
    // 计算保留点数
    int count = 0;
    for (int i = 0; i < rows; i++) {
        if (keep[i]) count++;
    }
    
    // 复制保留的点
    double** result = (double**)malloc(count * sizeof(double*));
    int idx = 0;
    for (int i = 0; i < rows; i++) {
        if (keep[i]) {
            result[idx] = (double*)malloc(cols * sizeof(double));
            for (int j = 0; j < cols; j++) {
                result[idx][j] = Filterpoints[i][j];
            }
            idx++;
        }
    }
    
    printf("过滤结果: %d -> %d 个点\n", rows, count);
    free(keep);
    *newRows = count;
    return result;
} // �ͷŶ�ά�����ڴ�
void freeDoubleMatrix(double** matrix, int rows) {
    for (int i = 0; i < rows; i++) {
        free(matrix[i]);
    }
    free(matrix);
}
