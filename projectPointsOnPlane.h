#pragma once
#ifndef PROJECT_POINTS_ON_PLANE_H
#define PROJECT_POINTS_ON_PLANE_H

void crossProduct(const double v1[3], const double v2[3], double result[3]);
double dotProduct(const double v1[3], const double v2[3]);
double vectorNorm(const double v[3]);
void normalizeVector(double v[3]);
void projectPointsOnPlane(const double vector1[3], const double vector2[3], const double point_on_plane[3], const double points[][3], int num_points, double projected_points[][3]);

// New version that works with dynamically allocated arrays
void projectPointsOnPlane_dynamic(const double vector1[3], const double vector2[3],
    const double point_on_plane[3],
    const double points[][3], int num_points,
    double** projected_points);

#endif