#ifndef CUNIHE_01_H
#define CUNIHE_01_H

#include "dongtaixuanqu_controlPoints.h"
#include "zhunjunyunsanci_2.h"
#include "shujuchuli.h" 
#include "nihe_qulv.h"  
#include "cal_curvature.h"  

// Define a new structure to hold both ControlPoints and ShujuResult
typedef struct {
    ControlPoints* controlPoints;  // Control points data
    double* K;                     // Curvature values from shujuchuli
    int K_length;                  // Length of the K array
} CuniheResult;

// Update function signature to return the new structure
CuniheResult* cunihe_01(const double* data, int rows, int cols);

// Function to free the memory used by CuniheResult
void free_cunihe_result(CuniheResult* result);

#endif