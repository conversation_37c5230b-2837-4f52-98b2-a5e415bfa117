//#include <stdio.h>
//#include <stdlib.h>
//#include "matrix.h"
//#include "zhunjunyunsanci_3.h"
//
//int main() {
//    // Create input matrix with 7 control points (7x3 matrix)
//    Matrix* control_points = create_matrix(7, 3);
//    
//    // Define 7 control points
//    // Point 1
//    set_matrix_element(control_points, 0, 0, 0.0);   // x
//    set_matrix_element(control_points, 0, 1, 0.0);   // y
//    set_matrix_element(control_points, 0, 2, 0.0);   // z
//    
//    // Point 2
//    set_matrix_element(control_points, 1, 0, 1.0);
//    set_matrix_element(control_points, 1, 1, 0.5);
//    set_matrix_element(control_points, 1, 2, 0.0);
//    
//    // Point 3
//    set_matrix_element(control_points, 2, 0, 2.0);
//    set_matrix_element(control_points, 2, 1, 1.0);
//    set_matrix_element(control_points, 2, 2, 0.0);
//    
//    // Point 4
//    set_matrix_element(control_points, 3, 0, 3.0);
//    set_matrix_element(control_points, 3, 1, 0.0);
//    set_matrix_element(control_points, 3, 2, 0.5);
//    
//    // Point 5
//    set_matrix_element(control_points, 4, 0, 4.0);
//    set_matrix_element(control_points, 4, 1, -1.0);
//    set_matrix_element(control_points, 4, 2, 1.0);
//    
//    // Point 6
//    set_matrix_element(control_points, 5, 0, 5.0);
//    set_matrix_element(control_points, 5, 1, -0.5);
//    set_matrix_element(control_points, 5, 2, 0.5);
//    
//    // Point 7
//    set_matrix_element(control_points, 6, 0, 6.0);
//    set_matrix_element(control_points, 6, 1, 0.0);
//    set_matrix_element(control_points, 6, 2, 0.0);
//    
//    // Print input control points
//    printf("Input control points (7x3 matrix):\n");
//    for (int i = 0; i < control_points->rows; i++) {
//        printf("Point %d: (%.2f, %.2f, %.2f)\n", 
//               i+1, 
//               get_matrix_element(control_points, i, 0),
//               get_matrix_element(control_points, i, 1),
//               get_matrix_element(control_points, i, 2));
//    }
//    printf("\n");
//    
//    // Call zhunjunyunsanci_3 to generate curve points
//    Matrix* p_u_x = NULL;
//    Matrix* p_u_y = NULL;
//    Matrix* p_u_z = NULL;
//    zhunjunyunsanci_3(7, control_points, &p_u_x, &p_u_y, &p_u_z);
//    
//    // Check results
//    if (p_u_x == NULL || p_u_y == NULL || p_u_z == NULL) {
//        printf("Error: Function did not return valid curve points\n");
//        if (control_points) free_matrix(control_points);
//        return 1;
//    }
//    
//    printf("Generated curve with %d points\n", p_u_x->rows);
//    printf("Sampling points along the curve:\n");
//    
//    // Print a subset of points to verify the curve
//    int num_to_print = p_u_x->rows ;
//    int step = p_u_x->rows / num_to_print;
//    if (step < 1) step = 1;
//    
//    for (int i = 0; i < p_u_x->rows; i += step) {
//        printf("Point %3d: (%.4f, %.4f, %.4f)\n", 
//               i, 
//               get_matrix_element(p_u_x, i, 0),
//               get_matrix_element(p_u_y, i, 0),
//               get_matrix_element(p_u_z, i, 0));
//    }
//    
//    // Print the last point
//    if (p_u_x->rows > 0) {
//        printf("Last Point: (%.4f, %.4f, %.4f)\n", 
//               get_matrix_element(p_u_x, p_u_x->rows - 1, 0),
//               get_matrix_element(p_u_y, p_u_y->rows - 1, 0),
//               get_matrix_element(p_u_z, p_u_z->rows - 1, 0));
//    }
//    
//    // Also save the curve points to a file for visualization
//    FILE* file = fopen("curve_points.csv", "w");
//    if (file) {
//        fprintf(file, "x,y,z\n");
//        for (int i = 0; i < p_u_x->rows; i++) {
//            fprintf(file, "%.6f,%.6f,%.6f\n", 
//                   get_matrix_element(p_u_x, i, 0),
//                   get_matrix_element(p_u_y, i, 0),
//                   get_matrix_element(p_u_z, i, 0));
//        }
//        fclose(file);
//        printf("\nCurve points saved to 'curve_points.csv'\n");
//    }
//    
//    // Clean up
//    free_matrix(control_points);
//    free_matrix(p_u_x);
//    free_matrix(p_u_y);
//    free_matrix(p_u_z);
//    
//    return 0;
//} 