#pragma once
#pragma once
#ifndef BRIDGE_ARC_H
#define BRIDGE_ARC_H

#include <math.h>
#include <stdlib.h>

typedef struct {
    double x;
    double y;
    double z;
} Vector3D_Bridge_arc_arc_1;

typedef struct {
    double real_x;
    double real_y;
    double real_z;
    double imag_x;
    double imag_y;
    double imag_z;
} ComplexVector3D_Bridge_arc_arc_1;

typedef struct {
    Vector3D_Bridge_arc_arc_1 P0;
    Vector3D_Bridge_arc_arc_1 P1;
    Vector3D_Bridge_arc_arc_1 P2;
    Vector3D_Bridge_arc_arc_1 P3;
    Vector3D_Bridge_arc_arc_1 P4;
} ControlPoint3_Bridge_arc_arc_1;

typedef struct {
    double K1;
    double K2;
    double U1;
    double U2;
    Vector3D_Bridge_arc_arc_1 D1;
    Vector3D_Bridge_arc_arc_1 D2;
    Vector3D_Bridge_arc_arc_1 N1;
    Vector3D_Bridge_arc_arc_1 N2;
    Vector3D_Bridge_arc_arc_1 B1;
    Vector3D_Bridge_arc_arc_1 B2;
    Vector3D_Bridge_arc_arc_1 P0;
    Vector3D_Bridge_arc_arc_1 P4;
    Vector3D_Bridge_arc_arc_1 De;
    Vector3D_Bridge_arc_arc_1 Ds;
    Vector3D_Bridge_arc_arc_1 Pq;
    Vector3D_Bridge_arc_arc_1 P1;
    Vector3D_Bridge_arc_arc_1 P2;
    Vector3D_Bridge_arc_arc_1 P3;
    Vector3D_Bridge_arc_arc_1 P22;
    Vector3D_Bridge_arc_arc_1 Pc;
    Vector3D_Bridge_arc_arc_1 sy1;
    Vector3D_Bridge_arc_arc_1 sy2;
    Vector3D_Bridge_arc_arc_1 P1P2;  // projected_points(3,:)-projected_points(2,:)
    Vector3D_Bridge_arc_arc_1 P3P2;  // projected_points(4,:)-projected_points(3,:)
    Vector3D_Bridge_arc_arc_1 P4P0;  // projected_points(5,:)-projected_points(1,:)
    ComplexVector3D_Bridge_arc_arc_1 P3_complex;
    ComplexVector3D_Bridge_arc_arc_1 Pc_complex;
    ComplexVector3D_Bridge_arc_arc_1 P1_complex;
    int is_complex;                
    double m;
    double n;
    double error;                 
    double* error_values;         
    int num_iterations;           
    int max_iterations;           
    double** projected_points;    
    int num_projected_points;     
    double A;                     
    double B;                     
    double C;                     
    double D;                     
    double* M;                    
    double* M_imag;               
    int* M_is_complex;            
    double* ER;                   
    ControlPoint3_Bridge_arc_arc_1 control_point_3; 
    ControlPoint3_Bridge_arc_arc_1 control_point_bridge;  
    double** P;                    // 输出: 合并后的P数组，(P_rows x 3)，需由free_bridge_result_Bridge_arc_arc_1释放
    int P_rows;                    // P的行数
    int P_cols;                    // P的列数
    double** X;                    // 输出: 合并后的X数组，(X_rows x 13)，需由free_bridge_result_Bridge_arc_arc_1释放
    int X_rows;                    // X的行数
    int X_cols;                    // X的列数
} BridgeResult_Bridge_arc_arc_1;

Vector3D_Bridge_arc_arc_1 normalize_vector_Bridge_arc_arc_1(Vector3D_Bridge_arc_arc_1 v);
Vector3D_Bridge_arc_arc_1 normalize_vector_Bridge_arc_arc_1(const double C[3][3], Vector3D_Bridge_arc_arc_1 v);
double dot_product_Bridge_arc_arc_1(Vector3D_Bridge_arc_arc_1 v1, Vector3D_Bridge_arc_arc_1 v2);
Vector3D_Bridge_arc_arc_1 cross_product_Bridge_arc_arc_1(Vector3D_Bridge_arc_arc_1 v1, Vector3D_Bridge_arc_arc_1 v2);

void init_bridge_result_Bridge_arc_arc_1(BridgeResult_Bridge_arc_arc_1* result, int max_iterations);

void free_bridge_result_Bridge_arc_arc_1(BridgeResult_Bridge_arc_arc_1* result);

double** create_control_point_array_Bridge_arc_arc_1(const ControlPoint3_Bridge_arc_arc_1* control_point_3);

void free_control_point_array_Bridge_arc_arc_1(double** control_point_array);

BridgeResult_Bridge_arc_arc_1 Bridge_arc_arc_1(const double C1[3][3], const double C2[3][3],
    double** arc_1, int arc_1_rows, int arc_1_cols,
    double** arc_2, int arc_2_rows, int arc_2_cols,
    double u1, double u2);

#endif // BRIDGE_ARC_H 