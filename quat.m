function TT=QUAT(orient)
% q_z11=[cos(O1(1)/2),0,0,1*sin(O1(1)/2)]; % z1轴：[0,0,1] 绕x轴旋转alpha 
% q_y11=[cos(O1(2)/2),0,1*sin(O1(2)/2),0];   % y轴：[0,1,0] 绕y轴旋转beita
% q_z12=[cos(O1(3)/2),0,0,1*sin(O1(3)/2)];     % z轴：[0,0,1] 绕z轴旋转gama 
% 
% q_z21=[cos(O2(1)/2),0,0,1*sin(O2(1)/2)]; % z1轴：[0,0,1] 绕x轴旋转alpha 
% q_y21=[cos(O2(2)/2),0,1*sin(O2(2)/2),0];   % y轴：[0,1,0] 绕y轴旋转beita
% q_z22=[cos(O2(3)/2),0,0,1*sin(O2(3)/2)];     % z轴：[0,0,1] 绕z轴旋转gama 
% 
% q_z31=[cos(O3(1)/2),0,0,1*sin(O3(1)/2)]; % z1轴：[0,0,1] 绕x轴旋转alpha 
% q_y31=[cos(O3(2)/2),0,1*sin(O3(2)/2),0];   % y轴：[0,1,0] 绕y轴旋转beita
% q_z32=[cos(O3(3)/2),0,0,1*sin(O3(3)/2)];     % z轴：[0,0,1] 绕z轴旋转gama 
% 
% %求各姿态的四元数
% qzyz1= quaternProd(q_z11,quaternProd(q_y11, q_z12));  %右乘顺序来
% qzyz2= quaternProd(q_z21,quaternProd(q_y21, q_z22));  %左乘顺序来
% qzyz3= quaternProd(q_z31,quaternProd(q_y31, q_z32));  %左乘顺序来
for i=1:size(orient,1)
    A=orient(i,:)*pi/180;
%     quat(:,i) = Trans_eulertoquat(A)';
    quat(:,i) = Trans_ZYXeulertoquat(A)';
    
end
n_orient=size(quat,2);

qzyz=zeros(4,n_orient);
P=zeros(3,n_orient);
theta1=zeros(1,n_orient);
n1=zeros(3,n_orient);

qzyz=quat;



for num=0:1: n_orient-1
    theta1(1,num+1)=2*acos(qzyz(1,num+1));
    n1(1,num+1)=qzyz(2,num+1)/sin(theta1(1,num+1)/2);
    n1(2,num+1)=qzyz(3,num+1)/sin(theta1(1,num+1)/2);
    n1(3,num+1)=qzyz(4,num+1)/sin(theta1(1,num+1)/2);
    P(1,num+1)= theta1(1,num+1)*n1(1,num+1);
    P(2,num+1)= theta1(1,num+1)*n1(2,num+1);
    P(3,num+1)= theta1(1,num+1)*n1(3,num+1);
end
TT=P';
end