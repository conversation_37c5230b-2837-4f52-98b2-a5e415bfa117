#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <math.h>
#include "chongfudian02.h"

// 比较时设置一个小的误差范围，用于判断是否相等
#define EPSILON 1e-9

// 判断两行是否相等
bool isRowEqual(double* row1, double* row2, int cols) {
    for (int i = 0; i < cols; i++) {
        if (fabs(row1[i] - row2[i]) > EPSILON) {
            return false;
        }
    }
    return true;
}

// 查找重复点并返回唯一行
double** chongfudian02(double** data, int rows, int cols, int* uniqueRows) {
    if (rows <= 1) {
        *uniqueRows = rows;
        return data;
    }
    
    bool* keep = (bool*)malloc(rows * sizeof(bool));
    keep[0] = true;  // 第一个点总是保留
    
    int keepCount = 1;
    
    // 只与前一个点比较，不做全局比较
    for (int i = 1; i < rows; i++) {
        bool equal = isRowEqual(data[i], data[i-1], cols);
        
        // 调试输出
        if (equal) {
            printf("发现重复点 %d:\n", i);
            printf("  前一点: ");
            for (int j = 0; j < cols; j++) {
                printf("%.10f ", data[i-1][j]);
            }
            printf("\n  当前点: ");
            for (int j = 0; j < cols; j++) {
                printf("%.10f ", data[i][j]);
            }
            printf("\n");
        }
        
        if (!equal) {
            keep[i] = true;
            keepCount++;
        } else {
            keep[i] = false;
        }
    }
    
    // 复制保留的点
    double** result = (double**)malloc(keepCount * sizeof(double*));
    int idx = 0;
    for (int i = 0; i < rows; i++) {
        if (keep[i]) {
            result[idx] = (double*)malloc(cols * sizeof(double));
            for (int j = 0; j < cols; j++) {
                result[idx][j] = data[i][j];
            }
            idx++;
        }
    }
    
    free(keep);
    *uniqueRows = keepCount;
    return result;
}

// 释放唯一行的内存
void freeUniquePoints(double** uniquePoints, int uniqueRows) {
    if (uniquePoints == NULL) return;
    for (int i = 0; i < uniqueRows; i++) {
        free(uniquePoints[i]);
        uniquePoints[i] = NULL;
    }
    free(uniquePoints);
    uniquePoints = NULL;
}
