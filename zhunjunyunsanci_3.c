#include "zhunjunyunsanci_3.h"

void zhunjunyunsanci_3(int n, Matrix* s_x_y_z, Matrix** p_u_x_1, Matrix** p_u_y_1, Matrix** p_u_z_1) {
    Matrix* M3, * M4, * M5, * M5_n_2, * M_n_1, * M_n;
    Matrix* Nikx = NULL, * Niky = NULL, * Nikz = NULL;
    int num_segments = 0;

    if (n == 4) {
        M3 = create_matrix(4, 4);
        double M3_data[4][4] = {
            {1.0, 0.0, 0.0, 0.0},
            {-3.0, 3.0, 0.0, 0.0},
            {3.0, -6.0, 3.0, 0.0},
            {-1.0, 3.0, -3.0, 1.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M3, i, j, M3_data[i][j]);
            }
        }
        Nikx = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 0, 0));
        <PERSON>y = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 1, 1));
        Nikz = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 2, 2));

        free_matrix(M3);
        num_segments = 1;
    }
    else if (n == 5) {
        M3 = create_matrix(4, 4);
        double M3_data[4][4] = {
            {1.0, 0.0, 0.0, 0.0},
            {-3.0, 3.0, 0.0, 0.0},
            {3.0, -9.0 / 2.0, 3.0 / 2.0, 0},
            {-1.0, 7.0 / 4.0, -1.0, 1.0 / 4.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M3, i, j, M3_data[i][j]);
            }
        }
        M4 = create_matrix(4, 4);
        double M4_data[4][4] = {
            {1.0 / 4.0, 1.0 / 2.0, 1.0 / 4.0, 0},
            {-3.0 / 4.0, 0, 3.0 / 4.0, 0},
            {3.0 / 4.0, -3.0 / 2.0, 3.0 / 4.0, 0},
            {-1.0 / 4.0, 1.0, -7.0 / 4.0, 1.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M4, i, j, M4_data[i][j]);
            }
        }
        Nikx = create_matrix(4, 2);
        Niky = create_matrix(4, 2);
        Nikz = create_matrix(4, 2);
        Matrix* temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, 0, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 1, 4, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, 1, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        // 同理处理 Niky 和 Nikz
        temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, 0, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 1, 4, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, 1, get_matrix_element(temp, i, 0));
        }
        temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, 0, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 1, 4, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, 1, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        free_matrix(M3);
        free_matrix(M4);
        num_segments = 2;
    }
    else if (n == 6) {
        M3 = create_matrix(4, 4);
        double M3_data[4][4] = {
            {1.0, 0.0, 0.0, 0.0},
            {-3.0, 3.0, 0.0, 0.0},
            {3.0, -9.0 / 2.0, 3.0 / 2.0, 0.0},
            {-1.0, 7.0 / 4.0, -11.0 / 12, 1.0 / 6.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M3, i, j, M3_data[i][j]);
            }
        }
        M4 = create_matrix(4, 4);
        double M4_data[4][4] = {
            {1.0 / 4.0, 7.0 / 12.0, 1.0 / 6.0, 0},
            {-3.0 / 4.0, 1.0 / 4.0, 1.0 / 2.0, 0},
            {3.0 / 4.0, -5.0 / 4.0, 1.0 / 2.0, 0},
            {-1.0 / 4.0, 7.0 / 12.0, -7.0 / 12.0, 1.0 / 4.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M4, i, j, M4_data[i][j]);
            }
        }
        M5 = create_matrix(4, 4);
        double M5_data[4][4] = {
            {1.0 / 6.0, 7.0 / 12.0, 1.0 / 4.0, 0.0},
            {-1.0 / 2.0, -1.0 / 4.0, 3.0 / 4.0, 0.0},
            {1.0 / 2.0, -5.0 / 4.0, 3.0 / 4.0, 0.0},
            {-1.0 / 6.0, 11.0 / 12.0, -7.0 / 4.0, 1.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M5, i, j, M5_data[i][j]);
            }
        }
        Nikx = create_matrix(4, 3);
        Niky = create_matrix(4, 3);
        Nikz = create_matrix(4, 3);
        // 计算 Nikx
        Matrix* temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, 0, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 1, 4, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, 1, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M5, sub_matrix(s_x_y_z, 2, 5, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, 2, get_matrix_element(temp, i, 0));
        }
        // 计算Niky
        free_matrix(temp);
        temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 2, 5, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, 2, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 2, 5, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, 2, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M5, sub_matrix(s_x_y_z, 2, 5, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, 2, get_matrix_element(temp, i, 0));
        }
        // 计算Nikz
        free_matrix(temp);
        temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 2, 5, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, 2, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 2, 5, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, 2, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M5, sub_matrix(s_x_y_z, 2, 5, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, 2, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        free_matrix(M3);
        free_matrix(M4);
        free_matrix(M5);
        num_segments = 3;
    }
    else if (n >= 7) {
        M3 = create_matrix(4, 4);
        double M3_data[4][4] = {
            {1.0, 0.0, 0.0, 0.0},
            {-3.0, 3.0, 0.0, 0.0},
            {3.0, -9.0 / 2.0, 3.0 / 2.0, 0.0},
            {-1.0, 7.0 / 4.0, -11.0 / 12.0, 1.0 / 6.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M3, i, j, M3_data[i][j]);
            }
        }
        M4 = create_matrix(4, 4);
        double M4_data[4][4] = {
            {1.0 / 4.0, 7.0 / 12.0, 1.0 / 6.0, 0.0},
            {-3.0 / 4.0, 1.0 / 4.0, 1.0 / 2.0, 0.0},
            {3.0 / 4.0, -5.0 / 4.0, 1.0 / 2.0, 0.0},
            {-1.0 / 4.0, 7.0 / 12.0, -1.0 / 2.0, 1.0 / 6.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M4, i, j, M4_data[i][j]);
            }
        }
        M5_n_2 = create_matrix(4, 4);
        double M5_n_2_data[4][4] = {
            {1.0 / 6.0, 2.0 / 3.0, 1.0 / 6.0, 0.0},
            {-1.0 / 2.0, 0, 1.0 / 2.0, 0.0},
            {1.0 / 2.0, -1.0, 1.0 / 2.0, 0.0},
            {-1.0 / 6.0, 1.0 / 2.0, -1.0 / 2.0, 1.0 / 6.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M5_n_2, i, j, M5_n_2_data[i][j]);
            }
        }
        M_n_1 = create_matrix(4, 4);
        double M_n_1_data[4][4] = {
            {1.0 / 6.0, 2.0 / 3.0, 1.0 / 6.0, 0.0},
            {-1.0 / 2.0, 0.0, 1.0 / 2.0, 0.0},
            {1.0 / 2.0, -1.0, 1.0 / 2.0, 0.0},
            {-1.0 / 6.0, 1.0 / 2.0, -7.0 / 12.0, 1.0 / 4.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M_n_1, i, j, M_n_1_data[i][j]);
            }
        }
        M_n = create_matrix(4, 4);
        double M_n_data[4][4] = {
            {1.0 / 6.0, 7.0 / 12.0, 1.0 / 4.0, 0.0},
            {-1.0 / 2.0, -1.0 / 4.0, 3.0 / 4.0, 0.0},
            {1.0 / 2.0, -5.0 / 4.0, 3.0 / 4.0, 0.0},
            {-1.0 / 6.0, 11.0 / 12.0, -7.0 / 4.0, 1.0}
        };
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                set_matrix_element(M_n, i, j, M_n_data[i][j]);
            }
        }
        Nikx = create_matrix(4, n - 3);
        Niky = create_matrix(4, n - 3);
        Nikz = create_matrix(4, n - 3);
        // 计算 Nikx, Niky, Nikz
        Matrix* temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, 0, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, 0, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M3, sub_matrix(s_x_y_z, 0, 3, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, 0, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        // 第二组
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 1, 4, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, 1, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 1, 4, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, 1, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M4, sub_matrix(s_x_y_z, 1, 4, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, 1, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        // 计算中间组
        for (int h = 2; h < n - 5; h++) {
            temp = matrix_multiply(M5_n_2, sub_matrix(s_x_y_z, h, h + 3, 0, 0));
            for (int i = 0; i < 4; i++) {
                set_matrix_element(Nikx, i, h, get_matrix_element(temp, i, 0));
            }
            free_matrix(temp);
            temp = matrix_multiply(M5_n_2, sub_matrix(s_x_y_z, h, h + 3, 1, 1));
            for (int i = 0; i < 4; i++) {
                set_matrix_element(Niky, i, h, get_matrix_element(temp, i, 0));
            }
            free_matrix(temp);
            temp = matrix_multiply(M5_n_2, sub_matrix(s_x_y_z, h, h + 3, 2, 2));
            for (int i = 0; i < 4; i++) {
                set_matrix_element(Nikz, i, h, get_matrix_element(temp, i, 0));
            }
            free_matrix(temp);
        }
        // 倒数第二组
        temp = matrix_multiply(M_n_1, sub_matrix(s_x_y_z, n - 5, n - 2, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, n - 5, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M_n_1, sub_matrix(s_x_y_z, n - 5, n - 2, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, n - 5, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M_n_1, sub_matrix(s_x_y_z, n - 5, n - 2, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, n - 5, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        // 倒数第一组
        temp = matrix_multiply(M_n, sub_matrix(s_x_y_z, n - 4, n - 1, 0, 0));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikx, i, n - 4, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M_n, sub_matrix(s_x_y_z, n - 4, n - 1, 1, 1));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Niky, i, n - 4, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        temp = matrix_multiply(M_n, sub_matrix(s_x_y_z, n - 4, n - 1, 2, 2));
        for (int i = 0; i < 4; i++) {
            set_matrix_element(Nikz, i, n - 4, get_matrix_element(temp, i, 0));
        }
        free_matrix(temp);
        free_matrix(M3);
        free_matrix(M4);
        free_matrix(M5_n_2);
        free_matrix(M_n_1);
        free_matrix(M_n);
        num_segments = n - 3;
    }

    int steps_per_segment = (int)(1 / 0.05) + 1;
    int num_points = (int)(1 / 0.05 + 1) * num_segments;
    *p_u_x_1 = create_matrix(num_points, 1);
    *p_u_y_1 = create_matrix(num_points, 1);
    *p_u_z_1 = create_matrix(num_points, 1);
    int l = 0;
    for (int m = 0; m < num_segments; m++) {
        for (int step = 0; step < steps_per_segment; step++) {
            double u = step * 0.05;
            Matrix* u_vector = create_matrix(1, 4);
            set_matrix_element(u_vector, 0, 0, 1);
            set_matrix_element(u_vector, 0, 1, u);
            set_matrix_element(u_vector, 0, 2, u * u);
            set_matrix_element(u_vector, 0, 3, u * u * u);
            Matrix* temp_x = matrix_multiply(u_vector, sub_matrix(Nikx, 0, 3, m, m));
            Matrix* temp_y = matrix_multiply(u_vector, sub_matrix(Niky, 0, 3, m, m));
            Matrix* temp_z = matrix_multiply(u_vector, sub_matrix(Nikz, 0, 3, m, m));
            set_matrix_element(*p_u_x_1, l, 0, get_matrix_element(temp_x, 0, 0));
            set_matrix_element(*p_u_y_1, l, 0, get_matrix_element(temp_y, 0, 0));
            set_matrix_element(*p_u_z_1, l, 0, get_matrix_element(temp_z, 0, 0));
            free_matrix(u_vector);
            free_matrix(temp_x);
            free_matrix(temp_y);
            free_matrix(temp_z);
            l++;
        }
    }
    free_matrix(Nikx);
    free_matrix(Niky);
    free_matrix(Nikz);
}