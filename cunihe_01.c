#include <stdio.h>
#include <stdlib.h>
#include <string.h>  // For memcpy
#include "cunihe_01.h"
#include "dongtaixuanqu_controlPoints.h"
#include "zhunjunyunsanci_2.h"
#include "cal_curvature.h"  

//  - ӦMATLABcunihe_01
CuniheResult* cunihe_01(const double* data, int rows, int cols) {
    // Ч
    if (data == NULL || rows <= 0 || cols < 3) {
        fprintf(stderr, "Invalid input data\n");
        return NULL;
    }

    // Create result structure
    CuniheResult* result = (CuniheResult*)malloc(sizeof(CuniheResult));
    if (result == NULL) {
        return NULL;
    }
    
    // Initialize with NULL values to prevent issues when freeing in case of error
    result->controlPoints = NULL;
    result->K = NULL;
    result->K_length = 0;

    // ȡx,y,z
    double* x = (double*)malloc(rows * sizeof(double));
    double* y = (double*)malloc(rows * sizeof(double));
    double* z = (double*)malloc(rows * sizeof(double));

    if (x == NULL || y == NULL || z == NULL) {
        free(x); free(y); free(z);
        free(result);
        return NULL;
    }

    for (int i = 0; i < rows; i++) {
        x[i] = data[i * cols + 0];  // һ
        y[i] = data[i * cols + 1];  // ڶ
        z[i] = data[i * cols + 2];  // 
    }

    // ö̬ѡȡƵ㺯
    result->controlPoints = dongtaixuanqu_controlPoints(x, y, z, rows);
    if (result->controlPoints == NULL) {
        free(x); free(y); free(z);
        free(result);
        return NULL;
    }

    // ͷʱڴ
    free(x); free(y); free(z);

    // ȡƵ
    int n = result->controlPoints->size;

    // ׼B
    UniformCubicResult bSplineResult = zhunjunyunsanci_2(n, result->controlPoints->points);

    // ȡ
    double* p_u_x = bSplineResult.p_u_x;
    double* p_u_y = bSplineResult.p_u_y;
    double* p_u_z = bSplineResult.p_u_z;

    // ݴ
    ShujuResult shujuResult = shujuchuli(result->controlPoints->points, n, result->controlPoints->size);
    
    // Copy K values to our result structure
    if (shujuResult.K != NULL && shujuResult.length > 0) {
        result->K_length = shujuResult.length;
        result->K = (double*)malloc(result->K_length * sizeof(double));
        if (result->K != NULL) {
            memcpy(result->K, shujuResult.K, result->K_length * sizeof(double));
        } else {
            // Memory allocation failed, clean up and return NULL
            free_uniform_cubic_result(&bSplineResult);
            free_shuju_result(shujuResult);
            freeControlPoints(result->controlPoints);
            free(result);
            return NULL;
        }
    }

    // Free resources we no longer need
    free_uniform_cubic_result(&bSplineResult);
    free_shuju_result(shujuResult);

    return result;
}

// Function to free memory used by CuniheResult
void free_cunihe_result(CuniheResult* result) {
    if (result == NULL) return;
    
    // Free control points if allocated
    if (result->controlPoints != NULL) {
        // Use the proper function to free ControlPoints
        freeControlPoints(result->controlPoints);
        // No need to free result->controlPoints here since freeControlPoints does that
    }
    
    // Free K values if allocated
    if (result->K != NULL) {
        free(result->K);
    }
    
    // Free the result structure itself
    free(result);
}