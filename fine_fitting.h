#pragma once
#ifndef FINE_FITTING_H
#define FINE_FITTING_H

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "matrix.h"
#include "math_utils.h"
#include "zhunjunyunsanci_2.h"
#include "zhunjunyunsanci_3.h"
#include "shujuchuli.h"
#include "Quasi_Bspline_K.h"
#include "zhunjunyunsanci.h"
#ifdef __cplusplus
extern "C" {
#endif

	// Helper function to convert Matrix* to double[][3] for zhunjunyunsanci_2
	void convert_matrix_to_array(Matrix* mat, double array[][3]);

	// Wrapper function for zhunjunyunsanci_2 that accepts Matrix* arguments
	void zhunjunyunsanci_2_wrapper(int n, Matrix* P, Matrix** Nikx, Matrix** Niky, Matrix** Nikz, Matrix** p_u_x, Matrix** p_u_y, Matrix** p_u_z);

	// Helper function for shujuchu<PERSON> that accepts Matrix* arguments
	void shujuchuli_wrapper(Matrix* P, int rows, <PERSON>** K, Matrix** D1, Matrix** D2, <PERSON>** N, Matrix** Bt);

	void zhunjunyunsanci_wrapper(int n, Matrix* P, Matrix** Nikx, Matrix** Niky, Matrix** Nikz, Matrix** p_u_x, Matrix** p_u_y, Matrix** p_u_z);








	void fine_fitting(Matrix* arc, Matrix** pos, Matrix** Nikx, Matrix** Niky, Matrix** Nikz, Matrix** X,int diedai);


#ifdef __cplusplus
}
#endif

#endif // FINE_FITTING_H