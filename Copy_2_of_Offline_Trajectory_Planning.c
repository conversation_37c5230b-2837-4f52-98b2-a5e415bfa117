#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include <string.h>
#include "Copy_2_of_Offline_Trajectory_Planning.h"
#include "select_points_uniformly.h"
#include "calculateArcLength.h"
#include "cunihe_01.h"
#include "zhunjunyunsanci_2.h"
#include "nihe_qulv.h"
#include "shujulvbo.h"
#include "curv_break.h"
#include "matrix.h"
#include "QUAT.h"
#include "fine_fitting.h"
#include "Nik_to_control.h"
#include "bsplan_pf_1.h"
#include "Velocity_Bspline_u.h"
#include "Bridge_arc_arc_1.h"
#include "Bridge_arc_arc_2.h"
#include "R3toS3.h"
#include "Trans_quatoMatrix_new.h"
#include "rotm2eul_new.h"  // Add this include for EulerAngles definition

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif
// 保存 position_inter 到 txt 文件
void save_position_inter_to_txt(const double* position_inter, int size, int num_cols, const char* filename) {
    FILE* fp = fopen(filename, "w");
    if (!fp) {
        printf("无法打开文件 %s 进行写入！\n", filename);
        return;
    }
    int num_rows = size / num_cols;
    for (int i = 0; i < num_rows; i++) {
        for (int j = 0; j < num_cols; j++) {
            fprintf(fp, "%.6f", position_inter[i * num_cols + j]);
            if (j < num_cols - 1)
                fprintf(fp, "\t");
        }
        fprintf(fp, "\n");
    }
    fclose(fp);
    printf("position_inter 已保存到 %s\n", filename);
}
// Helper function to remove a row from a matrix
static void remove_row(Matrix* matrix, int row_index) {
    if (!matrix || row_index < 0 || row_index >= matrix->rows) {
        return; // Invalid parameters
    }

    // Move all rows after the deleted row up by one position
    for (int i = row_index; i < matrix->rows - 1; i++) {
        for (int j = 0; j < matrix->cols; j++) {
            // Copy the element from the next row to the current row
            set_matrix_element(matrix, i, j, get_matrix_element(matrix, i + 1, j));
        }
    }
    matrix->rows--;
}
static UniformCubicResult call_zhunjunyunsanci_2(int n, double (*points)[3]) {
    return zhunjunyunsanci_2(n, points);
}
static double* calculateArcLength(const double* orient, int rows) {
    Point3D01* points = (Point3D01*)malloc(rows * sizeof(Point3D01));
    for (int i = 0; i < rows; i++) {
        points[i].x = orient[i * 3];     // First Euler angle
        points[i].y = orient[i * 3 + 1]; // Second Euler angle
        points[i].z = orient[i * 3 + 2]; // Third Euler angle
    }

    ArcLengthResult result = calculateArcLength3D(points, rows);
    double* arclength = (double*)malloc(rows * sizeof(double));
    memcpy(arclength, result.arclength, rows * sizeof(double));
    free(points);
    freeArcLengthResult(&result);
    return arclength;
}

void free_control_points_matrix(ControlPointsMatrix* matrix) {
    if (matrix) {
        if (matrix->P1_CX) free(matrix->P1_CX);
        if (matrix->P1_CY) free(matrix->P1_CY);
        if (matrix->P1_CZ) free(matrix->P1_CZ);
        if (matrix->P1_oriX) free(matrix->P1_oriX);
        if (matrix->P1_oriY) free(matrix->P1_oriY);
        if (matrix->P1_oriZ) free(matrix->P1_oriZ);
        matrix->n = 0;
    }
}

int Copy_2_of_Offline_Trajectory_Planning(
    const TrajectoryPlanningInput* input,
    TrajectoryPlanningOutput* output) {
    output->fitting_points = NULL;
    output->rows = 0;
    output->cols = 0;
    output->K = NULL;
    output->K_size = 0;
    output->K_max = NULL;
    output->K_max_size = 0;
    output->position_inter = NULL;
    output->position_inter_size = 0;
    output->V0 = NULL;
    output->V1 = NULL;
    output->V_size = 0;
    output->V_B = NULL;
    output->V_B_size = 0;
    output->P_bridge_con = init_3d(0, 0);
    output->P_bridge_X = init_3d(0, 0);
    output->indixi_trunc = init_3d(0, 0);
    output->P_bridge_con_ori = init_3d(0, 0);
    output->P_bridge = NULL;
    output->P_bridge_ori = NULL;
    output->POS = NULL;
    output->POS_ori = NULL;
    output->fit_over_pos = NULL;
    output->fit_over_pos_Nikx = NULL;
    output->fit_over_pos_Niky = NULL;
    output->fit_over_pos_Nikz = NULL;
    output->fit_over_pos_X = NULL;
    output->fit_over_ori = NULL;
    output->fit_over_ori_Nikx = NULL;
    output->fit_over_ori_Niky = NULL;
    output->fit_over_ori_Nikz = NULL;
    output->fit_over_ori_X = NULL;
    output->U_ori = NULL;
    output->ORI_con = NULL;
    output->Z_inter = NULL;
    output->Z_inter_size = 0;
    output->P_bridge = NULL;
    output->P_bridge_con = NULL;
    output->P_bridge_X = NULL;
    output->indixi_trunc = NULL;
    output->P_bridge_ori = NULL;
    output->transfer_error = 0.0;
    output->quotient_floor = 0.0;
    output->end_u1 = 0.0;
    output->nik_u1 = 0.0;
    output->T1 = NULL;
    output->h = NULL;
    output->Theta = NULL;
    output->RRR = NULL;
    output->RRR_size = 0;
    output->theta_trajectory = NULL;

    double** data_2d = (double**)malloc(input->rows * sizeof(double*));
    for (int i = 0; i < input->rows; i++) {
        data_2d[i] = (double*)malloc(input->cols * sizeof(double));
        for (int j = 0; j < input->cols; j++) {
            data_2d[i][j] = input->raw_data[i * input->cols + j];
        }
    }
    int newRows = 0;
    double** newMatrix = shujulvbo(data_2d, input->rows, input->cols, &newRows,input->threshold_lvbo,input->threshold_dy);
    //// 打印newMatrix
    //printf("\n--- 打印newMatrix ---\n");
    //printf("newMatrix大小: %d 行 x %d 列\n", newRows, input->cols);
    //if (newRows > 0 && newRows < 20) {
    //    for (int i = 0; i < newRows; i++) {
    //        printf("Row %3d: ", i);
    //        for (int j = 0; j < input->cols; j++) {
    //            printf("%9.4f ", newMatrix[i][j]);
    //        }
    //        printf("\n");
    //    }
    //}
    //else if (newRows >= 20) {
    //    printf("显示前10行和后10行:\n");
    //    for (int i = 0; i < 10; i++) {
    //        printf("Row %3d: ", i);
    //        for (int j = 0; j < input->cols; j++) {
    //            printf("%9.4f ", newMatrix[i][j]);
    //        }
    //        printf("\n");
    //    }
    //    printf("...\n");
    //    for (int i = newRows - 10; i < newRows; i++) {
    //        printf("Row %3d: ", i);
    //        for (int j = 0; j < input->cols; j++) {
    //            printf("%9.4f ", newMatrix[i][j]);
    //        }
    //        printf("\n");
    //    }
    //}
    //printf("--- newMatrix打印结束 ---\n\n");

    for (int i = 0; i < input->rows; i++) {
        free(data_2d[i]);
    }
    free(data_2d);
    if (!newMatrix || newRows <= 0) {
        if (newMatrix) {
            free(newMatrix);
        }
        newRows = input->rows;
        newMatrix = (double**)malloc(newRows * sizeof(double*));
        for (int i = 0; i < newRows; i++) {
            newMatrix[i] = (double*)malloc(input->cols * sizeof(double));
            for (int j = 0; j < input->cols; j++) {
                newMatrix[i][j] = input->raw_data[i * input->cols + j];
            }
        }
    }


  /*  if (newRows > 0 && newRows < 100) {
        for (int i = 0; i < newRows; i++) {
            printf("Row %3d: ", i);
            for (int j = 0; j < input->cols; j++) {
                printf("%9.4f ", newMatrix[i][j]);
            }
            printf("\n");
        }
    }
    else {
        printf("Too many rows to display. Showing first 5 and last 5 rows:\n");
        int display_rows = (newRows < 5) ? newRows : 5;
        for (int i = 0; i < display_rows; i++) {
            printf("Row %3d: ", i);
            for (int j = 0; j < input->cols; j++) {
                printf("%9.4f ", newMatrix[i][j]);
            }
            printf("\n");
        }

        if (newRows > 10) {
            printf("...\n");
            for (int i = newRows - 5; i < newRows; i++) {
                printf("Row %3d: ", i);
                for (int j = 0; j < input->cols; j++) {
                    printf("%9.4f ", newMatrix[i][j]);
                }
                printf("\n");
            }
        }
    }*/

    // Extract position and orientation data from filtered data
    double* pos = (double*)malloc(newRows * 3 * sizeof(double));
    double* orient = (double*)malloc(newRows * 3 * sizeof(double));
    for (int i = 0; i < newRows; i++) {
        pos[i * 3] = newMatrix[i][0];     // x
        pos[i * 3 + 1] = newMatrix[i][1]; // y
        pos[i * 3 + 2] = newMatrix[i][2]; // z

        orient[i * 3] = newMatrix[i][3]; // First Euler angle
        orient[i * 3 + 1] = newMatrix[i][4]; // Second Euler angle
        orient[i * 3 + 2] = newMatrix[i][5]; // Third Euler angle
    }
    for (int i = 0; i < newRows; i++) {
        free(newMatrix[i]);
    }
    free(newMatrix);
    for (int i = 1; i < newRows; i++) {
        if (orient[i * 3] - orient[(i - 1) * 3] >= 180) {
            for (int j = i; j < newRows; j++) {
                orient[j * 3] -= 360;
            }
        }
        else if (orient[i * 3] - orient[(i - 1) * 3] <= -180) {
            for (int j = i; j < newRows; j++) {
                orient[j * 3] += 360;
            }
        }
        if (orient[i * 3 + 1] - orient[(i - 1) * 3 + 1] >= 180) {
            for (int j = i; j < newRows; j++) {
                orient[j * 3 + 1] -= 360;
            }
        }
        else if (orient[i * 3 + 1] - orient[(i - 1) * 3 + 1] <= -180) {
            for (int j = i; j < newRows; j++) {
                orient[j * 3 + 1] += 360;
            }
        }
        if (orient[i * 3 + 2] - orient[(i - 1) * 3 + 2] >= 180) {
            for (int j = i; j < newRows; j++) {
                orient[j * 3 + 2] -= 360;
            }
        }
        else if (orient[i * 3 + 2] - orient[(i - 1) * 3 + 2] <= -180) {
            for (int j = i; j < newRows; j++) {
                orient[j * 3 + 2] += 360;
            }
        }
    }

    double* orient_0 = (double*)malloc(newRows * 3 * sizeof(double));

    memcpy(orient_0, orient, newRows * 3 * sizeof(double));
    double* arclength = calculateArcLength(orient_0, newRows);
    double* ori_diff = (double*)malloc((newRows - 1) * sizeof(double));
    for (int i = 0; i < newRows - 1; i++) {
        ori_diff[i] = arclength[i + 1] - arclength[i];
    }
    ////打印ori_diff
    //for (int i = 0; i < newRows - 1; i++) {
    //    printf("ori_diff[%d] = %.3f\n", i, ori_diff[i]);
    //}
    int interval_length = (int)floor(input->ori_smooth_param / 0.05);
    int* max_index = (int*)malloc(newRows * sizeof(int));
    int max_count = 0;
    for (int i = 0; i < newRows - 1; i++) {
        if (ori_diff[i] > 10) {
            max_index[max_count++] = i;
        }
    }
    if (max_count > 0) {
        for (int i = 0; i < max_count; i++) {
            int start_idx, end_idx;
            if (max_index[i] < interval_length) {
                start_idx = max_index[i];
                end_idx = max_index[i] + interval_length;
            }
            else if (max_index[i] + interval_length > newRows - 1) {
                start_idx = max_index[i] - interval_length;
                end_idx = max_index[i];
            }
            else {
                start_idx = max_index[i] - interval_length;
                end_idx = max_index[i] + interval_length;
            }
            int data_size = end_idx - start_idx + 1;
            double* data_ori = (double*)malloc(data_size * 3 * sizeof(double));

            for (int j = 0; j < data_size; j++) {
                data_ori[j * 3] = orient[(start_idx + j) * 3];
                data_ori[j * 3 + 1] = orient[(start_idx + j) * 3 + 1];
                data_ori[j * 3 + 2] = orient[(start_idx + j) * 3 + 2];
            }

            CuniheResult* ori_result = cunihe_01(data_ori, data_size, 3);

            ControlPoints* ori_cp = ori_result->controlPoints;
            if (ori_result->K != NULL && ori_result->K_length > 0) {

               /* for (int j = 0; j < ori_result->K_length; j++) {
                    printf("K[%3d] = %.3f", j, ori_result->K[j]);
                    if ((j + 1) % 4 == 0) {
                        printf("\n");
                    }
                    else {
                        printf("  |  ");
                    }
                }
                if (ori_result->K_length % 4 != 0) {
                    printf("\n");
                }*/

                double min_k = ori_result->K[0];
                double max_k = ori_result->K[0];
                double sum_k = ori_result->K[0];
                for (int j = 1; j < ori_result->K_length; j++) {
                    if (ori_result->K[j] < min_k) min_k = ori_result->K[j];
                    if (ori_result->K[j] > max_k) max_k = ori_result->K[j];
                    sum_k += ori_result->K[j];
                }
                double avg_k = sum_k / ori_result->K_length;
            }
            Matrix_select_points_uniformly input_matrix;
            input_matrix.data = (double*)malloc(ori_cp->size * 3 * sizeof(double));
            input_matrix.rows = ori_cp->size;
            input_matrix.cols = 3;
            for (int j = 0; j < ori_cp->size; j++) {
                input_matrix.data[j * 3] = ori_cp->points[j].x;
                input_matrix.data[j * 3 + 1] = ori_cp->points[j].y;
                input_matrix.data[j * 3 + 2] = ori_cp->points[j].z;
            }
            Matrix_select_points_uniformly* selected_points = select_points_uniformly(&input_matrix, data_size);

            // Check if selected_points is valid and has data
            if (selected_points && selected_points->data) {
                // Use the actual number of points returned, not the requested data_size
                int actual_points = selected_points->rows;
                int points_to_copy = (actual_points < data_size) ? actual_points : data_size;

                // Copy the available points
                for (int j = 0; j < points_to_copy; j++) {
                    // Ensure we don't exceed the orient array bounds
                    if (start_idx + j < newRows) {
                        orient[(start_idx + j) * 3] = selected_points->data[j * 3];
                        orient[(start_idx + j) * 3 + 1] = selected_points->data[j * 3 + 1];
                        orient[(start_idx + j) * 3 + 2] = selected_points->data[j * 3 + 2];
                    }
                }

                // If we have fewer points than expected, fill the remaining with the last available point
                if (points_to_copy < data_size && points_to_copy > 0) {
                    for (int j = points_to_copy; j < data_size; j++) {
                        if (start_idx + j < newRows) {
                            // Use the last available point
                            int last_idx = points_to_copy - 1;
                            orient[(start_idx + j) * 3] = selected_points->data[last_idx * 3];
                            orient[(start_idx + j) * 3 + 1] = selected_points->data[last_idx * 3 + 1];
                            orient[(start_idx + j) * 3 + 2] = selected_points->data[last_idx * 3 + 2];
                        }
                    }
                }
            }

            // Clean up
            free(data_ori);
            // Use the proper free function for CuniheResult
            free_cunihe_result(ori_result);
            free(input_matrix.data);
            // Change from free_matrix to free_matrix_select_points_uniformly
            if (selected_points) {
                free_matrix_select_points_uniformly(selected_points);
            }
        }
    }

    // Now do path fitting for position data
    CuniheResult* pos_result = cunihe_01(pos, newRows, 3);

    // Access controlPoints from the result structure
    ControlPoints* pos_cp = pos_result->controlPoints;

    // Instead of just using the control points, we need to generate the full curve
    // Convert control points to a format suitable for zhunjunyunsanci_2
    double (*control_points)[3] = (double(*)[3])malloc(pos_cp->size * sizeof(double[3]));
    for (int i = 0; i < pos_cp->size; i++) {
        control_points[i][0] = pos_cp->points[i].x;
        control_points[i][1] = pos_cp->points[i].y;
        control_points[i][2] = pos_cp->points[i].z;
    }

    // Generate the full curve using B-spline
    UniformCubicResult bSplineResult = call_zhunjunyunsanci_2(pos_cp->size, control_points);

    // Use K values from pos_result if needed
    // For example, to get curvature values:
    if (pos_result->K != NULL && pos_result->K_length > 0) {
        // Store curvature values in the K field
        output->K_size = pos_result->K_length;
        output->K = (double*)malloc(pos_result->K_length * sizeof(double));

        if (!output->K) {
            fprintf(stderr, "Memory allocation failed for curvature values\n");
        }
        else {
            // Copy the curvature values
            memcpy(output->K, pos_result->K, pos_result->K_length * sizeof(double));

            // Calculate the threshold as in MATLAB: yuzhi = threshold * 100
            double threshold = input->threshold * 100;

            // Initialize curvature break storage
            CurvBreak* cb = curv_break_init();
            if (!cb) {
                fprintf(stderr, "Failed to initialize curvature break detection\n");
            }
            else {
                // Detect curvature breaks (equivalent to MATLAB's detect_curvature_breaks function)
                detect_curvature_breaks(output->K, output->K_size, threshold, cb);

                // Store the results in the output
                if (cb->count > 0) {
                    // Allocate memory for K_max
                    output->K_max = (double*)malloc(cb->count * 2 * sizeof(double));
                    output->K_max_size = cb->count;

                    for (size_t i = 0; i < cb->count; i++) {
                        output->K_max[i * 2] = (double)cb->locs[i];         // locs
                        output->K_max[i * 2 + 1] = cb->peaks[i];           // pks
                    }

                }
                else {
                    output->K_max = NULL;
                    output->K_max_size = 0;
                }

                // Free the curvature break storage
                curv_break_free(cb);
            }
            //for (int i = 0; i < output->K_size; i++) {
            //    printf("K[%3d] = %.3f", i, output->K[i]);

            //    // Format output to have 4 values per line
            //    if ((i + 1) % 4 == 0) {
            //        printf("\n");
            //    }
            //    else {
            //        printf("  |  ");
            //    }
            //}
            //// End with a newline if not already at the start of a line
            //if (output->K_size % 4 != 0) {
            //    printf("\n");
            //}

            // Calculate and print statistics
            if (output->K_size > 0) {
                double min_k = output->K[0];
                double max_k = output->K[0];
                double sum_k = output->K[0];

                for (int i = 1; i < output->K_size; i++) {
                    if (output->K[i] < min_k) min_k = output->K[i];
                    if (output->K[i] > max_k) max_k = output->K[i];
                    sum_k += output->K[i];
                }

                double avg_k = sum_k / output->K_size;
            }
        }
    }

    // Prepare the output result with the full curve points
    output->rows = bSplineResult.rows;
    output->cols = 3;
    output->fitting_points = (double*)malloc(bSplineResult.rows * 3 * sizeof(double));

    // Copy the curve points to the output structure
    for (int i = 0; i < bSplineResult.rows; i++) {
        output->fitting_points[i * 3] = bSplineResult.p_u_x[i];
        output->fitting_points[i * 3 + 1] = bSplineResult.p_u_y[i];
        output->fitting_points[i * 3 + 2] = bSplineResult.p_u_z[i];
    }
    // Initialize the jizhi array
    output->jizhi_size = 0;
    output->jizhi = NULL;

    if (output->K_max == NULL || output->K_max_size == 0) {
        // No curvature maxima, just use start and end points
        output->jizhi_size = 2;
        output->jizhi = (int*)malloc(2 * sizeof(int));
        if (!output->jizhi) {
        }
        else {
            output->jizhi[0] = 0;  // First point (0-indexed in C)
            output->jizhi[1] = newRows - 1;  // Last point (0-indexed in C)
        }
    }
    else {
        // We have curvature maxima, find nearest points in original position data
        int num_maxima = output->K_max_size;
        output->jizhi_size = num_maxima + 2;  // +2 for start and end points
        output->jizhi = (int*)malloc(output->jizhi_size * sizeof(int));

        if (!output->jizhi) {
            fprintf(stderr, "Memory allocation failed for jizhi\n");
        }
        else
        {
            // First point is always included
            output->jizhi[0] = 0;  // First point (0-indexed in C)

            //printf("\n--- Segment Indices (jizhi) ---\n");
            //printf("Finding segment indices for %d curvature maxima:\n", num_maxima);

            // Process each curvature maximum point
            for (int i = 0; i < num_maxima; i++) {
                // Get the index in the fitted curve (K_max(i,1))
                int curve_index = (int)output->K_max[i * 2];

                // Create target point from fitting_points at curve_index
                Point target_point;
                target_point.x = output->fitting_points[curve_index * 3];
                target_point.y = output->fitting_points[curve_index * 3 + 1];
                target_point.z = output->fitting_points[curve_index * 3 + 2];

                // Convert original position data to Point array
                Point* position_points = (Point*)malloc(newRows * sizeof(Point));
                if (!position_points) {
                    fprintf(stderr, "Memory allocation failed for position_points\n");
                    continue;
                }

                // Fill position_points from the filtered data (newMatrix converted to pos earlier)
                for (int j = 0; j < newRows; j++) {
                    position_points[j].x = pos[j * 3];
                    position_points[j].y = pos[j * 3 + 1];
                    position_points[j].z = pos[j * 3 + 2];
                }

                // Find nearest point in original position data
                size_t nearest_index;
                double min_distance;
                find_nearest_point(position_points, newRows, target_point, &nearest_index, &min_distance);

                // Store the index
                output->jizhi[i + 1] = nearest_index;

          /*      printf("Curvature max at fitted curve index %d -> nearest position point at index %zu (distance: %.3f)\n",
                    curve_index, nearest_index, min_distance);*/

                // Clean up
                free(position_points);
            }

            // Last point is always included
            output->jizhi[output->jizhi_size - 1] = newRows - 1;  // Last point (0-indexed in C)

            //// Print the final jizhi array
            //printf("Final jizhi array (1-indexed for output): [");
            //for (int i = 0; i < output->jizhi_size; i++) {
            //    printf("%d", output->jizhi[i] + 1);  // +1 for 1-indexed output
            //    if (i < output->jizhi_size - 1) {
            //        printf(", ");
            //    }
            //}
            //printf("]\n");
            //printf("--- End of Segment Indices ---\n");
        }
    }

    // Initialize position data arrays and cell arrays
    // Equivalent to:
    // POS=[];
    // POS_ori=[];
    // fit_over_pos = cell(1, length(jizhi)-1);
    // fit_over_pos_Nikx = cell(1, length(jizhi)-1);
    // fit_over_pos_Niky = cell(1, length(jizhi)-1);
    // fit_over_pos_Nikz = cell(1, length(jizhi)-1);
    // fit_over_pos_X = cell(1, length(jizhi)-1);

    // Initialize empty matrices for POS and POS_ori
    output->POS = create_matrix(0, 3);  // Empty matrix with 3 columns (x,y,z)
    output->POS_ori = create_matrix(0, 3);  // Empty matrix with 3 columns (x,y,z)

    if (!output->POS || !output->POS_ori) {
        fprintf(stderr, "Memory allocation failed for position matrices\n");
        // Clean up if allocation failed
        if (output->POS) free_matrix(output->POS);
        if (output->POS_ori) free_matrix(output->POS_ori);
        output->POS = NULL;
        output->POS_ori = NULL;
    }
    else {
        printf("\n--- Position Data Arrays ---\n");
        printf("Initialized empty position matrices POS and POS_ori\n");
    }

    // Initialize cell arrays (Matrix3D structures)
    // For MATLAB's cell(1, length(jizhi)-1) equivalent
    int num_segments = output->jizhi_size;

    // Initialize fit_over_pos (cell array for fitted positions)
    output->fit_over_pos = init_3d(0, 0);
    output->fit_over_pos_Nikx = init_3d(0, 0);
    output->fit_over_pos_Niky = init_3d(0, 0);
    output->fit_over_pos_Nikz = init_3d(0, 0);
    output->fit_over_pos_X = init_3d(0, 0);


    if (!output->fit_over_pos || !output->fit_over_pos_Nikx ||
        !output->fit_over_pos_Niky || !output->fit_over_pos_Nikz ||
        !output->fit_over_pos_X) {
        fprintf(stderr, "Memory allocation failed for cell arrays\n");

        // Clean up on failure
        if (output->fit_over_pos) free_3d(output->fit_over_pos);
        if (output->fit_over_pos_Nikx) free_3d(output->fit_over_pos_Nikx);
        if (output->fit_over_pos_Niky) free_3d(output->fit_over_pos_Niky);
        if (output->fit_over_pos_Nikz) free_3d(output->fit_over_pos_Nikz);
        if (output->fit_over_pos_X) free_3d(output->fit_over_pos_X);

        output->fit_over_pos = NULL;
        output->fit_over_pos_Nikx = NULL;
        output->fit_over_pos_Niky = NULL;
        output->fit_over_pos_Nikz = NULL;
        output->fit_over_pos_X = NULL;
    }
    else {
        printf("Successfully initialized %d cell arrays for segments\n", num_segments);
    }
    // Initialize fit_over_ori and related cell arrays for orientation
    output->fit_over_ori = init_3d(0, 0);
    output->fit_over_ori_Nikx = init_3d(0, 0);
    output->fit_over_ori_Niky = init_3d(0, 0);
    output->fit_over_ori_Nikz = init_3d(0, 0);
    output->fit_over_ori_X = init_3d(0, 0);

    // Recover position and orientation data from filtered matrix
    double* pos_data = (double*)malloc(newRows * 3 * sizeof(double));
    double* orient_data = (double*)malloc(newRows * 3 * sizeof(double));

    // Reconstruct position and orientation arrays
    for (int i = 0; i < newRows; i++) {
        // Position data (first 3 columns)
        pos_data[i * 3] = input->raw_data[i * input->cols];
        pos_data[i * 3 + 1] = input->raw_data[i * input->cols + 1];
        pos_data[i * 3 + 2] = input->raw_data[i * input->cols + 2];

        // Orientation data (next 3 columns) - using preprocessed orient values instead of raw data
        orient_data[i * 3] = orient[i * 3];        // Use preprocessed orientation values
        orient_data[i * 3 + 1] = orient[i * 3 + 1]; // that went through continuity processing
        orient_data[i * 3 + 2] = orient[i * 3 + 2]; // instead of raw input values
    }

    for (int i = 1; i < num_segments; i++) {
        int start_idx = output->jizhi[i - 1];
        int end_idx = output->jizhi[i];
        int segment_size = end_idx - start_idx + 1;

        // Process position data
        Matrix* will_fit_pos = create_matrix(segment_size, 3);

        // Copy position data
        for (int j = 0; j < segment_size; j++) {
            set_matrix_element(will_fit_pos, j, 0, pos_data[(start_idx + j) * 3]);
            set_matrix_element(will_fit_pos, j, 1, pos_data[(start_idx + j) * 3 + 1]);
            set_matrix_element(will_fit_pos, j, 2, pos_data[(start_idx + j) * 3 + 2]);
        }

        // Call fine_fitting for position data
        Matrix* pos_fit = NULL;
        Matrix* Nikx = NULL;
        Matrix* Niky = NULL;
        Matrix* Nikz = NULL;
        Matrix* X = NULL;

        fine_fitting(will_fit_pos, &pos_fit, &Nikx, &Niky, &Nikz, &X, input->diedai);
        ////打印X
        //printf("X:\n");
        //print_matrix(X);
        // Remove specific rows from X matrix (same as MATLAB code)
        // In MATLAB: for j=1:(size(X,1)/21)-1
        // 
        //*********************************************************************************************************************
        int num_loops = (int)((float)X->rows / 21.0f) - 1; // Use floating point division like MATLAB
        for (int j = 1; j <= num_loops; j++) {
            int ra = 21 * j - (j - 1);
            remove_row(X, ra - 1); // Subtract 1 for 0-based indexing in C
        }
    
        //// 简单优化：从后往前删除，减少数据移动
        //int num_loops = (int)((float)X->rows / 21.0f) - 1;
        //for (int j = num_loops; j >= 1; j--) {
        //    int row_to_remove = 21 * j - (j - 1);
        //    if (row_to_remove - 1 < X->rows && row_to_remove - 1 >= 0) {
        //        remove_row(X, row_to_remove - 1);
        //    }
        //}
        //// Create deep copies of the data
        //Matrix* pos_fit_copy = copy_matrix(pos_fit);
        //Matrix* Nikx_copy = copy_matrix(Nikx);
        //Matrix* Niky_copy = copy_matrix(Niky);
        //Matrix* Nikz_copy = copy_matrix(Nikz);
        //Matrix* X_copy = copy_matrix(X);

        //// Store data in 3D matrices
        //int result = 0;
        //result += append_layer_flexible(output->fit_over_pos, pos_fit_copy);
        //result += append_layer_flexible(output->fit_over_pos_Nikx, Nikx_copy);
        //result += append_layer_flexible(output->fit_over_pos_Niky, Niky_copy);
        //result += append_layer_flexible(output->fit_over_pos_Nikz, Nikz_copy);
        //result += append_layer_flexible(output->fit_over_pos_X, X_copy);

        // 不再深拷贝，直接传递原始指针
        append_layer_flexible(output->fit_over_pos, pos_fit);
        append_layer_flexible(output->fit_over_pos_Nikx, Nikx);
        append_layer_flexible(output->fit_over_pos_Niky, Niky);
        append_layer_flexible(output->fit_over_pos_Nikz, Nikz);
        append_layer_flexible(output->fit_over_pos_X, X);

        // 不要再 free_matrix 这些指针
        free_matrix(will_fit_pos);


        // Clean up temporary matrices
        //free_matrix(will_fit_pos);
        //free_matrix(pos_fit);
        //free_matrix(Nikx);
        //free_matrix(Niky);
        //free_matrix(Nikz);
        //free_matrix(X);
        // Process orientation data
        //Matrix* will_fit_ori = create_matrix(segment_size, 3);

        //// Copy orientation data
        //for (int j = 0; j < segment_size; j++) {
        //    set_matrix_element(will_fit_ori, j, 0, orient_data[(start_idx + j) * 3]);
        //    set_matrix_element(will_fit_ori, j, 1, orient_data[(start_idx + j) * 3 + 1]);
        //    set_matrix_element(will_fit_ori, j, 2, orient_data[(start_idx + j) * 3 + 2]);
        //}
        //////打印will_fit_ori
        ////printf("will_fit_ori:\n");
        ////print_matrix(will_fit_ori);
        //// Convert will_fit_ori matrix data to array for QUAT function
        //double* orient_array = (double*)malloc(segment_size * 3 * sizeof(double));

        //// Copy data from will_fit_ori matrix to array
        //for (int j = 0; j < segment_size; j++) {
        //    orient_array[j * 3] = get_matrix_element(will_fit_ori, j, 0);
        //    orient_array[j * 3 + 1] = get_matrix_element(will_fit_ori, j, 1);
        //    orient_array[j * 3 + 2] = get_matrix_element(will_fit_ori, j, 2);
        //}

        //// Convert Euler angles to quaternion vectors using QUAT
        //Vector3_QUAT* orient_1 = QUAT(orient_array, segment_size, 3);

        //// Free temporary array
        //free(orient_array);

        Matrix* will_fit_ori = create_matrix(segment_size, 3);
        // 拷贝 orient_data 到 will_fit_ori
        for (int j = 0; j < segment_size; j++) {
            set_matrix_element(will_fit_ori, j, 0, orient_data[(start_idx + j) * 3]);
            set_matrix_element(will_fit_ori, j, 1, orient_data[(start_idx + j) * 3 + 1]);
            set_matrix_element(will_fit_ori, j, 2, orient_data[(start_idx + j) * 3 + 2]);
        }
        // 再拷贝 will_fit_ori 到 orient_array
        double* orient_array = (double*)malloc(segment_size * 3 * sizeof(double));
        for (int j = 0; j < segment_size; j++) {
            orient_array[j * 3] = get_matrix_element(will_fit_ori, j, 0);
            orient_array[j * 3 + 1] = get_matrix_element(will_fit_ori, j, 1);
            orient_array[j * 3 + 2] = get_matrix_element(will_fit_ori, j, 2);
        }
        Vector3_QUAT* orient_1 = QUAT(orient_array, segment_size, 3);
        free(orient_array);






        // Convert Vector3_QUAT array to Matrix
        Matrix* orient_1_matrix = create_matrix(segment_size, 3);

        for (int j = 0; j < segment_size; j++) {
            set_matrix_element(orient_1_matrix, j, 0, orient_1[j].x);
            set_matrix_element(orient_1_matrix, j, 1, orient_1[j].y);
            set_matrix_element(orient_1_matrix, j, 2, orient_1[j].z);
        }
        ////打印orient_1_matrix
        //printf("orient_1_matrix:\n");
        //print_matrix(orient_1_matrix);
        // Call fine_fitting for orientation data
        Matrix* ori_fit = NULL;
        Matrix* ori_Nikx = NULL;
        Matrix* ori_Niky = NULL;
        Matrix* ori_Nikz = NULL;
        Matrix* ori_X = NULL;

        fine_fitting(orient_1_matrix, &ori_fit, &ori_Nikx, &ori_Niky, &ori_Nikz, &ori_X,input->diedai);
        ////打印ori_x
        //printf("ori_X:\n");
        //print_matrix(ori_X);
        // Free orient_1 and orient_1_matrix after use
        free(orient_1);
        free_matrix(orient_1_matrix);
        // Create deep copies of the data
        Matrix* ori_fit_copy = copy_matrix(ori_fit);
        Matrix* ori_Nikx_copy = copy_matrix(ori_Nikx);
        Matrix* ori_Niky_copy = copy_matrix(ori_Niky);
        Matrix* ori_Nikz_copy = copy_matrix(ori_Nikz);
        Matrix* ori_X_copy = copy_matrix(ori_X);
        // Store data in 3D matrices
        int ori_result = 0;
        ori_result += append_layer_flexible(output->fit_over_ori, ori_fit_copy);
        ori_result += append_layer_flexible(output->fit_over_ori_Nikx, ori_Nikx_copy);
        ori_result += append_layer_flexible(output->fit_over_ori_Niky, ori_Niky_copy);
        ori_result += append_layer_flexible(output->fit_over_ori_Nikz, ori_Nikz_copy);
        ori_result += append_layer_flexible(output->fit_over_ori_X, ori_X_copy);
        // Clean up temporary matrices
        free_matrix(will_fit_ori);
        free_matrix(ori_fit);
        free_matrix(ori_Nikx);
        free_matrix(ori_Niky);
        free_matrix(ori_Nikz);
        free_matrix(ori_X);
  

    //Print fit_over_pos
    //printf("fit_over_pos:\n");
    for (int i = 0; i < output->fit_over_pos->layer_count; i++) {
        Matrix* layer = get_layer(output->fit_over_pos, i);
        //printf("Layer %d:\n", i);
        //print_matrix(layer);
        free_matrix(layer);
    }

    //Print fit_over_ori
   // printf("fit_over_ori:\n");
    for (int i = 0; i < output->fit_over_ori->layer_count; i++) {
        Matrix* layer = get_layer(output->fit_over_ori, i);
        //printf("Layer %d:\n", i);
        //print_matrix(layer);
        free_matrix(layer);
    }
}
    // Free temporary arrays
    free(pos_data);
    free(orient_data);
   
    // Handle the case when there are only two segments (start and end points)
    if (output->jizhi_size == 2) {
        // Set velocity constraints
        double vmax = input->vmax;
        double amax = input->amax;
        double jmax = input->jmax;

        // Get the first segment's control points
        Matrix* P1_CX = get_layer(output->fit_over_pos_Nikx, 0);
        Matrix* P1_CY = get_layer(output->fit_over_pos_Niky, 0);
        Matrix* P1_CZ = get_layer(output->fit_over_pos_Nikz, 0);
        Matrix* P1_oriX = get_layer(output->fit_over_ori_Nikx, 0);
        Matrix* P1_oriY = get_layer(output->fit_over_ori_Niky, 0);
        Matrix* P1_oriZ = get_layer(output->fit_over_ori_Nikz, 0);
        // Calculate the dimensions for reshaping
        int total_elements = P1_CX->rows * P1_CX->cols;  // Total number of elements in the matrix
        int rows = 4;  // Fixed number of rows
        int cols = total_elements / rows;  // Calculate number of columns needed

        // Create temporary 4xn matrices for each control point vector
        Matrix* P1_CX_4xn = create_matrix(rows, cols);
        Matrix* P1_CY_4xn = create_matrix(rows, cols);
        Matrix* P1_CZ_4xn = create_matrix(rows, cols);
        Matrix* P1_oriX_4xn = create_matrix(rows, cols);
        Matrix* P1_oriY_4xn = create_matrix(rows, cols);
        Matrix* P1_oriZ_4xn = create_matrix(rows, cols);
        // Copy data from original matrices to 4xn matrices
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                int src_idx = i * cols + j;
                int src_row = src_idx / P1_CX->cols;
                int src_col = src_idx % P1_CX->cols;

                if (src_row < P1_CX->rows) {
                    set_matrix_element(P1_CX_4xn, i, j, get_matrix_element(P1_CX, src_row, src_col));
                    set_matrix_element(P1_CY_4xn, i, j, get_matrix_element(P1_CY, src_row, src_col));
                    set_matrix_element(P1_CZ_4xn, i, j, get_matrix_element(P1_CZ, src_row, src_col));
                    set_matrix_element(P1_oriX_4xn, i, j, get_matrix_element(P1_oriX, src_row, src_col));
                    set_matrix_element(P1_oriY_4xn, i, j, get_matrix_element(P1_oriY, src_row, src_col));
                    set_matrix_element(P1_oriZ_4xn, i, j, get_matrix_element(P1_oriZ, src_row, src_col));
                }
            }
        }

        // Now use the 4xn matrices for further processing
        // ... existing code ...

        // Call Nik_to_control for position and orientation
        Matrix* control_point = Nik_to_control(P1_CX_4xn, P1_CY_4xn, P1_CZ_4xn);
        Matrix* control_point_ori = Nik_to_control(P1_oriX_4xn, P1_oriY_4xn, P1_oriZ_4xn);
        // Add trajectory planning using bsplan_pf_1
        double vs = 0.0;
        double ve = 0.0;
        double nik_u = 0.0;
        double end_u = control_point->rows - 3;

        Matrix* position = NULL;
        Matrix* U = NULL;

        int result = bsplan_pf_1(control_point, vs, ve, input->vmax, input->amax, input->jmax,
            nik_u, end_u, &position, &U);
        // Create U_ori with same dimensions as U (1xn matrix)
        output->U_ori = create_matrix(1, U->cols);
        // Get the first and last value of U (U is a 1xn matrix)
        double U_first = get_matrix_element(U, 0, 0);
        double U_last = get_matrix_element(U, 0, U->cols - 1);

        // Calculate U_ori exactly as in MATLAB code
        // U_ori=(U-U(1))/(U(end)-U(1))*(size(control_point_ori,1)-3);
        double scale_factor = control_point_ori->rows - 3;

        // U is a 1xn matrix, so access by row
        for (int i = 0; i < U->cols; i++) {
            double u_value = get_matrix_element(U, 0, i);
            double normalized_u = (u_value - U_first) / (U_last - U_first) * scale_factor;
            set_matrix_element(output->U_ori, 0, i, normalized_u);
        }

        ////Print U_ori
        //printf("U_ori:\n");
        //print_matrix(output->U_ori);

        // Set ORI_con = control_point_ori (exactly as in MATLAB)
        output->ORI_con = copy_matrix(control_point_ori);
        ////Print ORI_con
        //printf("ORI_con:\n");
        //print_matrix(output->ORI_con);

        // Call Velocity_Bspline_u with ORI_con and U_ori to get orientation trajectory
        // [~,~,~,p_u_x_ori,p_u_y_ori,p_u_z_ori]=Velocity_Bspline_u(ORI_con,U_ori);
        Matrix* Nikx_ori = NULL;
        Matrix* Niky_ori = NULL;
        Matrix* Nikz_ori = NULL;
        Matrix* p_u_x_ori = NULL;
        Matrix* p_u_y_ori = NULL;
        Matrix* p_u_z_ori = NULL;

        // Convert U_ori matrix to double array for Velocity_Bspline_u
        double* U_ori_array = (double*)malloc(U->cols * sizeof(double));
   
        // Copy U_ori values to array
        for (int i = 0; i < U->cols; i++) {
            U_ori_array[i] = get_matrix_element(output->U_ori, 0, i);
        }

        // Call Velocity_Bspline_u function
        Velocity_Bspline_u(output->ORI_con, U_ori_array, U->cols, &Nikx_ori, &Niky_ori, &Nikz_ori, &p_u_x_ori, &p_u_y_ori, &p_u_z_ori);
        // Free allocated memory
        free(U_ori_array);
        free_matrix(Nikx_ori);
        free_matrix(Niky_ori);
        free_matrix(Nikz_ori);

        // Create Z_inter as the concatenation of position and orientation data
        int pos_rows = position->rows;   // Should be 3
        int pos_cols = position->cols;   // Number of points

        // Store position data in output structure
        output->position_inter = (double*)malloc(pos_rows * pos_cols * sizeof(double));
        output->Z_inter = (double*)malloc(2 * pos_rows * pos_cols * sizeof(double));
        output->Z_inter_size = 2 * pos_rows * pos_cols;
        for (int i = 0; i < pos_cols; i++) {
            // Copy position data (Z_pos = position)
            output->position_inter[i * pos_rows + 0] = get_matrix_element(position, 0, i);
            output->position_inter[i * pos_rows + 1] = get_matrix_element(position, 1, i);
            output->position_inter[i * pos_rows + 2] = get_matrix_element(position, 2, i);

            // Add position data to Z_inter
            output->Z_inter[i * (2 * pos_rows) + 0] = get_matrix_element(position, 0, i);
            output->Z_inter[i * (2 * pos_rows) + 1] = get_matrix_element(position, 1, i);
            output->Z_inter[i * (2 * pos_rows) + 2] = get_matrix_element(position, 2, i);

            // Add orientation data to Z_inter
            output->Z_inter[i * (2 * pos_rows) + 3] = get_matrix_element(p_u_x_ori, i, 0);
            output->Z_inter[i * (2 * pos_rows) + 4] = get_matrix_element(p_u_y_ori, i, 0);
            output->Z_inter[i * (2 * pos_rows) + 5] = get_matrix_element(p_u_z_ori, i, 0);
        }

        output->position_inter_size = pos_rows * pos_cols;


        for (int i = 0; i < pos_cols; i++) {
                output->Z_inter[i * (2 * pos_rows) + 0],
                output->Z_inter[i * (2 * pos_rows) + 1],
                output->Z_inter[i * (2 * pos_rows) + 2],
                output->Z_inter[i * (2 * pos_rows) + 3],
                output->Z_inter[i * (2 * pos_rows) + 4],
                output->Z_inter[i * (2 * pos_rows) + 5];
        }

        // Create orientation matrix by combining p_u_x_ori, p_u_y_ori, and p_u_z_ori
        // Equivalent to MATLAB: ori=[p_u_x_ori,p_u_y_ori,p_u_z_ori]
        Matrix* ori = create_matrix(p_u_x_ori->rows, 3);
        if (ori) {
            for (int j = 0; j < p_u_x_ori->rows; j++) {
                set_matrix_element(ori, j, 0, get_matrix_element(p_u_x_ori, j, 0));
                set_matrix_element(ori, j, 1, get_matrix_element(p_u_y_ori, j, 0));
                set_matrix_element(ori, j, 2, get_matrix_element(p_u_z_ori, j, 0));
            }
            // Set Z_ori = ori
            output->Z_ori = ori;
        }
        
        // Create position matrix from existing position data (transposed)
        // Equivalent to MATLAB: Z_pos = position
        Matrix* pos_matrix = transpose_matrix(position);
        if (pos_matrix) {
            output->Z_pos = pos_matrix;
        }
 
        // Free orientation matrices now that we've copied their data
        free_matrix(p_u_x_ori);
        free_matrix(p_u_y_ori);
        free_matrix(p_u_z_ori);
    }
    else {
 
        output->P_bridge = create_matrix(0, 3);  // Empty matrix for bridge positions
        output->P_bridge_con = init_3d(0, 0);    // Empty cell array for bridge control points
        output->P_bridge_X = init_3d(0, 0);      // Empty cell array for bridge X data
        output->indixi_trunc = init_3d(0, 0);    // Empty cell array for truncated indices
        output->P_bridge_ori = create_matrix(0, 3); // Empty matrix for bridge orientations

        output->transfer_error = input->pos_smooth_param; // 取值0~1,1表示误差较大，0表示误差较小
        output->quotient_floor = floor(output->transfer_error / 0.2) * 0.2;
        output->end_u1 = output->quotient_floor * 0.25 + 0.1;
        output->nik_u1 = 0.9 - output->quotient_floor * 0.25;
        for (int i = 2; i < output->jizhi_size; i++) {
            Matrix* P1 = get_layer(output->fit_over_pos, i - 2);  // i-1 in MATLAB is i-2 in C (0-based)
            Matrix* P1_C = get_layer(output->fit_over_pos_X, i - 2);
            Matrix* P2 = get_layer(output->fit_over_pos, i - 1);  // i in MATLAB is i-1 in C
            Matrix* P2_C = get_layer(output->fit_over_pos_X, i - 1);
            Matrix* P1_C_ori = get_layer(output->fit_over_ori_X, i - 2);
            Matrix* P2_C_ori = get_layer(output->fit_over_ori_X, i - 1);
            output->ARC_1 = create_matrix(21, P1_C->cols);
            output->ARC_2 = create_matrix(21, P2_C->cols);
            for (int row = 0; row < 21; row++) {
                int src_row = P1_C->rows - 21 + row;
                for (int col = 0; col < P1_C->cols; col++) {
                    set_matrix_element(output->ARC_1, row, col, get_matrix_element(P1_C, src_row, col));
                }
            }
            for (int row = 0; row < 21; row++) {
                for (int col = 0; col < P2_C->cols; col++) {
                    set_matrix_element(output->ARC_2, row, col, get_matrix_element(P2_C, row, col));
                }
            }
            output->ARC_1_ori = create_matrix(21, P1_C_ori->cols);
            output->ARC_2_ori = create_matrix(21, P2_C_ori->cols);

            for (int row = 0; row < 21; row++) {
                int src_row = P1_C_ori->rows - 21 + row;
                for (int col = 0; col < P1_C_ori->cols; col++) {
                    set_matrix_element(output->ARC_1_ori, row, col, get_matrix_element(P1_C_ori, src_row, col));
                }
            }

            for (int row = 0; row < 21; row++) {
                for (int col = 0; col < P2_C_ori->cols; col++) {
                    set_matrix_element(output->ARC_2_ori, row, col, get_matrix_element(P2_C_ori, row, col));
                }
            }

            // Initialize constants
            double C1 = 1.0;
            double C2 = 1.0;
            double** arc_1 = (double**)malloc(output->ARC_1->rows * sizeof(double*));
            double** arc_2 = (double**)malloc(output->ARC_2->rows * sizeof(double*));

            for (int j = 0; j < output->ARC_1->rows; j++) {
                arc_1[j] = (double*)malloc(output->ARC_1->cols * sizeof(double));
                for (int k = 0; k < output->ARC_1->cols; k++) {
                    arc_1[j][k] = get_matrix_element(output->ARC_1, j, k);
                }
            }
            for (int j = 0; j < output->ARC_2->rows; j++) {
                arc_2[j] = (double*)malloc(output->ARC_2->cols * sizeof(double));
                for (int k = 0; k < output->ARC_2->cols; k++) {
                    arc_2[j][k] = get_matrix_element(output->ARC_2, j, k);
                }
            }

            double C1_matrix[3][3] = {{1.0, 0.0, 0.0}, {0.0, 1.0, 0.0}, {0.0, 0.0, 1.0}};
            double C2_matrix[3][3] = {{1.0, 0.0, 0.0}, {0.0, 1.0, 0.0}, {0.0, 0.0, 1.0}};

            BridgeResult_Bridge_arc_arc_1 bridge_result = Bridge_arc_arc_1(
                C1_matrix, C2_matrix,
                arc_1, output->ARC_1->rows, output->ARC_1->cols,
                arc_2, output->ARC_2->rows, output->ARC_2->cols,
                output->nik_u1, output->end_u1
            );

            if (bridge_result.P != NULL) {
                output->P_bridge = create_matrix(bridge_result.P_rows, bridge_result.P_cols);
                if (output->P_bridge) {
                    for (int j = 0; j < bridge_result.P_rows; j++) {
                        for (int k = 0; k < bridge_result.P_cols; k++) {
                            set_matrix_element(output->P_bridge, j, k, bridge_result.P[j][k]);
                        }
                    }
                }
            }
            // Store X matrix
            if (bridge_result.X != NULL) {
                // Create a new layer in P_bridge_X for the X matrix
                Matrix* X_matrix = create_matrix(bridge_result.X_rows, bridge_result.X_cols);
                if (X_matrix) {
                    for (int j = 0; j < bridge_result.X_rows; j++) {
                        for (int k = 0; k < bridge_result.X_cols; k++) {
                            set_matrix_element(X_matrix, j, k, bridge_result.X[j][k]);
                        }
                    }
                    append_layer_flexible(output->P_bridge_X, X_matrix);
                    free_matrix(X_matrix);
                }
            }
    
            // Store control points
            output->control_point_bridge = bridge_result.control_point_3;

            // Clean up
            for (int j = 0; j < output->ARC_1->rows; j++) free(arc_1[j]);
            free(arc_1);
            for (int j = 0; j < output->ARC_2->rows; j++) free(arc_2[j]);
            free(arc_2);
            free_bridge_result_Bridge_arc_arc_1(&bridge_result);


            // Convert ARC_1_ori and ARC_2_ori to double** format
            double** arc_1_ori = (double**)malloc(output->ARC_1_ori->rows * sizeof(double*));
            double** arc_2_ori = (double**)malloc(output->ARC_2_ori->rows * sizeof(double*));

            for (int j = 0; j < output->ARC_1_ori->rows; j++) {
                arc_1_ori[j] = (double*)malloc(output->ARC_1_ori->cols * sizeof(double));
                for (int k = 0; k < output->ARC_1_ori->cols; k++) {
                    arc_1_ori[j][k] = get_matrix_element(output->ARC_1_ori, j, k);
                }
            }

            for (int j = 0; j < output->ARC_2_ori->rows; j++) {
                arc_2_ori[j] = (double*)malloc(output->ARC_2_ori->cols * sizeof(double));
                for (int k = 0; k < output->ARC_2_ori->cols; k++) {
                    arc_2_ori[j][k] = get_matrix_element(output->ARC_2_ori, j, k);
                }
            }

            // Call Bridge_arc_arc_2
            BridgeResult_Bridge_arc_arc_2 bridge_result_ori = Bridge_arc_arc_2(
                C1_matrix, C2_matrix,
                arc_1_ori, output->ARC_1_ori->rows, output->ARC_1_ori->cols,
                arc_2_ori, output->ARC_2_ori->rows, output->ARC_2_ori->cols,
                output->nik_u1, output->end_u1
            );

            // Store the results
            // Store P_ori matrix
            if (bridge_result_ori.P != NULL) {
                output->P_bridge_ori = create_matrix(bridge_result_ori.P_rows, bridge_result_ori.P_cols);
                if (output->P_bridge_ori) {
                    for (int j = 0; j < bridge_result_ori.P_rows; j++) {
                        for (int k = 0; k < bridge_result_ori.P_cols; k++) {
                            set_matrix_element(output->P_bridge_ori, j, k, bridge_result_ori.P[j][k]);
                        }
                    }
                }
            }

            // Store control points for orientation
            output->control_point_bridge_ori = bridge_result_ori.control_point_3;

            // Find nearest point in pos data to P0
            Point* pos_points = (Point*)malloc(newRows * sizeof(Point));

            // Convert pos data to Point array
            for (int i = 0; i < newRows; i++) {
                pos_points[i].x = pos[i * 3];
                pos_points[i].y = pos[i * 3 + 1];
                pos_points[i].z = pos[i * 3 + 2];
            }

            // Create target point from bridge_result.P0
            Point target_point;
            target_point.x = bridge_result.P0.x;
            target_point.y = bridge_result.P0.y;
            target_point.z = bridge_result.P0.z;

            // Find nearest point
            double min_distance;
            find_nearest_point(pos_points, newRows, target_point, &output->index_bridge_start, &min_distance);

            printf("Found bridge start index: %zu (distance: %.6f)\n", output->index_bridge_start, min_distance);

            // Create target point from bridge_result.P4
            target_point.x = bridge_result.P4.x;
            target_point.y = bridge_result.P4.y;
            target_point.z = bridge_result.P4.z;

            // Find nearest point to P4
            find_nearest_point(pos_points, newRows, target_point, &output->index_bridge_end, &min_distance);

            printf("Found bridge end index: %zu (distance: %.6f)\n", output->index_bridge_end, min_distance);

            // Check if start and end indices are the same and adjust if necessary
            if (output->index_bridge_end == output->index_bridge_start) {
                if (output->index_bridge_end < newRows - 1) {  // Make sure we don't go past the array bounds
                    output->index_bridge_end++;
                    printf("Adjusted bridge end index to: %zu (was equal to start index)\n", output->index_bridge_end);
                }
            }

            // Store truncated indices
            Matrix* indices = create_matrix(1, 2);
            if (indices) {
                set_matrix_element(indices, 0, 0, (double)output->index_bridge_start);
                set_matrix_element(indices, 0, 1, (double)output->index_bridge_end);
                if (output->indixi_trunc) {
                    append_layer_flexible(output->indixi_trunc, indices);
                }
                free_matrix(indices);
            }

            // Store bridge control points
            Matrix* control_points = create_matrix(5, 3);
            if (control_points) {
                // Store P0
                set_matrix_element(control_points, 0, 0, bridge_result.control_point_3.P0.x);
                set_matrix_element(control_points, 0, 1, bridge_result.control_point_3.P0.y);
                set_matrix_element(control_points, 0, 2, bridge_result.control_point_3.P0.z);
                // Store P1
                set_matrix_element(control_points, 1, 0, bridge_result.control_point_3.P1.x);
                set_matrix_element(control_points, 1, 1, bridge_result.control_point_3.P1.y);
                set_matrix_element(control_points, 1, 2, bridge_result.control_point_3.P1.z);
                // Store P2
                set_matrix_element(control_points, 2, 0, bridge_result.control_point_3.P2.x);
                set_matrix_element(control_points, 2, 1, bridge_result.control_point_3.P2.y);
                set_matrix_element(control_points, 2, 2, bridge_result.control_point_3.P2.z);
                // Store P3
                set_matrix_element(control_points, 3, 0, bridge_result.control_point_3.P3.x);
                set_matrix_element(control_points, 3, 1, bridge_result.control_point_3.P3.y);
                set_matrix_element(control_points, 3, 2, bridge_result.control_point_3.P3.z);
                // Store P4
                set_matrix_element(control_points, 4, 0, bridge_result.control_point_3.P4.x);
                set_matrix_element(control_points, 4, 1, bridge_result.control_point_3.P4.y);
                set_matrix_element(control_points, 4, 2, bridge_result.control_point_3.P4.z);

                if (output->P_bridge_con) {
                    append_layer_flexible(output->P_bridge_con, control_points);
                }
                free_matrix(control_points);
            }

            // Store bridge X matrix
            if (bridge_result.X != NULL) {
                Matrix* X_matrix = create_matrix(bridge_result.X_rows, bridge_result.X_cols);
                if (X_matrix) {
                    for (int j = 0; j < bridge_result.X_rows; j++) {
                        for (int k = 0; k < bridge_result.X_cols; k++) {
                            set_matrix_element(X_matrix, j, k, bridge_result.X[j][k]);
                        }
                    }
                    if (output->P_bridge_X) {
                        append_layer_flexible(output->P_bridge_X, X_matrix);
                    }
                    free_matrix(X_matrix);
                }
            }

            // Store bridge P matrix
            if (bridge_result.P != NULL) {
                Matrix* new_P_bridge = create_matrix(bridge_result.P_rows, bridge_result.P_cols);
                if (new_P_bridge) {
                    for (int j = 0; j < bridge_result.P_rows; j++) {
                        for (int k = 0; k < bridge_result.P_cols; k++) {
                            set_matrix_element(new_P_bridge, j, k, bridge_result.P[j][k]);
                        }
                    }
                    // Append to existing P_bridge if it exists, otherwise create new
                    if (output->P_bridge == NULL) {
                        output->P_bridge = new_P_bridge;
                    } else {
                        Matrix* combined = combine_matrices_vertically(output->P_bridge, new_P_bridge);
                        if (combined) {
                            free_matrix(output->P_bridge);
                            output->P_bridge = combined;
                        }
                        free_matrix(new_P_bridge);
                    }
                }
            }

            // Store orientation control points
            Matrix* control_points_ori = create_matrix(5, 3);
            if (control_points_ori) {
                // Store P0
                set_matrix_element(control_points_ori, 0, 0, bridge_result_ori.control_point_3.P0.x);
                set_matrix_element(control_points_ori, 0, 1, bridge_result_ori.control_point_3.P0.y);
                set_matrix_element(control_points_ori, 0, 2, bridge_result_ori.control_point_3.P0.z);
                // Store P1
                set_matrix_element(control_points_ori, 1, 0, bridge_result_ori.control_point_3.P1.x);
                set_matrix_element(control_points_ori, 1, 1, bridge_result_ori.control_point_3.P1.y);
                set_matrix_element(control_points_ori, 1, 2, bridge_result_ori.control_point_3.P1.z);
                // Store P2
                set_matrix_element(control_points_ori, 2, 0, bridge_result_ori.control_point_3.P2.x);
                set_matrix_element(control_points_ori, 2, 1, bridge_result_ori.control_point_3.P2.y);
                set_matrix_element(control_points_ori, 2, 2, bridge_result_ori.control_point_3.P2.z);
                // Store P3
                set_matrix_element(control_points_ori, 3, 0, bridge_result_ori.control_point_3.P3.x);
                set_matrix_element(control_points_ori, 3, 1, bridge_result_ori.control_point_3.P3.y);
                set_matrix_element(control_points_ori, 3, 2, bridge_result_ori.control_point_3.P3.z);
                // Store P4
                set_matrix_element(control_points_ori, 4, 0, bridge_result_ori.control_point_3.P4.x);
                set_matrix_element(control_points_ori, 4, 1, bridge_result_ori.control_point_3.P4.y);
                set_matrix_element(control_points_ori, 4, 2, bridge_result_ori.control_point_3.P4.z);

                if (output->P_bridge_con_ori) {
                    append_layer_flexible(output->P_bridge_con_ori, control_points_ori);
                }
                free_matrix(control_points_ori);
            }

            // Store bridge orientation P matrix
            if (bridge_result_ori.P != NULL) {
                Matrix* new_P_bridge_ori = create_matrix(bridge_result_ori.P_rows, bridge_result_ori.P_cols);
                if (new_P_bridge_ori) {
                    for (int j = 0; j < bridge_result_ori.P_rows; j++) {
                        for (int k = 0; k < bridge_result_ori.P_cols; k++) {
                            set_matrix_element(new_P_bridge_ori, j, k, bridge_result_ori.P[j][k]);
                        }
                    }
                    // Append to existing P_bridge_ori if it exists, otherwise create new
                    if (output->P_bridge_ori == NULL) {
                        output->P_bridge_ori = new_P_bridge_ori;
                    } else {
                        Matrix* combined = combine_matrices_vertically(output->P_bridge_ori, new_P_bridge_ori);
                        if (combined) {
                            free_matrix(output->P_bridge_ori);
                            output->P_bridge_ori = combined;
                        }
                        free_matrix(new_P_bridge_ori);
                    }
                }
            }

            // Free temporary array
            free(pos_points);

            // Clean up orientation data
            for (int j = 0; j < output->ARC_1_ori->rows; j++) free(arc_1_ori[j]);
            free(arc_1_ori);
            for (int j = 0; j < output->ARC_2_ori->rows; j++) free(arc_2_ori[j]);
            free(arc_2_ori);
            free_bridge_result_Bridge_arc_arc_2(&bridge_result_ori);

            printf("Bridge_arc_arc_2 call completed.\n");
     
            // Free the matrices after use
            if (P1) free_matrix(P1);
            if (P1_C) free_matrix(P1_C);
            if (P2) free_matrix(P2);
            if (P2_C) free_matrix(P2_C);
            if (P1_C_ori) free_matrix(P1_C_ori);
            if (P2_C_ori) free_matrix(P2_C_ori);
        }

        // Calculate transition velocities for bridge paths
        int num_bridges = output->P_bridge_X ? output->P_bridge_X->layer_count : 0;
        printf("\n--- Calculating Transition Velocities for %d Bridge Paths ---\n", num_bridges);

        // Allocate arrays for transition velocities
        output->V0 = (double*)malloc(num_bridges * sizeof(double));
        output->V1 = (double*)malloc(num_bridges * sizeof(double));
        output->V_size = num_bridges;
        // Process each bridge segment
        for (int i = 0; i < num_bridges; i++) {
            // Get curvature values from P_bridge_X
            Matrix* bridge_X = get_layer(output->P_bridge_X, i);
            if (!bridge_X) {
                fprintf(stderr, "Failed to get bridge X data for segment %d\n", i);
                continue;
            }

            // Find maximum curvature (first column of bridge_X)
            double K_max = get_matrix_element(bridge_X, 0, 0);
            for (int j = 1; j < bridge_X->rows; j++) {
                double K = get_matrix_element(bridge_X, j, 0);
                if (K > K_max) K_max = K;
            }

            // Calculate transition velocities
            // V0 = sqrt(Amax/Kmax)
            output->V0[i] = sqrt(input->amax / K_max);

            // V1 = (Jmax/Kmax)^(1/3)
            output->V1[i] = cbrt(input->jmax / K_max);

       /*     printf("Bridge %d: Kmax=%.6f, V0=%.6f, V1=%.6f\n", 
                   i, K_max, output->V0[i], output->V1[i]);*/

            free_matrix(bridge_X);
        }
        printf("--- End of Transition Velocity Calculations ---\n\n");

        // Calculate V_B array with boundary conditions
        // V_B = [0 min(V0,V1) 0]
        output->V_B_size = num_bridges + 2;  // +2 for start and end zeros
        output->V_B = (double*)malloc(output->V_B_size * sizeof(double));

        printf("\n--- Calculating Bridge Velocities (V_B) ---\n");
        
        // Set first element to 0
        output->V_B[0] = 0.0;
        
        // Process middle elements
        for (int i = 0; i < num_bridges; i++) {
            // Get minimum of V0[i] and V1[i]
            double v_min = output->V0[i] < output->V1[i] ? output->V0[i] : output->V1[i];
            
            // Compare with running speed and take minimum
            output->V_B[i + 1] = input->running_speed < v_min ? input->running_speed : v_min;
            
    /*        printf("Bridge %d: V0=%.6f, V1=%.6f, V_min=%.6f, V_B=%.6f\n",
                   i, output->V0[i], output->V1[i], v_min, output->V_B[i + 1]);*/
        }
        
        // Set last element to 0
        output->V_B[output->V_B_size - 1] = 0.0;
        
        //printf("Final V_B array: [");
        //for (int i = 0; i < output->V_B_size; i++) {
        //    printf("%.6f", output->V_B[i]);
        //    if (i < output->V_B_size - 1) printf(", ");
        //}
        //printf("]\n");
        //printf("--- End of Bridge Velocity Calculations ---\n\n");

        // Initialize empty matrices and cell arrays
        output->poss = create_matrix(0, 3);        // Empty position array
       // printf("Initial poss matrix dimensions: %dx%d\n", output->poss->rows, output->poss->cols);
        output->ori_chabu = create_matrix(0, 3);   // Empty orientation array
        output->A_pos = init_3d(0, 0);            // Empty position A matrices
        output->B_pos = init_3d(0, 0);            // Empty position B matrices
        output->A_ori = init_3d(0, 0);            // Empty orientation A matrices
        output->B_ori = init_3d(0, 0);            // Empty orientation B matrices
        output->UU = init_3d(0, 0);               // Empty U parameter matrices

        // Initialize first segment control points to NULL
        output->P1_CX = NULL;
        output->P1_CY = NULL;
        output->P1_CZ = NULL;
        output->P1_oriX = NULL;
        output->P1_oriY = NULL;
        output->P1_oriZ = NULL;

        // Initialize control point
        output->control_point = NULL;
        
        // Declare ori at a higher scope so it persists across loop iterations
        Matrix* ori = NULL;

        // Process segments in a loop
        for (int i = 0; i < output->jizhi_size - 1; i++) {
            // Declare variables for position and orientation data
            Matrix* position = NULL;
            Matrix* U = NULL;

            if (i == 0) { // First segment (equivalent to i==1 in MATLAB)
                
                // Get control points for first segment
                output->P1_CX = get_layer(output->fit_over_pos_Nikx, i);
                output->P1_CY = get_layer(output->fit_over_pos_Niky, i);
                output->P1_CZ = get_layer(output->fit_over_pos_Nikz, i);
                output->P1_oriX = get_layer(output->fit_over_ori_Nikx, i);
                output->P1_oriY = get_layer(output->fit_over_ori_Niky, i);
                output->P1_oriZ = get_layer(output->fit_over_ori_Nikz, i);

                Matrix* segment_control_point = Nik_to_control(output->P1_CX, output->P1_CY, output->P1_CZ);

                // Calculate end_u based on control point size
                double end_u = output->nik_u1 - 1.0 + segment_control_point->rows - 3;

                // Store the control points and its size
                output->control_point = segment_control_point;
                output->control_point_size = segment_control_point->rows;

                // Call bsplan_pf_1 for trajectory planning
                double vs = 0.0;
                double ve = 0.0;
                double nik_u = 0.0;
                if (output->V_B && output->V_B_size >= 2) {
                    vs = output->V_B[0];
                    ve = output->V_B[1];
                }

                int result = bsplan_pf_1(segment_control_point, vs, ve, input->vmax, input->amax, input->jmax,
                                       nik_u, end_u, &position, &U);
                // Store the results
                if (output->position) {
                    if (output->position->rows > 0 && output->position->cols > 0) {
                        free_matrix(output->position);
                    } else {
                        output->position = NULL;
                    }
                }
                
                if (output->U) {
                    // Add extra safety check for U matrix
                    if (output->U->rows > 0 && output->U->cols > 0) {
                        free_matrix(output->U);
                    } else {
                        output->U = NULL;
                    }
                }

                // Transpose position from 3xn to nx3
                Matrix* position_transposed = transpose_matrix(position);
                if (position_transposed) {
       

                    // Free original position and store transposed version
                    free_matrix(position);
                    position = position_transposed;
                }

                output->position = position;
                output->U = U;
                output->control_point_ori = Nik_to_control(output->P1_oriX, output->P1_oriY, output->P1_oriZ);

                // Calculate orientation trajectory
                Matrix* Nikx_ori = NULL;
                Matrix* Niky_ori = NULL;
                Matrix* Nikz_ori = NULL;
                Matrix* p_u_x_ori = NULL;
                Matrix* p_u_y_ori = NULL;
                Matrix* p_u_z_ori = NULL;
                double* U_array = NULL;

                // Allocate and prepare U array
                U_array = (double*)malloc(U->cols * sizeof(double));
                for (int j = 0; j < U->cols; j++) {
                    U_array[j] = get_matrix_element(U, 0, j);
                }

                // Calculate orientation trajectory
                Velocity_Bspline_u(output->control_point_ori, U_array, U->cols,
                                 &Nikx_ori, &Niky_ori, &Nikz_ori,
                                 &p_u_x_ori, &p_u_y_ori, &p_u_z_ori);
                // Free U_array as it's no longer needed
                free(U_array);

                // Create orientation matrix by combining p_u_x_ori, p_u_y_ori, and p_u_z_ori
                // Equivalent to MATLAB: ori=[p_u_x_ori,p_u_y_ori,p_u_z_ori]
                ori = create_matrix(p_u_x_ori->rows, 3);
                if (ori) {
                    for (int j = 0; j < p_u_x_ori->rows; j++) {
                        set_matrix_element(ori, j, 0, get_matrix_element(p_u_x_ori, j, 0));
                        set_matrix_element(ori, j, 1, get_matrix_element(p_u_y_ori, j, 0));
                        set_matrix_element(ori, j, 2, get_matrix_element(p_u_z_ori, j, 0));
                    }
                }

                // Free temporary matrices
                free_matrix(Nikx_ori);
                free_matrix(Niky_ori);
                free_matrix(Nikz_ori);
                free_matrix(p_u_x_ori);
                free_matrix(p_u_y_ori);
                free_matrix(p_u_z_ori);
            }
            else if (i == output->jizhi_size - 2) { // Last segment (equivalent to i==length(jizhi)-1 in MATLAB)
    
                // Get control points for last segment
                output->P1_CX = get_layer(output->fit_over_pos_Nikx, i);
                output->P1_CY = get_layer(output->fit_over_pos_Niky, i);
                output->P1_CZ = get_layer(output->fit_over_pos_Nikz, i);
                output->P1_oriX = get_layer(output->fit_over_ori_Nikx, i);
                output->P1_oriY = get_layer(output->fit_over_ori_Niky, i);
                output->P1_oriZ = get_layer(output->fit_over_ori_Nikz, i);

                Matrix* segment_control_point = Nik_to_control(output->P1_CX, output->P1_CY, output->P1_CZ);

                // Calculate end_u based on control point size
                double nik_u = output->end_u1;
                double end_u = 1.0 - 1.0 + segment_control_point->rows - 3;

                // Store the control points and its size
                output->control_point = segment_control_point;
                output->control_point_size = segment_control_point->rows;

                // Call bsplan_pf_1 for trajectory planning
                double vs = 0.0;
                double ve = 0.0;
                if (output->V_B && i < output->V_B_size - 1) {
                    vs = output->V_B[i];
                    ve = output->V_B[i + 1];
                }

                int result = bsplan_pf_1(segment_control_point, vs, ve, input->vmax, input->amax, input->jmax,
                                       nik_u, end_u, &position, &U);
                
                //// Store the results
                //if (output->position) {
                //    if (output->position->rows > 0 && output->position->cols > 0) {
                //        free_matrix(output->position);
                //    } else {
                //        output->position = NULL;
                //    }
                //}
                
                //if (output->U) {
                //    // Add extra safety check for U matrix
                //    if (output->U->rows > 0 && output->U->cols > 0) {
                //        free_matrix(output->U);
                //    } else {
                //        output->U = NULL;
                //    }
                //}

                // Transpose position from 3xn to nx3
                Matrix* position_transposed = transpose_matrix(position);
                if (position_transposed) {
                    // Free original position and store transposed version
                    free_matrix(position);
                    position = position_transposed;
                }

                output->position = position;
                output->U = U;
                output->control_point_ori = Nik_to_control(output->P1_oriX, output->P1_oriY, output->P1_oriZ);
                Matrix* Nikx_ori = NULL;
                Matrix* Niky_ori = NULL;
                    Matrix* Nikz_ori = NULL;
                    Matrix* p_u_x_ori = NULL;
                    Matrix* p_u_y_ori = NULL;
                    Matrix* p_u_z_ori = NULL;
              
                double* U_array = NULL;

                // Allocate and prepare U array
                U_array = (double*)malloc(U->cols * sizeof(double));
                for (int j = 0; j < U->cols; j++) {
                    U_array[j] = get_matrix_element(U, 0, j);
                }

                // Calculate orientation trajectory
                Velocity_Bspline_u(output->control_point_ori, U_array, U->cols,
                                 &Nikx_ori, &Niky_ori, &Nikz_ori,
                                 &p_u_x_ori, &p_u_y_ori, &p_u_z_ori);

                // Free U_array as it's no longer needed
                free(U_array);

                // Create orientation matrix by combining p_u_x_ori, p_u_y_ori, and p_u_z_ori
                ori = create_matrix(p_u_x_ori->rows, 3);
                if (ori) {
                    for (int j = 0; j < p_u_x_ori->rows; j++) {
                        set_matrix_element(ori, j, 0, get_matrix_element(p_u_x_ori, j, 0));
                        set_matrix_element(ori, j, 1, get_matrix_element(p_u_y_ori, j, 0));
                        set_matrix_element(ori, j, 2, get_matrix_element(p_u_z_ori, j, 0));
                    }
                }


                // Free temporary matrices
                free_matrix(Nikx_ori);
                free_matrix(Niky_ori);
                free_matrix(Nikz_ori);
                free_matrix(p_u_x_ori);
                free_matrix(p_u_y_ori);
                free_matrix(p_u_z_ori);
            }
            else { // Middle segments
                // Get control points for middle segment
                output->P1_CX = get_layer(output->fit_over_pos_Nikx, i);
                output->P1_CY = get_layer(output->fit_over_pos_Niky, i);
                output->P1_CZ = get_layer(output->fit_over_pos_Nikz, i);
                output->P1_oriX = get_layer(output->fit_over_ori_Nikx, i);
                output->P1_oriY = get_layer(output->fit_over_ori_Niky, i);
                output->P1_oriZ = get_layer(output->fit_over_ori_Nikz, i);

                Matrix* segment_control_point = Nik_to_control(output->P1_CX, output->P1_CY, output->P1_CZ);
                
                // Different end_u calculation for middle segments
                double nik_u = output->end_u1;
                double end_u = output->nik_u1 - 1.0 + segment_control_point->rows - 3;
                
                // Store the control points and its size
                output->control_point = segment_control_point;
                output->control_point_size = segment_control_point->rows;
                
                // Get transition velocities
                double vs = 0.0;
                double ve = 0.0;
                if (output->V_B && i < output->V_B_size - 1) {
                    vs = output->V_B[i];
                    ve = output->V_B[i + 1];
                }
                
                // Call bsplan_pf_1 for trajectory planning
                Matrix* position = NULL;
                Matrix* U = NULL;
                int result = bsplan_pf_1(segment_control_point, vs, ve, input->vmax, input->amax, input->jmax,
                                      nik_u, end_u, &position, &U);
                
                // Transpose position from 3xn to nx3
                Matrix* position_transposed = transpose_matrix(position);
                if (position_transposed) {
                    // Free original position and store transposed version
                    free_matrix(position);
                    position = position_transposed;
                }
                
                // Create orientation control points
                output->control_point_ori = Nik_to_control(output->P1_oriX, output->P1_oriY, output->P1_oriZ);
                
                // Calculate orientation trajectory
                Matrix* Nikx_ori = NULL;
                Matrix* Niky_ori = NULL;
                Matrix* Nikz_ori = NULL;
                Matrix* p_u_x_ori = NULL;
                Matrix* p_u_y_ori = NULL;
                Matrix* p_u_z_ori = NULL;
                
                // Allocate and prepare U array
                double* U_array = (double*)malloc(U->cols * sizeof(double));
                for (int j = 0; j < U->cols; j++) {
                    U_array[j] = get_matrix_element(U, 0, j);
                }
                
                // Calculate orientation trajectory
                Velocity_Bspline_u(output->control_point_ori, U_array, U->cols,
                                &Nikx_ori, &Niky_ori, &Nikz_ori,
                                &p_u_x_ori, &p_u_y_ori, &p_u_z_ori);
                
                // Free U_array as it's no longer needed
                free(U_array);
                
                // Create orientation matrix by combining p_u_x_ori, p_u_y_ori, and p_u_z_ori
                ori = create_matrix(p_u_x_ori->rows, 3);
                if (ori) {
                    for (int j = 0; j < p_u_x_ori->rows; j++) {
                        set_matrix_element(ori, j, 0, get_matrix_element(p_u_x_ori, j, 0));
                        set_matrix_element(ori, j, 1, get_matrix_element(p_u_y_ori, j, 0));
                        set_matrix_element(ori, j, 2, get_matrix_element(p_u_z_ori, j, 0));
                    }
                }
                
                // Free temporary matrices
                free_matrix(Nikx_ori);
                free_matrix(Niky_ori);
                free_matrix(Nikz_ori);
                free_matrix(p_u_x_ori);
                free_matrix(p_u_y_ori);
                free_matrix(p_u_z_ori);
            }

            // Store position and orientation data in A_pos and A_ori arrays
            // Equivalent to MATLAB: A_pos{i}=position; A_ori{i}=ori;
            if (position && ori) {
                // Make copies of position and ori matrices to store in A_pos and A_ori
                Matrix* position_copy = copy_matrix(position);
                Matrix* ori_copy = copy_matrix(ori);
                
                if (position_copy && ori_copy) {
                    // Append to A_pos and A_ori arrays
                    if (output->A_pos) {
                        append_layer_flexible(output->A_pos, position_copy);
                    }
                    
                    if (output->A_ori) {
                        append_layer_flexible(output->A_ori, ori_copy);
                    }
                } else {
                    // Failed to copy, free any allocated memory
                    if (position_copy) free_matrix(position_copy);
                    if (ori_copy) free_matrix(ori_copy);
                }
                
                // Append position and orientation to poss and ori_chabu
                // Equivalent to MATLAB: poss=[poss;position]; ori_chabu=[ori_chabu;ori];
                if (output->poss) {
                    Matrix* new_poss = combine_matrices_vertically(output->poss, position);
                    if (new_poss) {
                        free_matrix(output->poss);
                        output->poss = new_poss;
                    }
                } else {
                    // If poss is NULL, create it as a copy of position
                    output->poss = copy_matrix(position);
                }
                
                if (output->ori_chabu) {
                    Matrix* new_ori_chabu = combine_matrices_vertically(output->ori_chabu, ori);
                    if (new_ori_chabu) {
                        free_matrix(output->ori_chabu);
                        output->ori_chabu = new_ori_chabu;
                    }
                } else {
                    // If ori_chabu is NULL, create it as a copy of ori
                    output->ori_chabu = copy_matrix(ori);
                }
            }

            // Free any temporary matrices used in this iteration
            // Note: We don't free 'ori' here anymore, it will be updated in the next iteration
            if (position) free_matrix(position);
            if (U) free_matrix(U);
            
            // Process bridge segments if not at the last segment
            // Equivalent to MATLAB: if i<length(jizhi)-1
            if (i < output->jizhi_size - 2) {  // Convert to 0-based indexing: jizhi_size-1 -> jizhi_size-2
                // Get bridge control points
                // Equivalent to MATLAB: control_point=P_bridge_con{i};
                Matrix* bridge_control_point = NULL;
                if (output->P_bridge_con && i < output->P_bridge_con->layer_count) {
                    bridge_control_point = get_layer(output->P_bridge_con, i);
                }
                
                // Get bridge orientation control points
                // Equivalent to MATLAB: control_point_ori=P_bridge_con_ori{i};
                Matrix* bridge_control_point_ori = NULL;
                if (output->P_bridge_con_ori && i < output->P_bridge_con_ori->layer_count) {
                    bridge_control_point_ori = get_layer(output->P_bridge_con_ori, i);
                }
                
                // Set parameters for bridge segment
                double nik_u = 0.0;
                double end_u = 2.0;
                
                // Get transition velocity
                // Equivalent to MATLAB: V0=V_B(i+1);
                double bridge_V0 = 0.0;
                if (output->V_B && i + 1 < output->V_B_size) {
                    bridge_V0 = output->V_B[i + 1];
                }
                
                Matrix* bridge_x0 = NULL;
                Matrix* bridge_y0 = NULL;
                Matrix* bridge_z0 = NULL;
                
                // Call the Velocity_Taylor_1 function
                Matrix* bridge_U = NULL;
                if (bridge_control_point) {
                    Velocity_Taylor_1(bridge_V0, bridge_control_point, nik_u, end_u,
                                    &bridge_U, &bridge_x0, &bridge_y0, &bridge_z0);
                }

                // Use bridge_U->cols instead of bridge_num
                int bridge_num = bridge_U ? bridge_U->cols : 0;

                // Convert U matrix to array for Velocity_Bspline_u
                double* U_array = NULL;
                if (bridge_U) {
                    U_array = (double*)malloc(bridge_num * sizeof(double));
                    for (int j = 0; j < bridge_num; j++) {
                        U_array[j] = get_matrix_element(bridge_U, 0, j);
                    }
                }

                // Calculate orientation trajectory using Velocity_Bspline_u
                Matrix* Nikx_ori = NULL;
                Matrix* Niky_ori = NULL;
                Matrix* Nikz_ori = NULL;
                Matrix* p_u_x_ori = NULL;
                Matrix* p_u_y_ori = NULL;
                Matrix* p_u_z_ori = NULL;

                // Call Velocity_Bspline_u for orientation
                if (bridge_control_point_ori) {
                    Velocity_Bspline_u(bridge_control_point_ori, U_array, bridge_num,
                                     &Nikx_ori, &Niky_ori, &Nikz_ori,
                                     &p_u_x_ori, &p_u_y_ori, &p_u_z_ori);
                }

                // Create orientation matrix by combining p_u_x_ori, p_u_y_ori, and p_u_z_ori
                Matrix* ori = create_matrix(p_u_x_ori->rows, 3);
                if (ori) {
                    for (int j = 0; j < p_u_x_ori->rows; j++) {
                        set_matrix_element(ori, j, 0, get_matrix_element(p_u_x_ori, j, 0));
                        set_matrix_element(ori, j, 1, get_matrix_element(p_u_y_ori, j, 0));
                        set_matrix_element(ori, j, 2, get_matrix_element(p_u_z_ori, j, 0));
                    }
                }

                // Free temporary matrices and array
                free(U_array);
                free_matrix(Nikx_ori);
                free_matrix(Niky_ori);
                free_matrix(Nikz_ori);
                free_matrix(p_u_x_ori);
                free_matrix(p_u_y_ori);
                free_matrix(p_u_z_ori);

                // Create B_position matrix from bridge_x0, bridge_y0, bridge_z0
                Matrix* B_position = create_matrix(bridge_num, 3);
                if (B_position) {
                    for (int j = 0; j < bridge_num; j++) {
                        set_matrix_element(B_position, j, 0, get_matrix_element(bridge_x0, j, 0));
                        set_matrix_element(B_position, j, 1, get_matrix_element(bridge_y0, j, 0));
                        set_matrix_element(B_position, j, 2, get_matrix_element(bridge_z0, j, 0));
                    }
                }

                // Store B_position and ori in B_pos and B_ori arrays
                // Equivalent to MATLAB: B_pos{i}=B_position; B_ori{i}=ori;
                if (B_position && ori) {
                    Matrix* B_position_copy = copy_matrix(B_position);
                    Matrix* ori_copy = copy_matrix(ori);
                    
                    if (B_position_copy && ori_copy) {
                        // Append to B_pos and B_ori arrays
                        if (output->B_pos) {
                            append_layer_flexible(output->B_pos, B_position_copy);
                        }
                        
                        if (output->B_ori) {
                            append_layer_flexible(output->B_ori, ori_copy);
                        }
                    } else {
                        if (B_position_copy) free_matrix(B_position_copy);
                        if (ori_copy) free_matrix(ori_copy);
                    }
                }

                // Append B_position and ori to poss and ori_chabu
                // Equivalent to MATLAB: poss=[poss;B_position]; ori_chabu=[ori_chabu;ori];
                if (B_position && ori) {
                    if (output->poss) {
                        Matrix* new_poss = combine_matrices_vertically(output->poss, B_position);
                        if (new_poss) {
                            free_matrix(output->poss);
                            output->poss = new_poss;
                        }
                    } else {
                        output->poss = copy_matrix(B_position);
                    }
                    
                    if (output->ori_chabu) {
                        Matrix* new_ori_chabu = combine_matrices_vertically(output->ori_chabu, ori);
                        if (new_ori_chabu) {
                            free_matrix(output->ori_chabu);
                            output->ori_chabu = new_ori_chabu;
                        }
                    } else {
                        output->ori_chabu = copy_matrix(ori);
                    }
                }

                // Clean up bridge matrices
                if (B_position) free_matrix(B_position);
                if (ori) free_matrix(ori);

                // Clean up
                if (bridge_control_point) free_matrix(bridge_control_point);
                if (bridge_control_point_ori) free_matrix(bridge_control_point_ori);
            }
  
        }

        // Free 'ori' after all segments are processed, if it was created
        if (ori) free_matrix(ori);

        // Initialize Z_pos and Z_ori matrices
        // Equivalent to MATLAB: Z_pos=[]; Z_ori=[];
        Matrix* Z_pos = create_matrix(0, 3);
        Matrix* Z_ori = create_matrix(0, 3);

        // Process each segment pair
        // Equivalent to MATLAB: for i=1:length(B_pos)
        if (output->A_pos && output->B_pos && output->A_ori && output->B_ori) {
            int num_segments = output->B_pos->layer_count;
            
            for (int i = 0; i < num_segments; i++) {
                // Get matrices from cell arrays
                // Equivalent to MATLAB: p1=A_pos{i}; p2=B_pos{i}; o1=A_ori{i}; o2=B_ori{i};
                Matrix* p1 = get_layer(output->A_pos, i);
                Matrix* p2 = get_layer(output->B_pos, i);
                Matrix* o1 = get_layer(output->A_ori, i);
                Matrix* o2 = get_layer(output->B_ori, i);

                if (p1 && p2 && o1 && o2) {
                    // Remove last row from each matrix
                    // Equivalent to MATLAB: p1(end,:)=[]; p2(end,:)=[]; o1(end,:)=[]; o2(end,:)=[];
                    Matrix* p1_trimmed = create_matrix(p1->rows - 1, p1->cols);
                    Matrix* p2_trimmed = create_matrix(p2->rows - 1, p2->cols);
                    Matrix* o1_trimmed = create_matrix(o1->rows - 1, o1->cols);
                    Matrix* o2_trimmed = create_matrix(o2->rows - 1, o2->cols);

                    // Copy all rows except the last one
                    for (int j = 0; j < p1->rows - 1; j++) {
                        for (int k = 0; k < p1->cols; k++) {
                            set_matrix_element(p1_trimmed, j, k, get_matrix_element(p1, j, k));
                        }
                    }
                    for (int j = 0; j < p2->rows - 1; j++) {
                        for (int k = 0; k < p2->cols; k++) {
                            set_matrix_element(p2_trimmed, j, k, get_matrix_element(p2, j, k));
                        }
                    }
                    for (int j = 0; j < o1->rows - 1; j++) {
                        for (int k = 0; k < o1->cols; k++) {
                            set_matrix_element(o1_trimmed, j, k, get_matrix_element(o1, j, k));
                        }
                    }
                    for (int j = 0; j < o2->rows - 1; j++) {
                        for (int k = 0; k < o2->cols; k++) {
                            set_matrix_element(o2_trimmed, j, k, get_matrix_element(o2, j, k));
                        }
                    }

                    // Combine matrices vertically
                    // Equivalent to MATLAB: Z_pos=[Z_pos;p1;p2]; Z_ori=[Z_ori;o1;o2];
                    Matrix* new_Z_pos = NULL;
                    Matrix* temp_Z_pos = NULL;
                    if (Z_pos->rows == 0) {
                        // First combination
                        temp_Z_pos = combine_matrices_vertically(p1_trimmed, p2_trimmed);
                        if (temp_Z_pos) {
                            free_matrix(Z_pos);
                            Z_pos = temp_Z_pos;
                        }
                    } else {
                        // Combine with existing Z_pos
                        temp_Z_pos = combine_matrices_vertically(Z_pos, p1_trimmed);
                        if (temp_Z_pos) {
                            new_Z_pos = combine_matrices_vertically(temp_Z_pos, p2_trimmed);
                            free_matrix(Z_pos);
                            free_matrix(temp_Z_pos);
                            Z_pos = new_Z_pos;
                        }
                    }

                    Matrix* new_Z_ori = NULL;
                    Matrix* temp_Z_ori = NULL;
                    if (Z_ori->rows == 0) {
                        // First combination
                        temp_Z_ori = combine_matrices_vertically(o1_trimmed, o2_trimmed);
                        if (temp_Z_ori) {
                            free_matrix(Z_ori);
                            Z_ori = temp_Z_ori;
                        }
                    } else {
                        // Combine with existing Z_ori
                        temp_Z_ori = combine_matrices_vertically(Z_ori, o1_trimmed);
                        if (temp_Z_ori) {
                            new_Z_ori = combine_matrices_vertically(temp_Z_ori, o2_trimmed);
                            free_matrix(Z_ori);
                            free_matrix(temp_Z_ori);
                            Z_ori = new_Z_ori;
                        }
                    }

                    // Clean up trimmed matrices
                    free_matrix(p1_trimmed);
                    free_matrix(p2_trimmed);
                    free_matrix(o1_trimmed);
                    free_matrix(o2_trimmed);
                }

                // Clean up original matrices
                free_matrix(p1);
                free_matrix(p2);
                free_matrix(o1);
                free_matrix(o2);
            }
        }

        // Process the last segment
        // Equivalent to MATLAB: p1=A_pos{end}; o1=A_ori{end};
        if (output->A_pos && output->A_ori) {
            // Get the last matrices from A_pos and A_ori
            Matrix* p1 = get_layer(output->A_pos, output->A_pos->layer_count - 1);
            Matrix* o1 = get_layer(output->A_ori, output->A_ori->layer_count - 1);

            if (p1 && o1) {
                // Combine with Z_pos and Z_ori
                // Equivalent to MATLAB: Z_pos=[Z_pos;p1]; Z_ori=[Z_ori;o1];
                Matrix* new_Z_pos = combine_matrices_vertically(Z_pos, p1);
                Matrix* new_Z_ori = combine_matrices_vertically(Z_ori, o1);

                if (new_Z_pos) {
                    free_matrix(Z_pos);
                    Z_pos = new_Z_pos;
                }
                if (new_Z_ori) {
                    free_matrix(Z_ori);
                    Z_ori = new_Z_ori;
                }
////打印z_ori矩阵
//printf("z_ori:\n");
//for (int i = 0; i < Z_ori->rows; i++) {
//    for (int j = 0; j < Z_ori->cols; j++) {
//        printf("%f ", get_matrix_element(Z_ori, i, j));
//    }
//    printf("\n");
//}

                // Clean up
                free_matrix(p1);
                free_matrix(o1);
            }
        }

        // Process Z_ori angles to be within -180 to 180 degrees range
        // Equivalent to MATLAB: for i=1:size(Z_ori,1)
        if (Z_ori) {
            for (int i = 0; i < Z_ori->rows; i++) {
                // Process first angle (Z_ori(i,1))
                double angle1 = get_matrix_element(Z_ori, i, 0);
                if (angle1 > 180.0) {
                    set_matrix_element(Z_ori, i, 0, angle1 - 360.0);
                } else if (angle1 < -180.0) {
                    set_matrix_element(Z_ori, i, 0, angle1 + 360.0);
                }

                // Process second angle (Z_ori(i,2))
                double angle2 = get_matrix_element(Z_ori, i, 1);
                if (angle2 > 180.0) {
                    set_matrix_element(Z_ori, i, 1, angle2 - 360.0);
                } else if (angle2 < -180.0) {
                    set_matrix_element(Z_ori, i, 1, angle2 + 360.0);
                }

                // Process third angle (Z_ori(i,3))
                double angle3 = get_matrix_element(Z_ori, i, 2);
                if (angle3 > 180.0) {
                    set_matrix_element(Z_ori, i, 2, angle3 - 360.0);
                } else if (angle3 < -180.0) {
                    set_matrix_element(Z_ori, i, 2, angle3 + 360.0);
                }
            }
        }

        // Combine Z_pos and Z_ori into Z_inter as nx6 matrix
        // Equivalent to MATLAB: Z_inter=[Z_pos,Z_ori];
        if (Z_pos && Z_ori && Z_pos->rows == Z_ori->rows) {
            // Each input matrix is nx3, output will be nx6
            int n = Z_pos->rows;
            output->Z_inter_size = n * 6;  // n rows x 6 columns
            output->Z_inter = (double*)malloc(output->Z_inter_size * sizeof(double));
            
            if (output->Z_inter) {
                for (int i = 0; i < n; i++) {
                    // Copy Z_pos row (first 3 columns)
                    output->Z_inter[i * 6 + 0] = get_matrix_element(Z_pos, i, 0);
                    output->Z_inter[i * 6 + 1] = get_matrix_element(Z_pos, i, 1);
                    output->Z_inter[i * 6 + 2] = get_matrix_element(Z_pos, i, 2);
                    
                    // Copy Z_ori row (last 3 columns)
                    output->Z_inter[i * 6 + 3] = get_matrix_element(Z_ori, i, 0);
                    output->Z_inter[i * 6 + 4] = get_matrix_element(Z_ori, i, 1);
                    output->Z_inter[i * 6 + 5] = get_matrix_element(Z_ori, i, 2);
                }

            }  
        }

        // Store the results in the output structure
        if (Z_pos) {
            output->Z_pos = Z_pos;
        }
        if (Z_ori) {
            output->Z_ori = Z_ori;
        }
    
    }
      // Convert orientation angles from radians to degrees
    // Equivalent to MATLAB: Z_inter(:,4:6)=Z_inter(:,4:6)*180/pi;
    if (output->Z_inter) {
        int n = output->Z_inter_size / 6;  // number of rows
        for (int i = 0; i < n; i++) {
            // Convert columns 4,5,6 (indices 3,4,5) from radians to degrees
            output->Z_inter[i * 6 + 3] *= 180.0 / M_PI;
            output->Z_inter[i * 6 + 4] *= 180.0 / M_PI;
            output->Z_inter[i * 6 + 5] *= 180.0 / M_PI;
        }
    }

    // Convert orientation angles to quaternions
    // Equivalent to MATLAB: for i=1:size(Z_ori,1) Q_ori=R3toS3(Z_ori(i,1),Z_ori(i,2),Z_ori(i,3));
    if (output->Z_ori) {
        // Initialize RRR array
        output->RRR_size = output->Z_ori->rows;
        output->RRR = (RotationMatrix*)malloc(output->RRR_size * sizeof(RotationMatrix));
        if (!output->RRR) {
            fprintf(stderr, "Failed to allocate memory for rotation matrices\n");
            return -1;
        }

        for (int i = 0; i < output->Z_ori->rows; i++) {
            // Get orientation angles from Z_ori
            double angle1 = get_matrix_element(output->Z_ori, i, 0);  // Z_ori(i,1)
            double angle2 = get_matrix_element(output->Z_ori, i, 1);  // Z_ori(i,2)
            double angle3 = get_matrix_element(output->Z_ori, i, 2);  // Z_ori(i,3)
            
            // Convert to quaternion using R3toS3
            Quaternion_R3toS3 Q_ori = R3toS3(angle1, angle2, angle3);
            
            // Convert quaternion to rotation matrix directly
            Trans_quatoMatrix(Q_ori.w, Q_ori.x, Q_ori.y, Q_ori.z, &output->RRR[i]);

            // Convert rotation matrix to Euler angles (ZYX sequence)
            EulerAngles euler_angles;
            rotm2eul_deg(&output->RRR[i], &euler_angles);

            // Store Euler angles in Z_ori matrix in order [roll, pitch, yaw]
            // Note: Z_ori(i,:)=[OOO(3),OOO(2),OOO(1)] in MATLAB becomes
            // Z_ori storing [roll(X), pitch(Y), yaw(Z)]
            set_matrix_element(output->Z_ori, i, 0, euler_angles.x);  // roll (X) = OOO(3)
            set_matrix_element(output->Z_ori, i, 1, euler_angles.y);  // pitch (Y) = OOO(2)
            set_matrix_element(output->Z_ori, i, 2, euler_angles.z);  // yaw (Z) = OOO(1)
       
        }

        // 笛卡尔空间: Z_inter=[Z_pos,Z_ori]
        if (output->Z_pos && output->Z_ori && output->Z_pos->rows == output->Z_ori->rows) {
            // Allocate memory for Z_inter (nx6 matrix)
            int n = output->Z_pos->rows;
            output->Z_inter_size = n * 6;  // n rows x 6 columns
            output->Z_inter = (double*)malloc(output->Z_inter_size * sizeof(double));
            
            if (output->Z_inter) {
                for (int i = 0; i < n; i++) {
                    // Copy Z_pos values (first 3 columns)
                    output->Z_inter[i * 6 + 0] = get_matrix_element(output->Z_pos, i, 0);
                    output->Z_inter[i * 6 + 1] = get_matrix_element(output->Z_pos, i, 1);
                    output->Z_inter[i * 6 + 2] = get_matrix_element(output->Z_pos, i, 2);
                    
                    // Copy Z_ori values (last 3 columns)
                    output->Z_inter[i * 6 + 3] = get_matrix_element(output->Z_ori, i, 0);
                    output->Z_inter[i * 6 + 4] = get_matrix_element(output->Z_ori, i, 1);
                    output->Z_inter[i * 6 + 5] = get_matrix_element(output->Z_ori, i, 2);
                }
                save_position_inter_to_txt(output->Z_inter, output->Z_inter_size, 6, "z_inter.txt");

            }
            ////打印Z_inter矩阵
            printf("Z_inter:\n");
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < 6; j++) {
                    printf("%f  ", output->Z_inter[i * 6 + j]);
                }
                printf("\n");
            }
        }





        // P=Z_pos*0.001;
        // P1=P(1,:)';
        // T1=[RRR(:,:,1) P1];
        // T1=[T1;0 0 0 1];
        if (output->Z_pos && output->Z_pos->rows > 0 && output->RRR && output->RRR_size > 0) {
            // Create P = Z_pos * 0.001 (scale to meters)
            Matrix* P = create_matrix(output->Z_pos->rows, output->Z_pos->cols);
            if (P) {
                for (int i = 0; i < output->Z_pos->rows; i++) {
                    for (int j = 0; j < output->Z_pos->cols; j++) {
                        set_matrix_element(P, i, j, get_matrix_element(output->Z_pos, i, j) * 1);
                    }
                }
                
                // Get P1 (first row of P as a column vector)
                double P1[3] = {
                    get_matrix_element(P, 0, 0),
                    get_matrix_element(P, 0, 1),
                    get_matrix_element(P, 0, 2)
                };
                
                // Create T1 = [RRR(:,:,1) P1]
                output->T1 = create_matrix(4, 4); // 4x4 homogeneous transformation matrix
                if (output->T1) {
                    // Copy rotation matrix from RRR[0]
                    for (int i = 0; i < 3; i++) {
                        for (int j = 0; j < 3; j++) {
                            set_matrix_element(output->T1, i, j, output->RRR[0].matrix[i][j]);
                        }
                    }
                    
                    // Add P1 column
                    set_matrix_element(output->T1, 0, 3, P1[0]);
                    set_matrix_element(output->T1, 1, 3, P1[1]);
                    set_matrix_element(output->T1, 2, 3, P1[2]);
                    
                    // Add [0 0 0 1] row
                    set_matrix_element(output->T1, 3, 0, 0.0);
                    set_matrix_element(output->T1, 3, 1, 0.0);
                    set_matrix_element(output->T1, 3, 2, 0.0);
                    set_matrix_element(output->T1, 3, 3, 1.0);
                }
////打印T1矩阵
//printf("T1:\n");
//for (int i = 0; i < 4; i++) {
//    for (int j = 0; j < 4; j++) {
//        printf("%f ", get_matrix_element(output->T1, i, j));
//    }
//}

                // T_ET=[0.939427912228202	-0.0237342489540220	0.341923797289908	0.151507000000000
                // -0.0223028986019901	-0.999718303036705	-0.00811759122898171	0.0217120000000000
                // 0.342020143325669	0	-0.939692620785908	0.349984000000000
                // 0	0	0	1];
                // T1=T1*T_ET^-1;
                // h=[0 0 0 0 0 0];
                
                // Create the T_ET matrix
                Matrix* T_ET = create_matrix(4, 4);
                if (T_ET) {
                    // First row
                    set_matrix_element(T_ET, 0, 0, 0.939427912228202);
                    set_matrix_element(T_ET, 0, 1, -0.0237342489540220);
                    set_matrix_element(T_ET, 0, 2, 0.341923797289908);
                    set_matrix_element(T_ET, 0, 3, 151.507000000000);
                    
                    // Second row
                    set_matrix_element(T_ET, 1, 0, -0.0223028986019901);
                    set_matrix_element(T_ET, 1, 1, -0.999718303036705);
                    set_matrix_element(T_ET, 1, 2, -0.00811759122898171);
                    set_matrix_element(T_ET, 1, 3, 21.7120000000000);
                    
                    // Third row
                    set_matrix_element(T_ET, 2, 0, 0.342020143325669);
                    set_matrix_element(T_ET, 2, 1, 0.0);
                    set_matrix_element(T_ET, 2, 2, -0.939692620785908);
                    set_matrix_element(T_ET, 2, 3, 349.984000000000);
                    
                    // Fourth row
                    set_matrix_element(T_ET, 3, 0, 0.0);
                    set_matrix_element(T_ET, 3, 1, 0.0);
                    set_matrix_element(T_ET, 3, 2, 0.0);
                    set_matrix_element(T_ET, 3, 3, 1.0);
                    
                    // Calculate inverse of T_ET
                    Matrix* T_ET_inv = invert_matrix(T_ET);
                    if (T_ET_inv) {
                        // Calculate T1 = T1 * T_ET^-1
                        Matrix* result = multiply_matrices(output->T1, T_ET_inv);
                        if (result) {
                            // Scale the translation part (4th column, rows 1-3) by 0.001
                            for (int i = 0; i < 3; i++) {
                                double scaled_value = get_matrix_element(result, i, 3) * 0.001;
                                set_matrix_element(result, i, 3, scaled_value);
                            }

                            free_matrix(output->T1);
                            output->T1 = result;
                        }
                        free_matrix(T_ET_inv);
                    }
                    free_matrix(T_ET);
                }
                // Create h = [0 0 0 0 0 0]
                output->h = (double*)malloc(6 * sizeof(double));
                if (output->h) {
                    for (int i = 0; i < 6; i++) {
                        output->h[i] = 0.0;
                    }
                }
                
                // Calculate inverse kinematics: [Theta,~,~] = QC_Inverse_Kinematics_CCR10_1450_SDH(T1,h)
                output->Theta = (double*)malloc(6 * sizeof(double));
                double* Valid_Theta = (double*)malloc(48 * sizeof(double)); // 8 possible solutions × 6 joints
                double* theta_all = (double*)malloc(48 * sizeof(double));
                
                if (output->Theta && Valid_Theta && theta_all && output->T1 && output->h) {
                    // Convert T1 matrix to array for the inverse kinematics function
                    double* T1_array = (double*)malloc(16 * sizeof(double));
                    if (T1_array) {
                        for (int i = 0; i < 4; i++) {
                            for (int j = 0; j < 4; j++) {
                                T1_array[i * 4 + j] = get_matrix_element(output->T1, i, j);
                            }
                        }
                        
                        // Call the inverse kinematics function
                        QC_Inverse_Kinematics_SDH(T1_array, output->h, output->Theta, Valid_Theta, theta_all);
                        
                        // Convert Theta from radians to degrees: Theta = Theta * 180/pi
                        for (int i = 0; i < 6; i++) {
                            output->Theta[i] = output->Theta[i] * 180.0 / M_PI;
                        }
                        
                        free(T1_array);
                    } else {
                        free(output->Theta);
                        output->Theta = NULL;
                    }
                } else {
                    if (output->Theta) free(output->Theta);
                    output->Theta = NULL;
                }
                

                
                // Clean up temporary arrays
                if (Valid_Theta) free(Valid_Theta);
                if (theta_all) free(theta_all);
                
                // Now calculate joint angles for all trajectory points
                // for i=1:size(Z_ori,1)
                //    P2=P(i,:)';
                //    T=[RRR(:,:,i) P2];
                //    T=[T;0 0 0 1];
                //    T=T*T_ET^-1;     
                //    [Theta,~,~] = QC_Inverse_Kinematics_CCR10_1450_SDH(T,Theta);
                //    Theta=Theta*180/pi;
                //    theta(i,:)=Theta;
                // end
                
                if (output->Z_pos && output->Z_pos->rows > 0 && output->RRR && output->RRR_size > 0) {
                    int num_points = output->Z_pos->rows;
                    
                    // Create matrix to store joint angles for all points
                    output->theta_trajectory = create_matrix(num_points, 6);
                    
                    if (output->theta_trajectory) {
                        // Create T_ET matrix for inverse calculation
                        Matrix* T_ET = create_matrix(4, 4);
                        if (T_ET) {
                            // First row
                            set_matrix_element(T_ET, 0, 0, 0.939427912228202);
                            set_matrix_element(T_ET, 0, 1, -0.0237342489540220);
                            set_matrix_element(T_ET, 0, 2, 0.341923797289908);
                            set_matrix_element(T_ET, 0, 3, 151.507000000000);

                            // Second row
                            set_matrix_element(T_ET, 1, 0, -0.0223028986019901);
                            set_matrix_element(T_ET, 1, 1, -0.999718303036705);
                            set_matrix_element(T_ET, 1, 2, -0.00811759122898171);
                            set_matrix_element(T_ET, 1, 3, 21.7120000000000);

                            // Third row
                            set_matrix_element(T_ET, 2, 0, 0.342020143325669);
                            set_matrix_element(T_ET, 2, 1, 0.0);
                            set_matrix_element(T_ET, 2, 2, -0.939692620785908);
                            set_matrix_element(T_ET, 2, 3, 349.984000000000);

                            // Fourth row
                            set_matrix_element(T_ET, 3, 0, 0.0);
                            set_matrix_element(T_ET, 3, 1, 0.0);
                            set_matrix_element(T_ET, 3, 2, 0.0);
                            set_matrix_element(T_ET, 3, 3, 1.0);
                            
                            // Calculate inverse of T_ET
                            Matrix* T_ET_inv = invert_matrix(T_ET);
                            if (T_ET_inv) {
                                // Process each point in the trajectory
                                for (int i = 0; i < num_points; i++) {
                                    // Allocate memory for temporary variables
                                    double* point_Theta = (double*)malloc(6 * sizeof(double));
                                    double* Valid_Theta = (double*)malloc(48 * sizeof(double));
                                    double* theta_all = (double*)malloc(48 * sizeof(double));
                                    double* h = (double*)malloc(6 * sizeof(double));
                                    
                             
                                    
                                    if (point_Theta && Valid_Theta && theta_all && h) {
                                        // Create transformation matrix T = [RRR(:,:,i) P2; 0 0 0 1]
                                        Matrix* T = create_matrix(4, 4);
                                        if (T) {
                                            // Copy rotation matrix from RRR[i]
                                            for (int r = 0; r < 3; r++) {
                                                for (int c = 0; c < 3; c++) {
                                                    set_matrix_element(T, r, c, output->RRR[i].matrix[r][c]);
                                                }
                                            }
                                            
                                            // Add position column P2 = P(i,:)'
                                            double P2[3];
                                            P2[0] = get_matrix_element(output->Z_pos, i, 0) * 1; // Scale to meters
                                            P2[1] = get_matrix_element(output->Z_pos, i, 1) * 1;
                                            P2[2] = get_matrix_element(output->Z_pos, i, 2) * 1;
                                            
                                            set_matrix_element(T, 0, 3, P2[0]);
                                            set_matrix_element(T, 1, 3, P2[1]);
                                            set_matrix_element(T, 2, 3, P2[2]);
                                            
                                            // Add [0 0 0 1] row
                                            set_matrix_element(T, 3, 0, 0.0);
                                            set_matrix_element(T, 3, 1, 0.0);
                                            set_matrix_element(T, 3, 2, 0.0);
                                            set_matrix_element(T, 3, 3, 1.0);
                                            
                                            // Calculate T = T * T_ET^-1
                                            Matrix* T_final = multiply_matrices(T, T_ET_inv);
                                            if (T_final) {

                                                for (int r = 0; r < 3; r++) {
                                                    double scaled_value = get_matrix_element(T_final, r, 3) * 0.001;
                                                    set_matrix_element(T_final, r, 3, scaled_value);
                                                }
                                                // Convert matrix to array for QC_Inverse_Kinematics_SDH
                                                double* T_array = (double*)malloc(16 * sizeof(double));
                                                if (T_array) {
                                                    for (int r = 0; r < 4; r++) {
                                                        for (int c = 0; c < 4; c++) {
                                                            T_array[r * 4 + c] = get_matrix_element(T_final, r, c);
                                                        }
                                                    }
                                                    ////打印T_array
                                                    //printf("T_array111111111111111111111111111111111111111111111:\n");
                                                    //for (int i = 0; i < 4; i++) {
                                                    //    for (int j = 0; j < 4; j++) {
                                                    //        printf("%f ", T_array[i * 4 + j]);
                                                    //    }
                                                    //    printf("\n");
                                                    //    }

                                                    ////打印Theta
                                                    //printf("Theta:\n");
                                                    //for (int i = 0; i < 6; i++) {
                                                    //    printf("%f ", output->Theta[i]);
                                                    //}
                                                    //printf("\n");
                                                   

                                                    
                                                    // Calculate point_Theta and convert to degrees
                                                    QC_Inverse_Kinematics_SDH(T_array, output->Theta, point_Theta, Valid_Theta, theta_all);
                                                    
                                                    // Convert point_Theta to degrees and update Theta for next iteration
                                                    for (int j = 0; j < 6; j++) {
                                                        point_Theta[j] = point_Theta[j] * 180.0 / M_PI;
                                                        // Update Theta for next iteration
                                                        output->Theta[j] = point_Theta[j];
                                                    }
                                                    
                                                    // Convert to degrees and store in theta_trajectory matrix
                                                    for (int j = 0; j < 6; j++) {
                                                        double angle_deg = point_Theta[j];
                                                        set_matrix_element(output->theta_trajectory, i, j, angle_deg);
                                                    }
                                                    ////打印theta_trajectory矩阵
                                                    //printf("theta_trajectory:\n");
                                                    //    for (int j = 0; j < 6; j++) {
                                                    //        printf("%f ", get_matrix_element(output->theta_trajectory, i, j));
                                                    //    }
                                                    //    printf("\n");
                                                    //    printf("\n");
                                                    free(T_array);
                                                }
                                                free_matrix(T_final);
                                            }
                                            free_matrix(T);
                                        }
                                    }
                                    
                                    // Free temporary arrays
                                  //  free_matrix(P);
                                      if (point_Theta) free(point_Theta);
                                    if (Valid_Theta) free(Valid_Theta);
                                    if (theta_all) free(theta_all);
                                    if (h) free(h);
                                }
                                
                                free_matrix(T_ET_inv);
                            }
                            free_matrix(T_ET);
                        }
                    }
                }
                free_matrix(P);

                // Set position_inter = theta_trajectory
                if (output->theta_trajectory) {
                    int num_rows = output->theta_trajectory->rows;
                    int num_cols = output->theta_trajectory->cols;
                    
                    // Allocate memory for position_inter
                    if (output->position_inter) {
                        free(output->position_inter);
                    }
                    output->position_inter = (double*)malloc(num_rows * num_cols * sizeof(double));
                    output->position_inter_size = num_rows * num_cols;
                    
                    if (output->position_inter) {
                        // Copy data from theta_trajectory to position_inter
                        for (int i = 0; i < num_rows; i++) {
                            for (int j = 0; j < num_cols; j++) {
                                output->position_inter[i * num_cols + j] = get_matrix_element(output->theta_trajectory, i, j);
                            }
                        }
                        save_position_inter_to_txt(output->position_inter, output->position_inter_size, 6, "position_inter.txt");

                        //// Print position_inter
                        //printf("position_inter:\n");
                        //for (int i = 0; i < num_rows; i++) {
                        //    for (int j = 0; j < num_cols; j++) {
                        //        printf("%f ", output->position_inter[i * num_cols + j]);
                        //    }
                        //    printf("\n");
                        //}
                    }
                }
            }
        }

        
    }

    // Clean up
    free(pos);
    free(orient);
    free(orient_0);
    free(arclength);
    free(ori_diff);
    free(max_index);
    // Use the proper free function for CuniheResult
    free_cunihe_result(pos_result);
    free(control_points);
    free_uniform_cubic_result(&bSplineResult);

    // Free orientation trajectory planning matrices
    if (output->U_ori) {
        free_matrix(output->U_ori);
        output->U_ori = NULL;
    }
    if (output->ORI_con) {
        free_matrix(output->ORI_con);
        output->ORI_con = NULL;
    }

    // Free combined trajectory data
    if (output->Z_inter) {
        free(output->Z_inter);
        output->Z_inter = NULL;
    }
    output->Z_inter_size = 0;
    
    // Free transformation matrix
    if (output->T1) {
        free_matrix(output->T1);
        output->T1 = NULL;
    }
    
    // Free h array
    if (output->h) {
        free(output->h);
        output->h = NULL;
    }
    
    // Free Theta array
    if (output->Theta) {
        free(output->Theta);
        output->Theta = NULL;
    }
    
    // Free theta_trajectory matrix (we don't free it here because it's our output)
    // We let the caller free it when they're done with it using free_trajectory_planning_output

    output->rows = 0;

    // Free ARC matrices
    if (output->ARC_1) {
        free_matrix(output->ARC_1);
        output->ARC_1 = NULL;
    }
    if (output->ARC_2) {
        free_matrix(output->ARC_2);
        output->ARC_2 = NULL;
    }
    if (output->ARC_1_ori) {
        free_matrix(output->ARC_1_ori);
        output->ARC_1_ori = NULL;
    }
    if (output->ARC_2_ori) {
        free_matrix(output->ARC_2_ori);
        output->ARC_2_ori = NULL;
    }

    // 只重置基本字段，不要重置fit_over_ori和fit_over_pos相关字段
    output->cols = 0;
    output->K_size = 0;
    output->K_max_size = 0;
    output->position_inter_size = 0;
    output->jizhi_size = 0;

    

    return 0;
}

void free_trajectory_planning_output(TrajectoryPlanningOutput* output) {
    if (output) {
        // Free RRR array
        if (output->RRR) {
            free(output->RRR);
            output->RRR = NULL;
            output->RRR_size = 0;
        }

        // Free velocity planning structures
        if (output->poss) {
            free_matrix(output->poss);
            output->poss = NULL;
        }
        if (output->ori_chabu) {
            free_matrix(output->ori_chabu);
            output->ori_chabu = NULL;
        }
        if (output->A_pos) {
            free_3d(output->A_pos);
            output->A_pos = NULL;
        }
        if (output->B_pos) {
            free_3d(output->B_pos);
            output->B_pos = NULL;
        }
        if (output->A_ori) {
            free_3d(output->A_ori);
            output->A_ori = NULL;
        }
        if (output->B_ori) {
            free_3d(output->B_ori);
            output->B_ori = NULL;
        }
        if (output->UU) {
            free_3d(output->UU);
            output->UU = NULL;
        }

        // Free combined position and orientation matrices
        if (output->Z_pos) {
            free_matrix(output->Z_pos);
            output->Z_pos = NULL;
        }
        if (output->Z_ori) {
            free_matrix(output->Z_ori);
            output->Z_ori = NULL;
        }
        
        // Free theta_trajectory matrix
        if (output->theta_trajectory) {
            free_matrix(output->theta_trajectory);
            output->theta_trajectory = NULL;
        }

        // Free first segment control points
        if (output->P1_CX) {
            free_matrix(output->P1_CX);
            output->P1_CX = NULL;
        }
        if (output->P1_CY) {
            free_matrix(output->P1_CY);
            output->P1_CY = NULL;
        }
        if (output->P1_CZ) {
            free_matrix(output->P1_CZ);
            output->P1_CZ = NULL;
        }
        if (output->P1_oriX) {
            free_matrix(output->P1_oriX);
            output->P1_oriX = NULL;
        }
        if (output->P1_oriY) {
            free_matrix(output->P1_oriY);
            output->P1_oriY = NULL;
        }
        if (output->P1_oriZ) {
            free_matrix(output->P1_oriZ);
            output->P1_oriZ = NULL;
        }

        // Free control point
        if (output->control_point) {
            free_matrix(output->control_point);
            output->control_point = NULL;
        }

        // Free transition velocity arrays
        if (output->V0) {
            free(output->V0);
            output->V0 = NULL;
        }
        if (output->V1) {
            free(output->V1);
            output->V1 = NULL;
        }
        if (output->V_B) {
            free(output->V_B);
            output->V_B = NULL;
        }
        output->V_size = 0;
        output->V_B_size = 0;

        if (output->fitting_points) {
            free(output->fitting_points);
            output->fitting_points = NULL;
        }
        if (output->K) {
            free(output->K);
            output->K = NULL;
        }
        if (output->K_max) {
            free(output->K_max);
            output->K_max = NULL;
        }
        if (output->position_inter) {
            free(output->position_inter);
            output->position_inter = NULL;
        }
        if (output->jizhi) {
            free(output->jizhi);
            output->jizhi = NULL;
        }

        // Free the new position matrices
        if (output->POS) {
            free_matrix(output->POS);
            output->POS = NULL;
        }
        if (output->POS_ori) {
            free_matrix(output->POS_ori);
            output->POS_ori = NULL;
        }

        // Free the cell arrays (Matrix3D structures)
        if (output->fit_over_pos) {
            free_3d(output->fit_over_pos);
            output->fit_over_pos = NULL;
        }
        if (output->fit_over_pos_Nikx) {
            free_3d(output->fit_over_pos_Nikx);
            output->fit_over_pos_Nikx = NULL;
        }
        if (output->fit_over_pos_Niky) {
            free_3d(output->fit_over_pos_Niky);
            output->fit_over_pos_Niky = NULL;
        }
        if (output->fit_over_pos_Nikz) {
            free_3d(output->fit_over_pos_Nikz);
            output->fit_over_pos_Nikz = NULL;
        }
        if (output->fit_over_pos_X) {
            free_3d(output->fit_over_pos_X);
            output->fit_over_pos_X = NULL;
        }

        // Free the orientation cell arrays (Matrix3D structures)
        if (output->fit_over_ori) {
            free_3d(output->fit_over_ori);
            output->fit_over_ori = NULL;
        }
        if (output->fit_over_ori_Nikx) {
            free_3d(output->fit_over_ori_Nikx);
            output->fit_over_ori_Nikx = NULL;
        }
        if (output->fit_over_ori_Niky) {
            free_3d(output->fit_over_ori_Niky);
            output->fit_over_ori_Niky = NULL;
        }
        if (output->fit_over_ori_Nikz) {
            free_3d(output->fit_over_ori_Nikz);
            output->fit_over_ori_Nikz = NULL;
        }
        if (output->fit_over_ori_X) {
            free_3d(output->fit_over_ori_X);
            output->fit_over_ori_X = NULL;
        }

        // Free orientation trajectory planning matrices
        if (output->U_ori) {
            free_matrix(output->U_ori);
            output->U_ori = NULL;
        }
        if (output->ORI_con) {
            free_matrix(output->ORI_con);
            output->ORI_con = NULL;
        }

        // Free combined trajectory data
        if (output->Z_inter) {
            free(output->Z_inter);
            output->Z_inter = NULL;
        }
        output->Z_inter_size = 0;

        output->rows = 0;
        output->cols = 0;
        output->K_size = 0;
        output->K_max_size = 0;
        output->position_inter_size = 0;
        output->jizhi_size = 0;
        }
    }



