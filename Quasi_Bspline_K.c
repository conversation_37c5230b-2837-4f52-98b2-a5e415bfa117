#include "Quasi_Bspline_K.h"
#include <math.h>

static Vector3D cross_product(Vector3D a, Vector3D b) {
    Vector3D result;
    result.x = a.y * b.z - a.z * b.y;
    result.y = a.z * b.x - a.x * b.z;
    result.z = a.x * b.y - a.y * b.x;
    return result;
}

static double norm(Vector3D v) {
    return sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
}

// Add a high precision norm calculation
static double high_precision_norm(Vector3D v) {
    // Compute with higher precision to avoid early rounding
    double x2 = v.x * v.x;
    double y2 = v.y * v.y;
    double z2 = v.z * v.z;
    
    // Use extended precision for the sum
    long double sum = (long double)x2 + (long double)y2 + (long double)z2;
    
    // Return the square root with full precision
    return sqrtl(sum);
}

void Quasi_Bspline_K(double Nik[4][3], double* uu, int uu_length,
    double* K, Vector3D* D1, Vector3D* D2,
    Vector3D* N, Vector3D* Bt) {
    double P_d[4][3], P_dd[4][3];

    // ���㵼������
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 3; j++) {
            P_d[i][j] = (i == 1) ? Nik[i][j] :
                (i == 2) ? 2 * Nik[i][j] :
                (i == 3) ? 3 * Nik[i][j] : 0;

            P_dd[i][j] = (i == 2) ? 2 * Nik[i][j] :
                (i == 3) ? 6 * Nik[i][j] : 0;
        }
    }

    for (int j = 0; j < uu_length; j++) {
        double u = uu[j];
        double U_d[4] = { 0, 1, u, u * u };
        double U_dd[4] = { 0, 0, 1, u };

        // ����r_d��r_dd
        Vector3D r_d = { 0 }, r_dd = { 0 };
        for (int k = 0; k < 4; k++) {
            r_d.x += U_d[k] * P_d[k][0];
            r_d.y += U_d[k] * P_d[k][1];
            r_d.z += U_d[k] * P_d[k][2];

            r_dd.x += U_dd[k] * P_dd[k][0];
            r_dd.y += U_dd[k] * P_dd[k][1];
            r_dd.z += U_dd[k] * P_dd[k][2];
        }

        D1[j] = r_d;
        D2[j] = r_dd;

        Vector3D a = cross_product(r_d, r_dd);
        // Use high precision norm calculation for curvature
        double r_norm = high_precision_norm(r_d);
        // Compute K with higher precision
        double a_norm = high_precision_norm(a);
        K[j] = a_norm / (r_norm * r_norm * r_norm);

        // ��������ʸ
        double b_term = (r_d.x * r_d.x + r_d.y * r_d.y + r_d.z * r_d.z);
        Vector3D b = { b_term * r_dd.x, b_term * r_dd.y, b_term * r_dd.z };

        double c_term = (r_d.x * r_dd.x + r_d.y * r_dd.y + r_d.z * r_dd.z);
        Vector3D c = { c_term * r_d.x, c_term * r_d.y, c_term * r_d.z };

        double d = r_norm * norm(a);
        N[j].x = (b.x - c.x) / d;
        N[j].y = (b.y - c.y) / d;
        N[j].z = (b.z - c.z) / d;

        // ���㸱��ʸ
        Bt[j] = cross_product(r_d, N[j]);
    }
}