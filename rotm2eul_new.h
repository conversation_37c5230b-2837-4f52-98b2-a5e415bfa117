#ifndef ROTM2EUL_NEW_H
#define ROTM2EUL_NEW_H

#include "Trans_quatoMatrix_new.h"  // For RotationMatrix definition

// Structure to hold Euler angles
typedef struct {
    double z;  // Z rotation (yaw)
    double y;  // Y rotation (pitch)
    double x;  // X rotation (roll)
} EulerAngles;

// Function to convert rotation matrix to ZYX Euler angles (in radians)
void rotm2eul(const RotationMatrix* R, EulerAngles* angles);

// Function to convert rotation matrix to ZYX Euler angles (in degrees)
void rotm2eul_deg(const RotationMatrix* R, EulerAngles* angles);

#endif 