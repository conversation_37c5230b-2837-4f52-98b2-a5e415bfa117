#include <stdio.h>
#include "rotm2eul_new.h"
#include "Trans_quatoMatrix_new.h"

// Function to print rotation matrix
void print_rotation_matrix(const RotationMatrix* R) {
    printf("Rotation Matrix:\n");
    for (int i = 0; i < 3; i++) {
        printf("[");
        for (int j = 0; j < 3; j++) {
            printf("%8.4f", R->matrix[i][j]);
            if (j < 2) printf(", ");
        }
        printf("]\n");
    }
}

// Function to print Euler angles
void print_euler_angles(const EulerAngles* angles) {
    printf("Euler Angles (ZYX, degrees):\n");
    printf("Z (yaw)   = %8.4f\n", angles->z);
    printf("Y (pitch) = %8.4f\n", angles->y);
    printf("X (roll)  = %8.4f\n", angles->x);
}

int main() {
    // Test case 1: Identity matrix (no rotation)
    RotationMatrix R1 = {{{1, 0, 0}, {0, 1, 0}, {0, 0, 1}}};
    
    // Test case 2: 90-degree rotation around Z axis
    RotationMatrix R2 = {{{0, -1, 0}, {1, 0, 0}, {0, 0, 1}}};
    
    // Test case 3: 45-degree rotation around Y axis
    double c45 = 0.7071067811865476;  // cos(45°)
    RotationMatrix R3 = {{{c45, 0, c45}, {0, 1, 0}, {-c45, 0, c45}}};
    
    // Test case 4: Gimbal lock case (90-degree pitch)
    RotationMatrix R4 = {{{0, 0, 1}, {0, 1, 0}, {-1, 0, 0}}};

    EulerAngles angles;
    
    // Test case 1
    printf("\n=== Test Case 1: Identity Matrix ===\n");
    print_rotation_matrix(&R1);
    rotm2eul_deg(&R1, &angles);
    print_euler_angles(&angles);

    // Test case 2
    printf("\n=== Test Case 2: 90° Z Rotation ===\n");
    print_rotation_matrix(&R2);
    rotm2eul_deg(&R2, &angles);
    print_euler_angles(&angles);

    // Test case 3
    printf("\n=== Test Case 3: 45° Y Rotation ===\n");
    print_rotation_matrix(&R3);
    rotm2eul_deg(&R3, &angles);
    print_euler_angles(&angles);

    // Test case 4
    printf("\n=== Test Case 4: Gimbal Lock Case ===\n");
    print_rotation_matrix(&R4);
    rotm2eul_deg(&R4, &angles);
    print_euler_angles(&angles);

    return 0;
} 