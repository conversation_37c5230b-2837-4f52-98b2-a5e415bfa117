#define _CRTDBG_MAP_ALLOC
#include <crtdbg.h>

#define _CRT_SECURE_NO_WARNINGS  // Add this at the top to disable deprecation warnings
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "Copy_2_of_Offline_Trajectory_Planning.h"
#include "matrix.h"
#include <time.h>  // ���ӵ��ļ�������include��

// Function to count the number of lines in a file
int count_lines(FILE* file) {
    int count = 0;
    char ch;
    
    // Reset to beginning of file
    rewind(file);
    
    while (!feof(file)) {
        ch = fgetc(file);
        if (ch == '\n') {
            count++;
        }
    }
    
    // If file doesn't end with a newline, add the last line
    if (count > 0 && ch != '\n') {
        count++;
    }
    
    // Reset to beginning of file
    rewind(file);
    return count;
}

// Function to read Raw_Data from a text file
void load_Raw_Data_from_file(double** Raw_Data, int* rows, int cols, const char* filename) {
    FILE* file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Error: Cannot open file %s\n", filename);
        exit(1);
    }

    // Count number of lines in file
    *rows = count_lines(file);
    printf("Found %d rows in data file\n", *rows);
    
    if (*rows <= 0) {
        fprintf(stderr, "Error: File is empty or invalid\n");
        fclose(file);
        exit(1);
    }

    // Allocate memory for the data
    *Raw_Data = (double*)malloc((*rows) * cols * sizeof(double));
    if (!*Raw_Data) {
        fprintf(stderr, "Error: Memory allocation failed\n");
        fclose(file);
        exit(1);
    }

    // Read data from file
    for (int i = 0; i < *rows; i++) {
        for (int j = 0; j < cols; j++) {
            #ifdef _MSC_VER
                // Use fscanf_s on Visual Studio
                if (fscanf_s(file, "%lf", &(*Raw_Data)[i * cols + j]) != 1) {
                    fprintf(stderr, "Error: Invalid data format at line %d, column %d\n", i + 1, j + 1);
                    free(*Raw_Data);
                    fclose(file);
                    exit(1);
                }
            #else
                // Use standard fscanf on other compilers
                if (fscanf(file, "%lf", &(*Raw_Data)[i * cols + j]) != 1) {
                    fprintf(stderr, "Error: Invalid data format at line %d, column %d\n", i + 1, j + 1);
                    free(*Raw_Data);
                    fclose(file);
                    exit(1);
                }
            #endif
        }
    }

    fclose(file);
}

int main() {

    // ��¼��ʼʱ��
    clock_t start_time = clock();
    // Test parameters
    int rows;                   // Will be set by load_Raw_Data_from_file
    int cols = 6;               // x,y,z,roll,pitch,yaw
    _CrtSetDbgFlag(_CRTDBG_ALLOC_MEM_DF | _CRTDBG_LEAK_CHECK_DF);
    _CrtSetBreakAlloc(3852095);    // Load the user's specific test data from file
    double* Raw_Data = NULL;
    load_Raw_Data_from_file(&Raw_Data, &rows, cols, "Raw_Data.txt");

    if (!Raw_Data) {
        fprintf(stderr, "Failed to load test data\n");
        return 1;
    }

    //// Print the input data
    //printf("Input data (x,y,z,roll,pitch,yaw):\n");
    //for (int i = 0; i < rows; i++) {
    //    printf("%2d: %8.3f %8.3f %8.3f | %8.3f %8.3f %8.3f\n",
    //        i,
    //        Raw_Data[i * cols + 0],
    //        Raw_Data[i * cols + 1],
    //        Raw_Data[i * cols + 2],
    //        Raw_Data[i * cols + 3],
    //        Raw_Data[i * cols + 4],
    //        Raw_Data[i * cols + 5]);
    //}

    // Prepare input structure
    struct TrajectoryPlanningInput input = {
        .raw_data = Raw_Data,
        .rows = rows,
        .cols = cols,
        .ori_smooth_param = 0.5,    // Orientation smoothing parameter
        .pos_smooth_param = 0.5,    // Position smoothing parameter
        .threshold = 0.0059,           // Threshold for data filtering
        .running_speed = 5.0,     // Running speed
        .vmax = 30.0,              // Maximum velocity
        .amax = 300.0,             // Maximum acceleration
        .jmax = 3000.0,              // Maximum jerk
        .diedai = 1,  // Set iteration count here
		.threshold_lvbo = 4.0, // Set data filtering threshold
        .threshold_dy = 0.0001, // Set data filtering threshold

    };
    // Prepare output structure
    struct TrajectoryPlanningOutput output = {0};

    // Call trajectory planning function
    int result = Copy_2_of_Offline_Trajectory_Planning(&input, &output);

    if (result != 0) {
        fprintf(stderr, "Trajectory planning failed with code %d\n", result);
        free(Raw_Data);
        return 1;
    }

    //// Print fitting points
    //printf("\nFitting points output (%d points):\n", output.rows);
    //for (int i = 0; i < output.rows; i++) {
    //    printf("%2d: %8.3f %8.3f %8.3f\n",
    //        i,
    //        output.fitting_points[i * 3 + 0],
    //        output.fitting_points[i * 3 + 1],
    //        output.fitting_points[i * 3 + 2]);
    //}

 

    //// Print position_inter if available
    //if (output.position_inter) {
    //    printf("\nPosition interpolation data:\n");
    //    for (int i = 0; i < output.position_inter_size; i++) {
    //        printf("%2d: %8.3f\n", i, output.position_inter[i]);
    //    }
    //}
    //else {
    //    printf("\nPosition interpolation data not available\n");
    //}
 /*   printf("rows:%d, cols:%d\n", output.theta_trajectory->rows, output.theta_trajectory->cols);
    for (int i = 0; i < output.theta_trajectory->rows; i++) {
        for (int j = 0; j < output.theta_trajectory->cols; j++) {
            printf("%f ", output.position_inter[i * output.theta_trajectory->cols + j]);
        }
        printf("\n");
    }*/


    // Clean up
    free(Raw_Data);
    free_trajectory_planning_output(&output);
   // printf("\nTrajectory planning test completed successfully\n");
    _CrtDumpMemoryLeaks();
    // ��¼����ʱ�䲢�����ʱ
    clock_t end_time = clock();
    double elapsed_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;
    printf("time: %.6f ��\n", elapsed_time);
    return 0;

}