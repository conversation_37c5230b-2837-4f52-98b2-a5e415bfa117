#include <math.h>
#include <stdlib.h>
#include "QUAT.h"
#include "Trans_ZYXeulertoquat.h"
#define M_PI 3.14159265358979323846 

Vector3_QUAT* QUAT(double* orient, int rows, int cols) {
    // Allocate memory for quaternions and result vectors
    Quaternion_quaternProd* quat = (Quaternion_quaternProd*)malloc(rows * sizeof(Quaternion_quaternProd));
    Vector3_QUAT* P = (Vector3_QUAT*)malloc(rows * sizeof(Vector3_QUAT));

    // Convert Euler angles to quaternions
    for (int i = 0; i < rows; i++) {
        EulerAngle_quaternProd angle = {
            .z = orient[i * cols] * M_PI / 180.0,
            .y = orient[i * cols + 1] * M_PI / 180.0,
            .x = orient[i * cols + 2] * M_PI / 180.0
        };
        quat[i] = Trans_ZYXeulertoquat(angle);
    }

    // Process quaternions exactly as MATLAB does
    for (int i = 0; i < rows; i++) {
        // Compute theta - exactly like MATLAB: theta1(1,num+1)=2*acos(qzyz(1,num+1));
        double theta = 2.0 * acos(quat[i].w);
        
        // Compute axis components - exactly like MATLAB code
        double nx, ny, nz;
        
        // MATLAB: n1(1,num+1)=qzyz(2,num+1)/sin(theta1(1,num+1)/2);
        // In MATLAB, qzyz(2,num+1) is the x component
        nx = quat[i].x / sin(theta/2.0);
        
        // MATLAB: n1(2,num+1)=qzyz(3,num+1)/sin(theta1(1,num+1)/2);
        // In MATLAB, qzyz(3,num+1) is the y component
        ny = quat[i].y / sin(theta/2.0);
        
        // MATLAB: n1(3,num+1)=qzyz(4,num+1)/sin(theta1(1,num+1)/2);
        // In MATLAB, qzyz(4,num+1) is the z component
        nz = quat[i].z / sin(theta/2.0);
        
        // MATLAB: P(1,num+1)= theta1(1,num+1)*n1(1,num+1);
        P[i].x = theta * nx;
        
        // MATLAB: P(2,num+1)= theta1(1,num+1)*n1(2,num+1);
        P[i].y = theta * ny;
        
        // MATLAB: P(3,num+1)= theta1(1,num+1)*n1(3,num+1);
        P[i].z = theta * nz;
    }

    // Free temporary memory
    free(quat);
    return P;
}