#pragma once
#ifndef BSPLAN_PF_1_H
#define BSPLAN_PF_1_H

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "bsplan_pf_1.h"
#include "matrix.h"
#include "velocity_bspline_u.h"
#include "calculateArcLength.h"
#include "speed_planning.h"
#include "s_timeround.h"
#include "s_snew.h"
#include "diff_u.h"

/**
 * @brief Perform trajectory planning using B-spline
 *
 * @param control_point Matrix pointer to control points
 * @param vs Initial velocity
 * @param ve Final velocity
 * @param vmax Maximum velocity
 * @param amax Maximum acceleration
 * @param jmax Maximum jerk
 * @param nik_u Start parameter value
 * @param end_u End parameter value
 * @param pt Pointer to matrix where trajectory points will be stored
 * @param z Pointer to matrix where additional data will be stored
 * @return int Status code (0 for success)
 */
int bsplan_pf_1(Matrix* control_point, double vs, double ve, double vmax,
    double amax, double jmax, double nik_u, double end_u,
    Matrix** pt, Matrix** z);

#endif /* BSPLAN_PF_1_H */
