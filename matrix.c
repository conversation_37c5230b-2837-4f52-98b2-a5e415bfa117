#include "matrix.h"
#include "find_nearest_point.h"

Matrix* create_matrix(int rows, int cols) {
    Matrix* mat = (Matrix*)malloc(sizeof(Matrix));
    if (!mat) return NULL;
    mat->rows = rows;
    mat->cols = cols;
    mat->data = (double**)malloc(rows * sizeof(double*));
    if (!mat->data) { free(mat); return NULL; }
    for (int i = 0; i < rows; i++) {
        mat->data[i] = (double*)malloc(cols * sizeof(double));
        if (!mat->data[i]) {
            // 释放已分配的行
            for (int j = 0; j < i; j++) free(mat->data[j]);
            free(mat->data);
            free(mat);
            return NULL;
        }
    }
    return mat;
}

void free_matrix(Matrix* mat) {
    if (!mat) return;
    for (int i = 0; i < mat->rows; i++) free(mat->data[i]);
    free(mat->data);
    free(mat);
}

Matrix* matrix_multiply(Matrix* a, Matrix* b) {
    if (a->cols != b->rows) {
        fprintf(stderr, "����ά�Ȳ�ƥ�䣬�޷����\n");
        return NULL;
    }
    Matrix* result = create_matrix(a->rows, b->cols);
    for (int i = 0; i < a->rows; i++) {
        for (int j = 0; j < b->cols; j++) {
            result->data[i][j] = 0;
            for (int k = 0; k < a->cols; k++) {
                result->data[i][j] += a->data[i][k] * b->data[k][j];
            }
        }
    }
    return result;
}

double get_matrix_element(Matrix* mat, int row, int col) {
    return mat->data[row][col];
}

void set_matrix_element(Matrix* mat, int row, int col, double value) {
    mat->data[row][col] = value;
}

Matrix* copy_matrix(Matrix* mat) {
    Matrix* new_mat = create_matrix(mat->rows, mat->cols);
    for (int i = 0; i < mat->rows; i++) {
        for (int j = 0; j < mat->cols; j++) {
            new_mat->data[i][j] = mat->data[i][j];
        }
    }
    return new_mat;
}

Matrix* sub_matrix(Matrix* mat, int start_row, int end_row, int start_col, int end_col) {
    Matrix* sub = create_matrix(end_row - start_row + 1, end_col - start_col + 1);
    for (int i = start_row; i <= end_row; i++) {
        for (int j = start_col; j <= end_col; j++) {
            set_matrix_element(sub, i - start_row, j - start_col, get_matrix_element(mat, i, j));
        }
    }
    return sub;
}

Matrix* matrix_cross(Matrix* a, Matrix* b) {

    Matrix* result = create_matrix(3, 1);
    if (a->rows != 1 || a->cols != 3 || b->rows != 1 || b->cols != 3) {
        fprintf(stderr, "�����˲�����\n");
        return NULL;
    }
    result->data[0][0] = a->data[0][1] * b->data[0][2] - a->data[0][2] * b->data[0][1];
    result->data[1][0] = a->data[0][2] * b->data[0][0] - a->data[0][0] * b->data[0][2];
    result->data[2][0] = a->data[0][0] * b->data[0][1] - a->data[0][1] * b->data[0][0];
    return result;
}

double vector_norm(Matrix* vec) {
    double sum = 0;
    if (vec->rows > vec->cols) {
        for (int i = 0; i < vec->rows; i++) {
            sum += vec->data[i][0] * vec->data[i][0];
        }
    }
    else
    {
        for (int i = 0; i < vec->cols; i++) {
            sum += vec->data[0][i] * vec->data[0][i];
        }
    }
    return sqrt(sum);
}

Matrix* matrix_add(Matrix* a, Matrix* b) {
    if (a->rows != b->rows || a->cols != b->cols) {
        return NULL;
    }
    Matrix* result = create_matrix(a->rows, a->cols);
    for (int i = 0; i < a->rows; i++) {
        for (int j = 0; j < a->cols; j++) {
            result->data[i][j] = a->data[i][j] + b->data[i][j];
        }
    }
    return result;
}

Matrix* transpose_matrix(Matrix* mat) {
    Matrix* transposed = create_matrix(mat->cols, mat->rows);
    for (int i = 0; i < mat->rows; i++) {
        for (int j = 0; j < mat->cols; j++) {
            set_matrix_element(transposed, j, i, get_matrix_element(mat, i, j));
        }
    }
    return transposed;
}

Matrix* create_matrix_from_points(Point* points, int num_points) {
    Matrix* mat = create_matrix(num_points, 3);
    for (int i = 0; i < num_points; i++) {
        mat->data[i][0] = points[i].x;
        mat->data[i][1] = points[i].y;
        mat->data[i][2] = points[i].z;
    }
    return mat;
}

Matrix3D* init_3d(int rows, int cols) {
    Matrix3D* m3d = malloc(sizeof(Matrix3D));
    if (!m3d) return NULL;

    m3d->rows = rows;
    m3d->cols = cols;
    m3d->layer_count = 0;
    m3d->layers = NULL;
    return m3d;
}

void free_3d(Matrix3D* m3d) {
    if (m3d) {
        for (int i = 0; i < m3d->layer_count; i++) {
            free_matrix(m3d->layers[i]);  // �ͷ�ÿ��Ķ�ά����
        }
        free(m3d->layers);
        free(m3d);
    }
}

int append_layer(Matrix3D* m3d, Matrix* layer) {
    // ά�ȼ��
    if (m3d->layer_count == 0) {
        m3d->rows = layer->rows;
        m3d->cols = layer->cols;
    }
    else if (layer->rows != m3d->rows || layer->cols != m3d->cols) {
        fprintf(stderr, "Dimension error: %dx%d vs %dx%d\n",
            layer->rows, layer->cols, m3d->rows, m3d->cols);
        return -1;
    }

    // ��չ����
    Matrix** new_layers = realloc(m3d->layers, (m3d->layer_count + 1) * sizeof(Matrix*));
    if (!new_layers) {
        perror("Layer array realloc failed");
        return -1;
    }
    m3d->layers = new_layers;

    // �����²㲢���ƶ�ά�����ֵ
    m3d->layers[m3d->layer_count] = create_matrix(layer->rows, layer->cols);
    for (int i = 0; i < layer->rows; i++) {
        for (int j = 0; j < layer->cols; j++) {
            m3d->layers[m3d->layer_count]->data[i][j] = layer->data[i][j];
        }
    }

    m3d->layer_count++;
    return 0;
}
int append_layer_flexible(Matrix3D* m3d, Matrix* layer) {
    if (!m3d || !layer) {
        fprintf(stderr, "Invalid input: m3d or layer is NULL\n");
        return -1;
    }

    // 扩展层数组
    Matrix** new_layers = (Matrix**)realloc(m3d->layers, (m3d->layer_count + 1) * sizeof(Matrix*));
    if (!new_layers) {
        fprintf(stderr, "Failed to reallocate layer array\n");
        return -1;
    }
    m3d->layers = new_layers;

    // 创建新层并复制二维矩阵值
    m3d->layers[m3d->layer_count] = create_matrix(layer->rows, layer->cols);
    if (!m3d->layers[m3d->layer_count]) {
        fprintf(stderr, "Failed to create new layer matrix\n");
        return -1;
    }

    // 复制数据
    for (int i = 0; i < layer->rows; i++) {
        for (int j = 0; j < layer->cols; j++) {
            m3d->layers[m3d->layer_count]->data[i][j] = layer->data[i][j];
        }
    }

    // 更新三维尺寸
    if (m3d->layer_count == 0) {
        m3d->rows = layer->rows;
        m3d->cols = layer->cols;
    }
    else {
        m3d->rows = (layer->rows > m3d->rows) ? layer->rows : m3d->rows;
        m3d->cols = (layer->cols > m3d->cols) ? layer->cols : m3d->cols;
    }

    m3d->layer_count++;
    return 0;
}
// ƴӺ����������¾��󣬾ɾ�����ͷţ�
//Matrix* vcat_step(Matrix* current, Matrix* to_add) {
//    // ������ʼ״̬���״ε��ã�
//    if (!current) {
//        if (!to_add) return NULL;
//        Matrix* new_mat = malloc(sizeof(Matrix));
//        *new_mat = *to_add;  // ֱ�Ӹ��ƽṹ��
//        new_mat->data = malloc(to_add->rows * sizeof(double*));
//        for (int i = 0; i < to_add->rows; i++) {
//            new_mat->data[i] = malloc(to_add->cols * sizeof(double));
//            memcpy(new_mat->data[i], to_add->data[i], to_add->cols * sizeof(double));
//        }
//        return new_mat;
//    }
//
//    // ά��У�飨��������һ�£�
//    if (to_add->cols != current->cols) {
//        fprintf(stderr, "������ƥ��: %d vs %d\n", to_add->cols, current->cols);
//        return NULL;
//    }
//
//    // �������ڴ棨ԭ���� + ���У�
//    Matrix* new_mat = malloc(sizeof(Matrix));
//    new_mat->cols = current->cols;
//    new_mat->rows = current->rows + to_add->rows;
//    new_mat->data = malloc(new_mat->rows * sizeof(double*));
//
//    // ����ԭ����
//    for (int i = 0; i < current->rows; i++) {
//        new_mat->data[i] = malloc(current->cols * sizeof(double));
//        memcpy(new_mat->data[i], current->data[i], current->cols * sizeof(double));
//    }
//
//    // ׷������
//    for (int i = 0; i < to_add->rows; i++) {
//        new_mat->data[current->rows + i] = malloc(to_add->cols * sizeof(double));
//        memcpy(new_mat->data[current->rows + i], to_add->data[i], to_add->cols * sizeof(double));
//    }
//
//    // �ͷžɾ���ע�⣺���ͷ������ڴ棬�����ṹ�����ڸ��ƣ�
//    for (int i = 0; i < current->rows; i++) free(current->data[i]);
//    free(current->data);
//    free(current);
//
//    return new_mat;
//}
Matrix* vcat_step(Matrix* current, Matrix* to_add) {
    // ��� to_add �Ƿ�Ϊ��
    if (!to_add) {
        fprintf(stderr, "to_add Ϊ�գ��޷�ƴ��\n");
        return current;
    }

    // ��� current Ϊ�գ�ֱ�Ӹ��� to_add
    if (!current) {
        return copy_matrix(to_add);
    }

    // ��������Ƿ�ƥ��
    if (to_add->cols != current->cols) {
        fprintf(stderr, "������ƥ��: %d vs %d\n", to_add->cols, current->cols);
        return NULL;
    }

    // �����µľ������ڴ洢ƴ�ӽ��
    int new_rows = current->rows + to_add->rows;
    int cols = current->cols;
    Matrix* new_mat = create_matrix(new_rows, cols);

    // ���� current ���������
    for (int i = 0; i < current->rows; i++) {
        for (int j = 0; j < cols; j++) {
            new_mat->data[i][j] = current->data[i][j];
        }
    }

    // ���� to_add ���������
    for (int i = 0; i < to_add->rows; i++) {
        for (int j = 0; j < cols; j++) {
            new_mat->data[current->rows + i][j] = to_add->data[i][j];
        }
    }

    return new_mat;
}

// ��ʼ���ն�ά����ָ��������
Matrix* init_2d(int cols) {
    Matrix* mat = malloc(sizeof(Matrix));
    mat->data = NULL;
    mat->rows = 0;
    mat->cols = cols;
    return mat;
}

// ��������
Matrix* matrix_inverse(Matrix* mat) {
    if (mat->rows != mat->cols) {
        fprintf(stderr, "矩阵非方阵，无法求逆\n");
        return NULL;
    }

    int n = mat->rows;
    // 创建增广矩阵 [A | I] - 直接访问数据指针
    Matrix* augmented = create_matrix(n, 2 * n);
    if (!augmented) return NULL;

    // 初始化增广矩阵 - 使用直接内存访问
    for (int i = 0; i < n; i++) {
        // 复制原矩阵
        memcpy(augmented->data[i], mat->data[i], n * sizeof(double));
        // 初始化单位矩阵部分
        memset(augmented->data[i] + n, 0, n * sizeof(double));
        augmented->data[i][i + n] = 1.0;
    }

    // 高斯-约旦消元法 - 使用直接数组访问
    for (int i = 0; i < n; i++) {
        // 选主元 - 查找最大绝对值
        int pivot_row = i;
        double max_val = fabs(augmented->data[i][i]);
        for (int k = i + 1; k < n; k++) {
            double val = fabs(augmented->data[k][i]);
            if (val > max_val) {
                max_val = val;
                pivot_row = k;
            }
        }

        // 交换行
        if (pivot_row != i) {
            double* temp = augmented->data[i];
            augmented->data[i] = augmented->data[pivot_row];
            augmented->data[pivot_row] = temp;
        }

        double pivot = augmented->data[i][i];
        if (fabs(pivot) < 1e-12) {
            fprintf(stderr, "矩阵奇异，无法求逆\n");
            free_matrix(augmented);
            return NULL;
        }

        // 归一化主元行 - 向量化操作
        double inv_pivot = 1.0 / pivot;
        for (int j = 0; j < 2 * n; j++) {
            augmented->data[i][j] *= inv_pivot;
        }

        // 消去其他行的主元列元素
        for (int k = 0; k < n; k++) {
            if (k != i) {
                double factor = augmented->data[k][i];
                if (fabs(factor) > 1e-15) {  // 避免不必要的计算
                    for (int j = 0; j < 2 * n; j++) {
                        augmented->data[k][j] -= factor * augmented->data[i][j];
                    }
                }
            }
        }
    }

    // 提取逆矩阵 - 使用内存复制
    Matrix* inverse = create_matrix(n, n);
    if (!inverse) {
        free_matrix(augmented);
        return NULL;
    }

    for (int i = 0; i < n; i++) {
        memcpy(inverse->data[i], augmented->data[i] + n, n * sizeof(double));
    }

    free_matrix(augmented);
    return inverse;
}

// ����ά�����ĳһ�㸳ֵ����ά����
Matrix* assign_layer_to_matrix(Matrix3D* m3d, int layer_index) {
    if (layer_index < 0 || layer_index >= m3d->layer_count) {
        fprintf(stderr, "Layer index out of range.\n");
        return NULL;
    }

    Matrix* new_mat = create_matrix(m3d->rows, m3d->cols);
    for (int i = 0; i < m3d->rows; i++) {
        for (int j = 0; j < m3d->cols; j++) {
            new_mat->data[i][j] = m3d->layers[layer_index]->data[i][j];
        }
    }

    return new_mat;
}

Matrix* copy_layer_to_matrix(Matrix3D* m3d, int layer_index) {
    if (!m3d || layer_index < 0 || layer_index >= m3d->layer_count) {
        fprintf(stderr, "Layer index out of range or NULL Matrix3D.\n");
        return NULL;
    }

    // ʹ��ʵ�ʲ��ά�ȶ��������ά��
    Matrix* layer = m3d->layers[layer_index];
    Matrix* new_mat = create_matrix(layer->rows, layer->cols);

    // ����ʵ������
    for (int i = 0; i < layer->rows; i++) {
        for (int j = 0; j < layer->cols; j++) {
            new_mat->data[i][j] = layer->data[i][j];
        }
    }

    return new_mat;
}




// ��ȡ�����е����ֵ
double get_matrix_max(Matrix* mat) {
    if (mat->rows == 0 || mat->cols == 0) {
        fprintf(stderr, "����Ϊ�գ��޷���ȡ���ֵ\n");
        return 0.0;
    }
    double max_val = mat->data[0][0];
    for (int i = 0; i < mat->rows; i++) {
        for (int j = 0; j < mat->cols; j++) {
            if (mat->data[i][j] > max_val) {
                max_val = mat->data[i][j];
            }
        }
    }
    return max_val;
}

// Function to remove a specific row from a matrix
void remove_matrix_row(Matrix* m, int row_index) {
    if (!m || row_index < 0 || row_index >= m->rows) {
        fprintf(stderr, "Invalid matrix or row index for row removal\n");
        return;
    }
    
    // Free the row that needs to be removed
    free(m->data[row_index]);
    
    // Shift all rows after the removed row one position up
    for (int i = row_index; i < m->rows - 1; i++) {
        m->data[i] = m->data[i + 1];
    }
    
    // Set the last pointer to NULL to avoid double freeing
    m->data[m->rows - 1] = NULL;
    
    // Update the number of rows
    m->rows--;
}

// Function to get a specific layer from Matrix3D
Matrix* get_layer(Matrix3D* m3d, int layer_index) {
    if (!m3d || layer_index < 0 || layer_index >= m3d->layer_count) {
        fprintf(stderr, "Invalid Matrix3D or layer index for layer retrieval\n");
        return NULL;
    }

    // Create a new matrix to store the layer data
    Matrix* layer = create_matrix(m3d->layers[layer_index]->rows, m3d->layers[layer_index]->cols);
    if (!layer) {
        fprintf(stderr, "Failed to create matrix for layer\n");
        return NULL;
    }

    // Copy the data from the specified layer
    for (int i = 0; i < layer->rows; i++) {
        for (int j = 0; j < layer->cols; j++) {
            layer->data[i][j] = m3d->layers[layer_index]->data[i][j];
        }
    }

    return layer;
}

// Function to print matrix contents
void print_matrix(Matrix* mat) {
    if (!mat) {
        printf("Matrix is NULL\n");
        return;
    }

    printf("Matrix (%d x %d):\n", mat->rows, mat->cols);
    for (int i = 0; i < mat->rows; i++) {
        for (int j = 0; j < mat->cols; j++) {
            printf("%12.6f ", mat->data[i][j]);
        }
        printf("\n");
    }
    printf("\n");
}

// Function to combine two matrices vertically (stacking)
Matrix* combine_matrices_vertically(Matrix* top, Matrix* bottom) {
    // Check for NULL inputs
    if (!top || !bottom) {
        fprintf(stderr, "One of the input matrices is NULL\n");
        return NULL;
    }

    // Check if the matrices have the same number of columns
    if (top->cols != bottom->cols) {
        fprintf(stderr, "Matrices must have the same number of columns to be combined vertically\n");
        return NULL;
    }

    // Create a new matrix with combined dimensions
    Matrix* combined = create_matrix(top->rows + bottom->rows, top->cols);
    if (!combined) {
        fprintf(stderr, "Failed to create combined matrix\n");
        return NULL;
    }

    // Copy data from top matrix
    for (int i = 0; i < top->rows; i++) {
        for (int j = 0; j < top->cols; j++) {
            combined->data[i][j] = top->data[i][j];
        }
    }

    // Copy data from bottom matrix
    for (int i = 0; i < bottom->rows; i++) {
        for (int j = 0; j < bottom->cols; j++) {
            combined->data[i + top->rows][j] = bottom->data[i][j];
        }
    }

    return combined;
}

// Function to invert a matrix (create inverse matrix)
Matrix* invert_matrix(Matrix* mat) {
    if (!mat || mat->rows != mat->cols) {
        fprintf(stderr, "Matrix inversion requires a square matrix\n");
        return NULL;
    }

    int n = mat->rows;
    // Create augmented matrix [A | I]
    Matrix* augmented = create_matrix(n, 2 * n);
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            set_matrix_element(augmented, i, j, get_matrix_element(mat, i, j));
            set_matrix_element(augmented, i, j + n, 0.0);
        }
        set_matrix_element(augmented, i, i + n, 1.0);
    }

    // Gaussian elimination with pivoting
    for (int i = 0; i < n; i++) {
        // Find pivot
        int pivot_row = i;
        double pivot_val = fabs(get_matrix_element(augmented, i, i));
        
        for (int j = i+1; j < n; j++) {
            double val = fabs(get_matrix_element(augmented, j, i));
            if (val > pivot_val) {
                pivot_val = val;
                pivot_row = j;
            }
        }
        
        // Swap rows if needed
        if (pivot_row != i) {
            for (int j = 0; j < 2*n; j++) {
                double temp = get_matrix_element(augmented, i, j);
                set_matrix_element(augmented, i, j, get_matrix_element(augmented, pivot_row, j));
                set_matrix_element(augmented, pivot_row, j, temp);
            }
        }
        
        // Check if matrix is singular
        double pivot = get_matrix_element(augmented, i, i);
        if (fabs(pivot) < 1e-10) {
            fprintf(stderr, "Matrix is singular, cannot be inverted\n");
            free_matrix(augmented);
            return NULL;
        }
        
        // Scale the pivot row
        for (int j = 0; j < 2*n; j++) {
            set_matrix_element(augmented, i, j, get_matrix_element(augmented, i, j) / pivot);
        }
        
        // Eliminate other rows
        for (int j = 0; j < n; j++) {
            if (j != i) {
                double factor = get_matrix_element(augmented, j, i);
                for (int k = 0; k < 2*n; k++) {
                    double new_val = get_matrix_element(augmented, j, k) - 
                                    factor * get_matrix_element(augmented, i, k);
                    set_matrix_element(augmented, j, k, new_val);
                }
            }
        }
    }
    
    // Extract the inverse from the augmented matrix
    Matrix* inverse = create_matrix(n, n);
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            set_matrix_element(inverse, i, j, get_matrix_element(augmented, i, j + n));
        }
    }
    
    free_matrix(augmented);
    return inverse;
}

// Function to multiply two matrices
Matrix* multiply_matrices(Matrix* a, Matrix* b) {
    if (!a || !b || a->cols != b->rows) {
        fprintf(stderr, "Invalid matrix dimensions for multiplication\n");
        return NULL;
    }
    
    Matrix* result = create_matrix(a->rows, b->cols);
    
    for (int i = 0; i < a->rows; i++) {
        for (int j = 0; j < b->cols; j++) {
            double sum = 0.0;
            for (int k = 0; k < a->cols; k++) {
                sum += get_matrix_element(a, i, k) * get_matrix_element(b, k, j);
            }
            set_matrix_element(result, i, j, sum);
        }
    }
    
    return result;
}