
function [p_u_x_1,p_u_y_1,p_u_z_1]=zhunjunyunsanci_3(n, s_x_y_z)

l=1;
if n==4
    M3=[1,0,0,0;-3,3,0,0;3,-6,3,0;-1,3,-3,1];
    Nikx(:,1)=M3*s_x_y_z(1:4,1);
    <PERSON>y(:,1)=M3*s_x_y_z(1:4,2);
    Nikz(:,1)=M3*s_x_y_z(1:4,3);
end
if n==5
    M3=[1,0,0,0;-3,3,0,0;3,-9/2,3/2,0;-1,7/4,-1,1/4];
    M4=[1/4,1/2,1/4,0;-3/4,0,3/4,0;3/4,-3/2,3/4,0;-1/4,1,-7/4,1];
    %%第一个点到第四个点
    Nikx(:,1)=M3*s_x_y_z(1:4,1);
    <PERSON>y(:,1)=M3*s_x_y_z(1:4,2);
    Nikz(:,1)=M3*s_x_y_z(1:4,3);
    %%第二个点到第五个点
    Nikx(:,2)=M4*s_x_y_z(2:5,1);
    Niky(:,2)=M4*s_x_y_z(2:5,2);
    Nikz(:,2)=M4*s_x_y_z(2:5,3);
end
if n==6
    M3=[1,0,0,0;-3,3,0,0;3,-9/2,3/2,0;-1,7/4,-11/12,1/6];
    M4=[1/4,7/12,1/6,0;-3/4,1/4,1/2,0;3/4,-5/4,1/2,0;-1/4,7/12,-7/12,1/4];
    M5=[1/6, 7/12 ,1/4, 0; -1/2, -1/4, 3/4, 0;  1/2, -5/4 ,3/4 ,0;  -1/6, 11/12, -7/4 ,1];
    %%第一个点到第四个点
    Nikx(:,1)=M3*s_x_y_z(1:4,1);
    Niky(:,1)=M3*s_x_y_z(1:4,2);
    Nikz(:,1)=M3*s_x_y_z(1:4,3);
    %%第二个点到第五个点
    Nikx(:,2)=M4*s_x_y_z(2:5,1);
    Niky(:,2)=M4*s_x_y_z(2:5,2);
    Nikz(:,2)=M4*s_x_y_z(2:5,3);
    %%第三个点到第六个点
    Nikx(:,3)=M5*s_x_y_z(3:6,1);
    Niky(:,3)=M5*s_x_y_z(3:6,2);
    Nikz(:,3)=M5*s_x_y_z(3:6,3);
end

if n==7
M3=[1, 0, 0, 0;    -3 ,3 ,0, 0;    3,-9/2, 3/2 ,0;    -1, 7/4 ,-11/12, 1/6];
M4=[1/4 ,7/12, 1/6, 0;    -3/4 ,1/4 ,1/2, 0;    3/4, -5/4 ,1/2 ,0;    -1/4, 7/12, -1/2, 1/6];
M_n_1=[1/6 ,2/3 ,1/6 ,0;    -1/2 ,0 ,1/2, 0;    1/2, -1, 1/2 ,0;    -1/6 ,1/2, -7/12, 1/4];
M_n=[1/6, 7/12 ,1/4, 0;    -1/2, -1/4, 3/4, 0;    1/2, -5/4 ,3/4 ,0;    -1/6, 11/12, -7/4 ,1];
%%第一组：第一个点到第四个点
Nikx(:,1)=M3*s_x_y_z(1:4,1);
Niky(:,1)=M3*s_x_y_z(1:4,2);
Nikz(:,1)=M3*s_x_y_z(1:4,3);
%%第二组：第二个点到第六个点
Nikx(:,2)=M4*s_x_y_z(2:5,1);
Niky(:,2)=M4*s_x_y_z(2:5,2);
Nikz(:,2)=M4*s_x_y_z(2:5,3);
%%第三组：第三个点开始

h=n-4;
Nikx(:,h)=M_n_1*s_x_y_z(h:h+3,1);
Niky(:,h)=M_n_1*s_x_y_z(h:h+3,2);
Nikz(:,h)=M_n_1*s_x_y_z(h:h+3,3);

Nikx(:,h+1)=M_n*s_x_y_z(h+1:h+4,1);
Niky(:,h+1)=M_n*s_x_y_z(h+1:h+4,2);
Nikz(:,h+1)=M_n*s_x_y_z(h+1:h+4,3);
end


if n>=8
M3=[1, 0, 0, 0;    -3 ,3 ,0, 0;    3,-9/2, 3/2 ,0;    -1, 7/4 ,-11/12, 1/6];
M4=[1/4 ,7/12, 1/6, 0;    -3/4 ,1/4 ,1/2, 0;    3/4, -5/4 ,1/2 ,0;    -1/4, 7/12, -1/2, 1/6];
M5_n_2=[1/6, 2/3, 1/6 ,0;    -1/2, 0, 1/2 ,0;    1/2 ,-1 ,1/2, 0;    -1/6 ,1/2 ,-1/2 ,1/6];
M_n_1=[1/6 ,2/3 ,1/6 ,0;    -1/2 ,0 ,1/2, 0;    1/2, -1, 1/2 ,0;    -1/6 ,1/2, -7/12, 1/4];
M_n=[1/6, 7/12 ,1/4, 0;    -1/2, -1/4, 3/4, 0;    1/2, -5/4 ,3/4 ,0;    -1/6, 11/12, -7/4 ,1];
%%第一组：第一个点到第四个点
Nikx(:,1)=M3*s_x_y_z(1:4,1);
Niky(:,1)=M3*s_x_y_z(1:4,2);
Nikz(:,1)=M3*s_x_y_z(1:4,3);
%%第二组：第二个点到第六个点
Nikx(:,2)=M4*s_x_y_z(2:5,1);
Niky(:,2)=M4*s_x_y_z(2:5,2);
Nikz(:,2)=M4*s_x_y_z(2:5,3);
%%第三组：第三个点开始
for h=1:n-5
    Nikx(:,h+2)= M5_n_2*s_x_y_z(h+2:h+5,1);
    Niky(:,h+2)= M5_n_2*s_x_y_z(h+2:h+5,2);
    Nikz(:,h+2)= M5_n_2*s_x_y_z(h+2:h+5,3);
end
h=n-4;
Nikx(:,h)=M_n_1*s_x_y_z(h:h+3,1);
Niky(:,h)=M_n_1*s_x_y_z(h:h+3,2);
Nikz(:,h)=M_n_1*s_x_y_z(h:h+3,3);

Nikx(:,h+1)=M_n*s_x_y_z(h+1:h+4,1);
Niky(:,h+1)=M_n*s_x_y_z(h+1:h+4,2);
Nikz(:,h+1)=M_n*s_x_y_z(h+1:h+4,3);
end
for m=1:length(Nikx(1,:))
    for u=0:0.05:1
        p_u_x_1(l,1)=[1,u,u^2,u^3]* Nikx(:,m);
        p_u_y_1(l,1)=[1,u,u^2,u^3]* Niky(:,m);
        p_u_z_1(l,1)=[1,u,u^2,u^3]* Nikz(:,m);
        l=l+1;
    end
end



