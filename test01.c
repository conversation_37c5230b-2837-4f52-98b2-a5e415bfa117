#include "curv_break.h"
#include <stdio.h>
#include <stdlib.h>
#include "find_nearest_point.h"
#include "matrix_ops.h"
#include "fine_fitting.h"
#include "Nik_to_control.h"
#include "Velocity_Bspline_u.h"
#include "velocity_taylor_222222.h"
#include "calculateArcLength.h"
#include "uniform_b_spline_uu.h"
#include "Velocity_Taylor_1.h"
#include "QC_Inverse.h"

#define PI 3.141592653589793238
#define Vmax 0.5
#define Amax 1
#define Jmax 1

int main() {


    // 模拟曲率数据
    //double curv[] = { 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4};

    double curv[5019];
    FILE* file;
    errno_t err = fopen_s(&file, "num_K.txt", "r");
    if (err != 0 || file == NULL) {
        perror("无法打开文件");
        return 1;
    }

    int count = 0;
    // 从文件中读取数值，直到达到数组大小或文件结束
    while (count < 5019 && fscanf_s(file, "%lf", &curv[count]) == 1) {
        count++;
    }

    fclose(file); // 关闭文件
    //// 输出读取到的数字
    //printf("读取到 %d 个数字:\n", count);
    //for (int i = 0; i < count; i++) {
    //    printf("%.6lf\n", curv[i]);
    //}

    const size_t n = sizeof(curv) / sizeof(curv[0]);
    const double threshold = 4.6; // 中等灵敏度

    // 模块化使用流程
    CurvBreak* cb = curv_break_init(); // 1. 初始化
    detect_curvature_breaks(curv, n, threshold, cb); // 2. 检测

    //// 输出结果
    //printf("检测到%d个曲率突变点（阈值%.1f）：\n",
    //    (int)cb->count, threshold);
    //for (size_t i = 0; i < cb->count; i++) {
    //    printf("位置%d: 曲率变化 %.1f→%.1f→%.1f\n",
    //        (int)cb->locs[i],
    //        curv[cb->locs[i] - 1],
    //        curv[cb->locs[i]],
    //        curv[cb->locs[i] + 1]);
    //}

    // 假设的拟合点和位置点
    //Point fitting_point[] = {
    //    {2.000, 0.000, 0.000},
    //    {1.618, 1.176, 0.628},
    //    {0.309, 1.951, 1.257},
    //    {-1.618, 1.176, 1.885},
    //    {-1.902, 0.309, 2.513},
    //    {-2.000, 0.000, 3.142},
    //    {-1.618, -1.176, 3.770},
    //    {-0.309, -1.951, 4.400},
    //    {1.618, -1.176, 5.027},
    //    {1.902, -0.309, 5.655}
    // };
    FILE* file1;
    errno_t err1 = fopen_s(&file1, "num_pos.txt", "r");
    if (err1 != 0 || file1 == NULL) {
        perror("无法打开文件");
        return 1;
    }

    Point fitting_point[5019]; // 定义 Point 数组
    count = 0;

    // 从文件中读取数据
    while (count < 5019 && fscanf_s(file1, "%lf %lf %lf",
        &fitting_point[count].x,
        &fitting_point[count].y,
        &fitting_point[count].z) == 3) {
        count++;
    }

    fclose(file1); // 关闭文件




    FILE* file6;
    errno_t err6 = fopen_s(&file6, "pos.txt", "r");
    if (err6 != 0 || file6 == NULL) {
        perror("无法打开文件");
        return 1;
    }

    Point pos[1334]; // 定义 Point 数组
    count = 0;

    // 从文件中读取数据
    while (count < 1334 && fscanf_s(file6, "%lf %lf %lf",
        &pos[count].x,
        &pos[count].y,
        &pos[count].z) == 3) {
        count++;
    }

    fclose(file6); // 关闭文件

    //// 验证读取结果
    //for (int i = 0; i < count; i++) {
    //    printf("Point %d: x = %lf, y = %lf, z = %lf\n",
    //        i, fitting_point[i].x, fitting_point[i].y, fitting_point[i].z);
    //}
    //Point pos[] = {
    //    {2.000, 0.000, 0.000},
    //    {1.618, 1.176, 0.628},
    //    {0.309, 1.951, 1.257},
    //    {-1.618, 1.176, 1.885},
    //    {-1.902, 0.309, 2.513},
    //    {-2.000, 0.000, 3.142},
    //    {-1.618, -1.176, 3.770},
    //    {-0.309, -1.951, 4.400},
    //    {1.618, -1.176, 5.027},
    //    {1.902, -0.309, 5.655}
    //};
    //Point pos[412];
    //// 将 fitting_point 的值复制到 pos
    //for (int i = 0; i < 412; i++) {
    //    pos[i] = fitting_point[i];
    //}
    // 验证复制结果
    //for (int i = 0; i < 412; i++) {
    //    printf("pos[%d]: x = %lf, y = %lf, z = %lf\n",
    //        i, pos[i].x, pos[i].y, pos[i].z);
    //}

    //double orient_data[10][3] = {
    //{0,       0,       0},       // 初始姿态
    //{PI / 12,    0,       0},       // 俯仰15°（π/12）
    //{0,       PI / 6,     0},       // 偏航30°（π/6）
    //{0,       0,       PI / 4},     // 滚转45°（π/4）
    //{-PI / 9,    PI / 18,    PI / 12},    // 复合姿态1（-20°, 10°, 15°）
    //{PI / 6,     -PI / 12,   -5 * PI / 36}, // 复合姿态2（30°, -15°, -25°）
    //{PI / 2,     0,       0},       // 垂直向下（90°）
    //{0,       PI / 2,     0},       // 正右方（90°偏航）
    //{0,       0,       PI / 2},     // 完全侧倾（90°滚转）
    //{PI / 4,     PI / 4,     PI / 4}      // 全方位倾斜（45°×3）
    //};

    double orient_data[1334][3]; // 定义二维数组
    FILE* file2;
    errno_t err2 = fopen_s(&file2, "num_ori.txt", "r");
    if (err2 != 0 || file2 == NULL) {
        perror("无法打开文件");
        return 1;
    }

    int row = 0;
    // 从文件中读取数据
    while (row < 1334 && fscanf_s(file2, "%lf %lf %lf",
        &orient_data[row][0],
        &orient_data[row][1],
        &orient_data[row][2]) == 3) {
        row++;
    }

    fclose(file2); // 关闭文件

    //验证读取结果
   //for (int i = 0; i < 412; i++) {
   //    printf("orient_data[%d]: %lf %lf %lf\n",
   //        i, orient_data[i][0], orient_data[i][1], orient_data[i][2]);
   //}

    Matrix* orient = create_matrix(1334, 3);
    for (int i = 0; i < orient->rows; i++) {
        for (int j = 0; j < orient->cols; j++) {
            set_matrix_element(orient, i, j, orient_data[i][j]);
        }
    }

    // 读取oriNikx.txt
    Matrix* oriNikx = create_matrix(4, 332);
    FILE* file3;
    errno_t err3 = fopen_s(&file3, "oriNikx.txt", "r");
    if (err3 != 0 || file3 == NULL) {
        perror("无法打开oriNikx.txt文件");
        return 1;
    }
    for (int j = 0; j < 332; j++) {
        for (int i = 0; i < 4; i++) {
            double value;
            if (fscanf_s(file3, "%lf", &value) != 1) {
                perror("读取oriNikx.txt数据失败");
                fclose(file3);
                return 1;
            }
            set_matrix_element(oriNikx, i, j, value);
        }
    }
    fclose(file3);

    // 读取oriNiky.txt
    Matrix* oriNiky = create_matrix(4, 332);
    FILE* file4;
    errno_t err4 = fopen_s(&file4, "oriNiky.txt", "r");
    if (err4 != 0 || file4 == NULL) {
        perror("无法打开oriNiky.txt文件");
        return 1;
    }
    for (int j = 0; j < 332; j++) {
        for (int i = 0; i < 4; i++) {
            double value;
            if (fscanf_s(file4, "%lf", &value) != 1) {
                perror("读取oriNiky.txt数据失败");
                fclose(file4);
                return 1;
            }
            set_matrix_element(oriNiky, i, j, value);
        }
    }
    fclose(file4);

    // 读取oriNikz.txt
    Matrix* oriNikz = create_matrix(4, 332);
    FILE* file5;
    errno_t err5 = fopen_s(&file5, "oriNikz.txt", "r");
    if (err5 != 0 || file5 == NULL) {
        perror("无法打开oriNikz.txt文件");
        return 1;
    }
    for (int j = 0; j < 332; j++) {
        for (int i = 0; i < 4; i++) {
            double value;
            if (fscanf_s(file5, "%lf", &value) != 1) {
                perror("读取oriNikz.txt数据失败");
                fclose(file5);
                return 1;
            }
            set_matrix_element(oriNikz, i, j, value);
        }
    }
    fclose(file5);

    size_t num_points = sizeof(pos) / sizeof(pos[0]);

    size_t* jizhi;
    if (cb->count == 0) {
        jizhi = (size_t*)malloc(2 * sizeof(size_t));
        if (!jizhi) {
            fprintf(stderr, "内存分配失败.\n");
            curv_break_free(cb);
            return 1;
        }
        jizhi[0] = 0;
        jizhi[1] = num_points - 1;
    }
    else {
        jizhi = (size_t*)malloc((cb->count + 2) * sizeof(size_t));
        if (!jizhi) {
            fprintf(stderr, "内存分配失败.\n");
            curv_break_free(cb);
            return 1;
        }
        jizhi[0] = 0;
        for (size_t i = 0; i < cb->count; i++) {
            Point target_point = fitting_point[cb->locs[i]];
            size_t index_start;
            double min_distance;
            find_nearest_point(pos, num_points, target_point, &index_start, &min_distance);
            jizhi[i + 1] = index_start;
        }
        jizhi[cb->count + 1] = num_points - 1;
    }

    //输出结果
    for (size_t i = 0; i < cb->count + 2; i++) {
        printf("jizhi[%zu] = %zu\n", i, jizhi[i]);
    }

    int jizhi_length = cb->count + 2;
    int num_segments = jizhi_length - 1;
    Matrix* POS = init_2d(3);
    Matrix3D* fit_over_pos = init_3d(0, 0);
    Matrix3D* fit_over_pos_Nikx = init_3d(0, 0);
    Matrix3D* fit_over_pos_Niky = init_3d(0, 0);
    Matrix3D* fit_over_pos_Nikz = init_3d(0, 0);
    Matrix3D* fit_over_pos_X = init_3d(0, 0);

    for (int i = 0; i < num_segments; i++) {
        int start_row = jizhi[i];
        int end_row = jizhi[i + 1];
        //Matrix* will_fit_pos = sub_matrix(pos, start_row, end_row, 0, 2);
        Matrix* will_fit_pos = create_matrix_from_points(&pos[start_row], end_row - start_row + 1);

        Matrix* pos_fit;
        Matrix* Nikx;
        Matrix* Niky;
        Matrix* Nikz;
        Matrix* X;

        fine_fitting(will_fit_pos, &pos_fit, &Nikx, &Niky, &Nikz, &X);
		//打印pos_fit
        
		//if ((X) != NULL) {
		//	printf("X矩阵的内容:\n");
		//	for (int i = 0; i < (X)->rows; i++) {
		//		for (int j = 0; j < (X)->cols; j++) {
		//			printf("%f ", (X)->data[i][j]);
		//		}
		//		printf("\n");
		//	}
		//}
        //free(arc); // 释放一维数组内存
        remove_specific_rows(&X);
        // 拼接矩阵（层扩展）
        append_layer_flexible(fit_over_pos, pos_fit);
        append_layer_flexible(fit_over_pos_Nikx, Nikx);
        append_layer_flexible(fit_over_pos_Niky, Niky);
        append_layer_flexible(fit_over_pos_Nikz, Nikz);
        append_layer_flexible(fit_over_pos_X, X);

        POS = vcat_step(POS, pos_fit);

        free_matrix(will_fit_pos);
        free_matrix(pos_fit);
        free_matrix(Nikx);
        free_matrix(Niky);
        free_matrix(Nikz);
        free_matrix(X);
    }

    //   Matrix* oripos_fit;
    //   Matrix* oriNikx;
    //   Matrix* oriNiky;
    //   Matrix* oriNikz;
    //   Matrix* oriX;

    //   fine_fitting(orient, &oripos_fit, &oriNikx, &oriNiky, &oriNikz, &oriX);
    //   //打印oriNikx
       //if ((oriNikx) != NULL) {
       //    printf("oriNikx矩阵的内容:\n");
       //    for (int i = 0; i < (oriNikx)->rows; i++) {
       //        for (int j = 0; j < (oriNikx)->cols; j++) {
       //            printf("%f ", (oriNikx)->data[i][j]);
       //        }
       //        printf("\n");
       //    }
    //   free_matrix(oripos_fit);
    //   free_matrix(oriX);

    Matrix* control_point1111 = Nik_to_control(oriNikx, oriNiky, oriNikz);

    //// 输出control_point1111矩阵的内容
    //if ((control_point1111) != NULL) {
    //    printf("control_point1111矩阵的内容:\n");
    //    for (int i = 0; i < (control_point1111)->rows; i++) {
    //        for (int j = 0; j < (control_point1111)->cols; j++) {
    //            printf("%f ", (control_point1111)->data[i][j]);
    //        }
    //        printf("\n");
    //    }
    //}

    int nik_u = 0; double step = 0.001;
    int end_u = (control_point1111)->rows - 3;
    int num_u = (end_u - nik_u) / 0.001 + 1;
    double* u0 = (double*)malloc(num_u * sizeof(double));
    for (int i = 0; i < num_u; i++) {
        u0[i] = nik_u + i * step;
    }
    Matrix* Nikx0;
    Matrix* Niky0;
    Matrix* Nikz0;
    Matrix* x0;
    Matrix* y0;
    Matrix* z0;
    Velocity_Bspline_u(control_point1111, u0, num_u, &Nikx0, &Niky0, &Nikz0, &x0, &y0, &z0);
    ////输出x0矩阵的内容
    //if ((x0) != NULL) {
    //    printf("x0矩阵的内容:\n");
    //    for (int i = 0; i < (x0)->rows; i++) {
    //        for (int j = 0; j < (x0)->cols; j++) {
    //            printf("%f ", (x0)->data[i][j]);
    //        }
    //        printf("\n");
    //    }
    //}


    Matrix* POS_U = create_matrix(3, x0->rows);
    for (int i = 0; i < POS_U->cols; i++) {
        set_matrix_element(POS_U, 0, i, get_matrix_element(x0, i, 0));
        set_matrix_element(POS_U, 1, i, get_matrix_element(y0, i, 0));
        set_matrix_element(POS_U, 2, i, get_matrix_element(z0, i, 0));
    }

    double arcLength = 0;
    double* arclength = 0;
    calculateArcLength(POS_U, &arcLength, &arclength);
    printf("arcLength的内容:\n");
    printf("%f \n", arcLength);

    double ds = arcLength / (orient->rows - 1);
    Matrix* U = 0;
    Matrix* position = 0;

    Velocity_Taylor_222222(ds, control_point1111, nik_u, end_u, &U, &position);
    //// 输出position矩阵的内容
    //if ((position) != NULL) {
    //    printf("position矩阵的内容:\n");
    //    for (int i = 0; i < (position)->rows; i++) {
    //        for (int j = 0; j < (position)->cols; j++) {
    //            printf("%f ", (position)->data[i][j]);
    //        }
    //        printf("\n");
    //    }
    //}
    Matrix* XYZ = NULL;
    Matrix* control_point = NULL;
    Matrix* indix = NULL;
    Uniform_B_spline_weld(position, &XYZ, &control_point, &indix);

    Matrix* ori_control_point = create_matrix(control_point->rows, 3);
    for (int i = 0; i < ori_control_point->rows; i++) {

        set_matrix_element(ori_control_point, i, 0, get_matrix_element(control_point, i, 0));
        set_matrix_element(ori_control_point, i, 1, get_matrix_element(control_point, i, 1));
        set_matrix_element(ori_control_point, i, 2, get_matrix_element(control_point, i, 2));

    }
    //打印ori_control_point
    if ((ori_control_point) != NULL) {
        printf("ori_control_point矩阵的内容:\n");
        for (int i = 0; i < (ori_control_point)->rows; i++) {
            for (int j = 0; j < (ori_control_point)->cols; j++) {
                printf("%f ", (ori_control_point)->data[i][j]);
            }
            printf("\n");
        }


        Matrix* P1_CX = NULL, * P1_CY = NULL, * P1_CZ = NULL;
        control_point = NULL;
        Matrix* Z_pos = NULL, * Z_ori = NULL, * Z_inter = NULL;
        if (jizhi_length == 2) {
            double vmax = 200; double amax = 1000; double jmax = 1000;
            P1_CX = assign_layer_to_matrix(fit_over_pos_Nikx, 0);
            P1_CY = assign_layer_to_matrix(fit_over_pos_Niky, 0);
            P1_CZ = assign_layer_to_matrix(fit_over_pos_Nikz, 0);
            control_point = Nik_to_control(P1_CX, P1_CY, P1_CZ);
            double vs = 0, ve = 0;
            double nik_0 = 0, end_0 = control_point->rows - 3;
            Matrix* pt = NULL, * POS1 = NULL;
            double* z = NULL;
            bsplan_pf_1(control_point, vs, ve, vmax, amax, jmax, nik_0, end_0, &pt, &POS1, &z);
            free_matrix(POS1);

            Matrix* U_ori = create_matrix(1, pt->rows);
            for (int i = 0; i < (U_ori)->rows; i++) {
                for (int j = 0; j < (U_ori)->cols; j++) {

                    set_matrix_element(U_ori, i, j, (z[j] - z[0]) / (z[U_ori->cols - 1] - z[0]) * (ori_control_point->rows - 3));

                }
            }
            free(z);

            Matrix* ori = Uniform_B_spline_uu(ori_control_point, U_ori);
            Z_pos = copy_matrix(pt);
            Z_ori = copy_matrix(ori);
            Z_inter = create_matrix(pt->rows, Z_ori->cols + Z_ori->cols);
            for (int i = 0; i < (Z_inter)->rows; i++) {
                for (int j = 0; j < (Z_pos)->cols; j++) {

                    set_matrix_element(Z_inter, i, j, get_matrix_element(pt, i, j));

                }
            }
            for (int i = 0; i < (Z_inter)->rows; i++) {
                for (int j = 0; j < (Z_ori)->cols; j++) {

                    set_matrix_element(Z_inter, i, (Z_pos)->cols + j, get_matrix_element(ori, i, j));

                }
            }
        }
        else {
          Matrix* P_bridge = init_2d(3);
            Matrix3D* P_bridge_con = init_3d(0, 0);
            Matrix3D* P_bridge_X = init_3d(0, 0);
            Matrix3D* indixi_trunc = init_3d(0, 0);
            double trannsfer_error = 0.5;
            double quotient_floor = floor(trannsfer_error / 0.2) * 0.2;
            double end_u1 = quotient_floor * 0.25 + 0.1;
            double nik_u1 = 0.9 - quotient_floor * 0.25;

            for (int i = 1; i < jizhi_length-1; i++) {

                Matrix* P1 = copy_layer_to_matrix(fit_over_pos, i - 1);
                Matrix* P1_C = copy_layer_to_matrix(fit_over_pos_X, i - 1);
                Matrix* P2 = copy_layer_to_matrix(fit_over_pos, i);
                Matrix* P2_C = copy_layer_to_matrix(fit_over_pos_X, i);
				//打印P1
				if ((P1) != NULL) {
					printf("P1矩阵的内容:\n");
					for (int i = 0; i < (P1)->rows; i++) {
						for (int j = 0; j < (P1)->cols; j++) {
							printf("%f ", (P1)->data[i][j]);
						}
						printf("\n");
					}
				}
                    if ((P1_C) != NULL) {
                        printf("P1_C矩阵的内容:\n");
                        for (int i = 0; i < (P1_C)->rows; i++) {
                            for (int j = 0; j < (P1_C)->cols; j++) {
                                printf("%f ", (P1_C)->data[i][j]);
                            }
                            printf("\n");
                        }
					}
                    if ((P2) != NULL) {
                        printf("P2矩阵的内容:\n");
                        for (int i = 0; i < (P2)->rows; i++) {
                            for (int j = 0; j < (P2)->cols; j++) {
                                printf("%f ", (P2)->data[i][j]);
                            }
                            printf("\n");
                        }
                    }
					if ((P2_C) != NULL) {
						printf("P2_C矩阵的内容:\n");
						for (int i = 0; i < (P2_C)->rows; i++) {
							for (int j = 0; j < (P2_C)->cols; j++) {
								printf("%f ", (P2_C)->data[i][j]);
							}
							printf("\n");
						}
					}
                int C1 = 1, C2 = 1;
                double ARC_1[21][16] = { 0 }, ARC_2[21][16] = { 0 };
                //给ARC_1和ARC_2赋值
                for (int j = 0; j < 21; j++) {
                    for (int k = 0; k < 16; k++) {

                        ARC_1[j][k] = get_matrix_element(P1_C, P1_C->rows - 21 + j, k);
                        ARC_2[j][k] = get_matrix_element(P2_C, j, k);

                    }
                }
                double PPP[201][3];
                double control_point_bridge1[5][3];
                double X[202][13];
                double P000[3];
                double P444[3];
                Bridge_arc_arc_1(C1, C2, &ARC_1[0][0], &ARC_2[0][0], nik_u1, end_u1, &PPP[0][0], &control_point_bridge1[0][0], &X[0][0], P000, P444);
                //释放内存
                free_matrix(P1);
                free_matrix(P2);
                free_matrix(P1_C);
                free_matrix(P2_C);
                //将数据复制到Point中
                Point P00 = { P000[0], P000[1], P000[2] };
                Point P44 = { P444[0], P444[1], P444[2] };
                size_t index_bridge_start, index_bridge_end;
                double min_distance;
                find_nearest_point(pos, num_points, P00, &index_bridge_start, &min_distance);
                find_nearest_point(pos, num_points, P44, &index_bridge_end, &min_distance);
                if (index_bridge_start == index_bridge_end) {
                    index_bridge_end = index_bridge_end + 1;
                }
                // indixi_trunc{i-1}=[index_bridge_start,index_bridge_end];
                Matrix* index_bridge = create_matrix(1, 2);
                set_matrix_element(index_bridge, 0, 0, index_bridge_start);
                set_matrix_element(index_bridge, 0, 1, index_bridge_end);
                append_layer(indixi_trunc, index_bridge);
                free_matrix(index_bridge);
                // P_bridge_con{i-1}=control_point_bridge;
                Matrix* control_point_bridge11 = create_matrix(5, 3);
                for (int j = 0; j < 5; j++) {
                    for (int k = 0; k < 3; k++) {
                        set_matrix_element(control_point_bridge11, j, k, control_point_bridge1[j][k]);
                    }
                }
                append_layer(P_bridge_con, control_point_bridge11);
                free_matrix(control_point_bridge11);
                // P_bridge_X{i-1}=X;
                Matrix* P_bridge_X1 = create_matrix(202, 13);
                for (int j = 0; j < 202; j++) {
                    for (int k = 0; k < 13; k++) {
                        set_matrix_element(P_bridge_X1, j, k, X[j][k]);
                    }
                }
                append_layer(P_bridge_X, P_bridge_X1);
                free_matrix(P_bridge_X1);
                // P_bridge=[P_bridge;P];
                Matrix* PPP1 = create_matrix(201, 3);
                for (int j = 0; j < 201; j++) {
                    for (int k = 0; k < 3; k++) {
                        set_matrix_element(PPP1, j, k, PPP[j][k]);
                    }
                }
                P_bridge = vcat_step(P_bridge, PPP1);
            }
            // 确定转接路径的过渡速度
            double V_d = 2;
            Matrix* V0 = create_matrix(1, P_bridge_X->layer_count);
            Matrix* V1 = create_matrix(1, P_bridge_X->layer_count);
            for (int i = 0; i < P_bridge_X->layer_count; i++) {
                Matrix* K00 = assign_layer_to_matrix(P_bridge_X, i);
                Matrix* K0 = create_matrix(K00->rows, 1);
                for (int j = 0; j < K00->rows; j++) {
                    set_matrix_element(K0, j, 0, get_matrix_element(K00, j, 0));
                }
                double Kmax = get_matrix_max(K0);
                set_matrix_element(V0, 0, i, sqrt(Amax / Kmax));
                set_matrix_element(V1, 0, i, pow(Jmax / Kmax, 1.0 / 3.0));
            }
            // V_B = [0 min(V0, V1) 0];
            Matrix* V_B = create_matrix(1, P_bridge_X->layer_count + 2);
            set_matrix_element(V_B, 0, 0, 0);
            for (int i = 0; i < P_bridge_X->layer_count; i++) {
                double V0_value = get_matrix_element(V0, 0, i);
                double V1_value = get_matrix_element(V1, 0, i);
                double min_value = fmin(V0_value, V1_value);
                set_matrix_element(V_B, 0, i + 1, min_value);
            }
            set_matrix_element(V_B, 0, P_bridge_X->layer_count + 1, 0);
            for (int i = 0; i < P_bridge_X->layer_count + 2; i++) {
                if (V_d < get_matrix_element(V_B, 0, i)) {
                    set_matrix_element(V_B, 0, i, V_d);
                }
            }
            //       // 速度规划部分
               //	double vmax = Vmax, amax = Amax, jmax = Jmax;
            //       Matrix* poss = init_2d(3);
            //       Matrix* ori_chabu = init_2d(3);
               //	Matrix3D* A_pos = init_3d(0, 0);
            //       Matrix3D* B_pos = init_3d(0, 0);
               //	Matrix3D* A_ori = init_3d(0, 0);
               //	Matrix3D* B_ori = init_3d(0, 0);
            //       Matrix3D* UU = init_3d(0, 0);
            //       
            //       for (int i = 1; i < jizhi_length; i++) {  
               //		Matrix* pt = NULL, * POS1 = NULL, * ori = NULL;
            //           if (i == 1) {
               //			P1_CX = assign_layer_to_matrix(fit_over_pos_Nikx, i - 1);
               //			P1_CY = assign_layer_to_matrix(fit_over_pos_Niky, i - 1);
               //			P1_CZ = assign_layer_to_matrix(fit_over_pos_Nikz, i - 1);
               //			control_point = Nik_to_control(P1_CX, P1_CY, P1_CZ);
               //			double vs = get_matrix_element(V_B, 0, i - 1), ve = get_matrix_element(V_B, 0, i);
               //			double nik_u2 = 0, end_u2 = nik_u1 - 1 + control_point->rows - 3;
            //               //Matrix* pt = NULL, * POS1 = NULL;
            //               double* z = NULL;
            //               bsplan_pf_1(control_point, vs, ve, vmax, amax, jmax, nik_u2, end_u2, &pt, &POS1, &z);
            //               int ori_indix_start = 1.0;
               //			int ori_indix_end = indixi_trunc->layers[i - 1]->data[0][0];
               //			Matrix* U_ori = create_matrix(POS1->rows, POS1->cols);
            //               for (int j = 0; j < POS1->rows; j++) {
            //                   for (int k = 0; k < POS1->cols; k++) {
            //                       set_matrix_element(U_ori, j, k, (get_matrix_element(POS1, j, k) - get_matrix_element(POS1, 0, 0))
            //                           / (get_matrix_element(POS1, POS1->rows - 1, POS1->cols - 1) - get_matrix_element(POS1, 0, 0)) * (ori_indix_end - ori_indix_start));
            //                   }
            //               }
            //               Matrix* ORI_con = sub_matrix(ori_control_point, ori_indix_start - 1, ori_indix_end + 1, 0, 2);
               //			ori = Uniform_B_spline_uu(&ori_control_point, &U_ori);
            //           }
            //           else if (i == jizhi_length - 1) {
            //               P1_CX = assign_layer_to_matrix(fit_over_pos_Nikx, i - 1);
            //               P1_CY = assign_layer_to_matrix(fit_over_pos_Niky, i - 1);
            //               P1_CZ = assign_layer_to_matrix(fit_over_pos_Nikz, i - 1);
            //               control_point = Nik_to_control(P1_CX, P1_CY, P1_CZ);
            //               double vs = get_matrix_element(V_B, 0, i - 1), ve = get_matrix_element(V_B, 0, i);
            //               double nik_u2 = end_u1, end_u2 = nik_u1 - 1 + control_point->rows - 3;
            //               //Matrix* pt = NULL, * POS1 = NULL;
            //               double* z = NULL;
            //               bsplan_pf_1(control_point, vs, ve, vmax, amax, jmax, nik_u2, end_u2, &pt, &POS1, &z);
            //               int ori_indix_start = indixi_trunc->layers[i - 2]->data[indixi_trunc->rows - 1][indixi_trunc->cols - 1];
            //               int ori_indix_end = orient->rows;
            //               Matrix* U_ori = create_matrix(POS1->rows, POS1->cols);
            //               for (int j = 0; j < POS1->rows; j++) {
            //                   for (int k = 0; k < POS1->cols; k++) {
            //                       set_matrix_element(U_ori, j, k, (get_matrix_element(POS1, j, k) - get_matrix_element(POS1, 0, 0))
            //                           / (get_matrix_element(POS1, POS1->rows - 1, POS1->cols - 1) - get_matrix_element(POS1, 0, 0)) * (ori_indix_end - ori_indix_start));
            //                   }
            //               }
            //               Matrix* ORI_con = sub_matrix(ori_control_point, ori_indix_start - 1, ori_indix_end + 1, 0, 2);
            //               ori = Uniform_B_spline_uu(&ori_control_point, &U_ori);
            //           }
            //           else {
               //			P1_CX = assign_layer_to_matrix(fit_over_pos_Nikx, i - 1);
               //			P1_CY = assign_layer_to_matrix(fit_over_pos_Niky, i - 1);
               //			P1_CZ = assign_layer_to_matrix(fit_over_pos_Nikz, i - 1);
               //			control_point = Nik_to_control(P1_CX, P1_CY, P1_CZ);
               //			double vs = get_matrix_element(V_B, 0, i - 1), ve = get_matrix_element(V_B, 0, i);
               //			double nik_u2 = end_u1, end_u2 = nik_u1 - 1 + control_point->rows - 3;
               //			//Matrix* pt = NULL, * POS1 = NULL;
               //			double* z = NULL;
               //			bsplan_pf_1(control_point, vs, ve, vmax, amax, jmax, nik_u2, end_u2, &pt, &POS1, &z);
               //			int ori_indix_start = indixi_trunc->layers[i - 2]->data[indixi_trunc->rows - 1][indixi_trunc->cols - 1];
               //			int ori_indix_end = indixi_trunc->layers[i - 1]->data[0][0];
               //			Matrix* U_ori = create_matrix(POS1->rows, POS1->cols);
            //               for (int j = 0; j < POS1->rows; j++) {
            //                   for (int k = 0; k < POS1->cols; k++) {
            //                       set_matrix_element(U_ori, j, k, (get_matrix_element(POS1, j, k) - get_matrix_element(POS1, 0, 0))
            //                           / (get_matrix_element(POS1, POS1->rows - 1, POS1->cols - 1) - get_matrix_element(POS1, 0, 0)) * (ori_indix_end - ori_indix_start));
            //                   }
            //               }
               //			Matrix* ORI_con = sub_matrix(ori_control_point, ori_indix_start - 1, ori_indix_end + 1, 0, 2);
               //			ori = Uniform_B_spline_uu(&ori_control_point, &U_ori);
            //           }

            //           append_layer(A_pos, pt);
               //		append_layer(A_ori, ori);
               //		poss = vcat_step(poss, pt);
               //		ori_chabu = vcat_step(ori_chabu, ori);
            //           if (i < jizhi_length - 1) {
               //			control_point = assign_layer_to_matrix(P_bridge_con, i - 1);
               //			nik_u = 0;
               //			end_u = 2;
               //			double V00 = get_matrix_element(V_B, 0, i);
            //               Matrix* U = NULL, * B_position = NULL, * uu = NULL;int V = 0;
               //			Velocity_Taylor_1(V00, control_point, nik_u, end_u, &U, &B_position, &uu, &V);
               //			int ori_indix_start = indixi_trunc->layers[i - 1]->data[0][0];
               //			int ori_indix_end = indixi_trunc->layers[i - 1]->data[indixi_trunc->rows - 1][indixi_trunc->cols - 1];
               //			Matrix* U_ori = create_matrix(U->rows, U->cols);
               //			for (int j = 0; j < U->rows; j++) {
               //				for (int k = 0; k < U->cols; k++) {
               //					set_matrix_element(U_ori, j, k, (get_matrix_element(U, j, k) - get_matrix_element(U, 0, 0))
               //						/ (get_matrix_element(U, U->rows - 1, U->cols - 1) - get_matrix_element(U, 0, 0)) * (ori_indix_end - ori_indix_start));
               //				}
               //			}
               //			Matrix* ORI_con = sub_matrix(ori_control_point, ori_indix_start - 1, ori_indix_end + 1, 0, 2);
               //			ori = Uniform_B_spline_uu(&ori_control_point, &U_ori);
               //			append_layer(B_pos, B_position);
               //			append_layer(B_ori, ori);
               //			poss = vcat_step(poss, B_position);
               //			ori_chabu = vcat_step(ori_chabu, ori);
               //			// 释放内存
               //		    free_matrix(B_position);
               //			free_matrix(control_point);
               //		    free_matrix(uu);
               //		    free_matrix(V);	
            //           }
               //		free_matrix(P1_CX);
               //		free_matrix(P1_CY);
               //		free_matrix(P1_CZ);
               //		free_matrix(control_point);
               //		free_matrix(pt);
               //		free_matrix(POS1);
               //		free_matrix(ori);
               //		free_matrix(U);
               //	
            //       }
            //       Matrix* p1 = NULL, * p2 = NULL, * o1 = NULL, * o2 = NULL;
            //       for (int i = 0; i < B_pos->layer_count; i++) {          
               //	    p1 = assign_layer_to_matrix(A_pos, i);
               //		p2 = assign_layer_to_matrix(B_pos, i);
               //		o1 = assign_layer_to_matrix(A_ori, i);
               //		o2 = assign_layer_to_matrix(B_ori, i);
               //		Matrix* p11 = create_matrix(p1->rows - 1, p1->cols);
               //		Matrix* p22 = create_matrix(p2->rows - 1, p2->cols);
               //		Matrix* o11 = create_matrix(o1->rows - 1, o1->cols);
               //		Matrix* o22 = create_matrix(o2->rows - 1, o2->cols);
               //		for (int j = 0; j < p1->rows - 1; j++) {
               //			for (int k = 0; k < p1->cols; k++) {
               //				set_matrix_element(p11, j, k, get_matrix_element(p1, j, k));
               //				set_matrix_element(p22, j, k, get_matrix_element(p2, j, k));
               //				set_matrix_element(o11, j, k, get_matrix_element(o1, j, k));
               //				set_matrix_element(o22, j, k, get_matrix_element(o2, j, k));
               //			}
               //		}
            //           Z_pos = vcat_step(poss, p11);
               //		Z_pos = vcat_step(Z_pos, p22);
               //		Z_ori = vcat_step(ori_chabu, o11);
               //		Z_ori = vcat_step(Z_ori, o22);
            //       }
               //	p1 = assign_layer_to_matrix(A_pos, A_pos->layer_count - 1);
               //	o1 = assign_layer_to_matrix(A_ori, A_ori->layer_count - 1);
               //	Z_pos = vcat_step(Z_pos, p1);
               //	Z_ori = vcat_step(Z_ori, o1);
            //       // 插补点
               //	for (int i = 0; i < Z_ori->rows; i++) {       
            //           if (get_matrix_element(Z_ori, i, 0) > 180) {
            //               set_matrix_element(Z_ori, i, 0, get_matrix_element(Z_ori, i, 0) - 360);
            //           }
               //		else if (get_matrix_element(Z_ori, i, 0) < -180) {
               //			set_matrix_element(Z_ori, i, 0, get_matrix_element(Z_ori, i, 0) + 360);
            //           }
               //		if (get_matrix_element(Z_ori, i, 1) > 180) {
               //			set_matrix_element(Z_ori, i, 1, get_matrix_element(Z_ori, i, 1) - 360);
               //		}
               //		else if (get_matrix_element(Z_ori, i, 1) < -180) {
               //			set_matrix_element(Z_ori, i, 1, get_matrix_element(Z_ori, i, 1) + 360);
               //		}
               //		if (get_matrix_element(Z_ori, i, 2) > 180) {
               //			set_matrix_element(Z_ori, i, 2, get_matrix_element(Z_ori, i, 2) - 360);
               //		}
               //		else if (get_matrix_element(Z_ori, i, 2) < -180) {
               //			set_matrix_element(Z_ori, i, 2, get_matrix_element(Z_ori, i, 2) + 360);
               //		}
               //	}
               //	Z_inter = vcat_step(Z_pos, Z_ori);
            //   }
            //   // 求逆解
               //double quat[4] = { 0 }, R[3][3] = { 0 };
            //   double O1[4] = { Z_ori->data[0][0] * PI / 180, Z_ori->data[0][1],Z_ori->data[0][2] * PI / 180 ,Z_ori->data[0][3] * PI / 180 };
            //   Trans_eulertoquat(O1, quat);
            //   Trans_eulertoMatrix(&quat[0], &R[0][0]);
            //   double T1[4][4] = { {R[0][0], R[0][1], R[0][2], Z_pos->data[0][0] * 0.001},
            //                       {R[1][0], R[1][1], R[1][2], Z_pos->data[0][1] * 0.001},
            //                       {R[2][0], R[2][1], R[2][2], Z_pos->data[0][2] * 0.001},
            //                       {0, 0, 0, 1}};
            //   double h[6] = { 0,	0,	0,	0,	0,	0 };
            //   double Theta[6];
            //   double Valid_Theta[8][6];
            //   double theta[8][6];

            //   QC_Inverse_Kinematics_SDH(&T1[0][0], h, Theta, &Valid_Theta[0][0], &theta[0][0]);
               //Theta[0] = Theta[0] * 180 / PI;Theta[1] = Theta[1] * 180 / PI;Theta[2] = Theta[2] * 180 / PI;
               //Theta[3] = Theta[3] * 180 / PI;Theta[4] = Theta[4] * 180 / PI;Theta[5] = Theta[5] * 180 / PI;
            //   
               //Matrix* theta0 = create_matrix(Z_ori->rows, 6);

               //for (int i = 0; i < Z_ori->rows; i++) {       
            //       double O1[4] = { Z_ori->data[i][0] * PI / 180, Z_ori->data[i][1],Z_ori->data[i][2] * PI / 180 ,Z_ori->data[i][3] * PI / 180 };
            //       Trans_eulertoquat(O1, quat);
            //       Trans_eulertoMatrix(&quat[0], &R[0][0]);
            //       double T1[4][4] = { {R[0][0], R[0][1], R[0][2], Z_pos->data[i][0] * 0.001},
            //                           {R[1][0], R[1][1], R[1][2], Z_pos->data[i][1] * 0.001},
            //                           {R[2][0], R[2][1], R[2][2], Z_pos->data[i][2] * 0.001},
            //                           {0, 0, 0, 1} };

               //	double h[6] = { Theta[0],	Theta[1],	Theta[2],	Theta[3],	Theta[4],	Theta[5] };
            //       QC_Inverse_Kinematics_SDH(&T1[0][0], h, Theta, &Valid_Theta[0][0], &theta[0][0]);
            //       Theta[0] = Theta[0] * 180 / PI; Theta[1] = Theta[1] * 180 / PI; Theta[2] = Theta[2] * 180 / PI;
            //       Theta[3] = Theta[3] * 180 / PI; Theta[4] = Theta[4] * 180 / PI; Theta[5] = Theta[5] * 180 / PI;

               //	for (int j = 0; j < 6; j++) {
               //		set_matrix_element(theta0, i, j, Theta[j]);
               //	}
               //}
            //   // 输出pt矩阵的内容
            //   if ((theta0) != NULL) {
            //       printf("theta0矩阵的内容:\n");
            //       for (int i = 0; i < (theta0)->rows; i++) {
            //           for (int j = 0; j < (theta0)->cols; j++) {
            //               printf("%f ", (theta0)->data[i][j]);
            //           }
            //           printf("\n");
            //       }
        }


        // 释放内存 
        free_3d(fit_over_pos);
        free_3d(fit_over_pos_Nikx);
        free_3d(fit_over_pos_Niky);
        free_3d(fit_over_pos_Nikz);
        free_3d(fit_over_pos_X);
        free(u0);
        free_matrix(orient);
        // free_matrix(theta0);
        free_matrix(POS);
        free_matrix(Nikx0);
        free_matrix(Niky0);
        free_matrix(Nikz0);
        free_matrix(x0);
        free_matrix(y0);
        free_matrix(z0);
        free_matrix(U);
        free_matrix(position);
        free_matrix(control_point);
        free_matrix(P1_CX);
        free_matrix(P1_CY);
        free_matrix(P1_CZ);
        free_matrix(oriNikx);
        free_matrix(oriNiky);
        free_matrix(oriNikz);
        free_matrix(control_point1111);
        free_matrix(POS_U);
        free_matrix(ori_control_point);
        free(jizhi);
        free(arclength);
        curv_break_free(cb);

        return 0;
    }
}