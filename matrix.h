#ifndef MATRIX_H
#define MATRIX_H

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include "find_nearest_point.h"


// �������ṹ��
typedef struct {
    double** data;
    int rows;
    int cols;
} Matrix;
// ��ά����ṹ�壨����С��У���άָ�����飩
typedef struct {
    Matrix** layers;  // ÿ����һ����ά����
    int rows;           // ���в���������̶���
    int cols;           // ���в���������̶���
    int layer_count;    // ��ǰ����
} Matrix3D;

// ��������
Matrix* create_matrix(int rows, int cols);
// �ͷž����ڴ�
void free_matrix(Matrix* mat);
// ����˷�
Matrix* matrix_multiply(Matrix* a, Matrix* b);
// ��ȡ����Ԫ��
double get_matrix_element(Matrix* mat, int row, int col);
// ���þ���Ԫ��
void set_matrix_element(Matrix* mat, int row, int col, double value);
// ���ƾ���
Matrix* copy_matrix(Matrix* mat);
// ��ȡ�����ӿ�
Matrix* sub_matrix(Matrix* mat, int start_row, int end_row, int start_col, int end_col);
// �����ˣ�����Ϊ 3x1 ������
Matrix* matrix_cross(Matrix* a, Matrix* b);
// ��������
double vector_norm(Matrix* vec);
// ����ӷ�
Matrix* matrix_add(Matrix* a, Matrix* b);
// ����ת��
Matrix* transpose_matrix(Matrix* mat);
// �� Point ���鴴������
Matrix* create_matrix_from_points(Point* points, int num_points);
// ��ʼ����ά����ָ�����У�
Matrix3D* init_3d(int rows, int cols);
// �ͷ���ά�����ڴ棨����ͷţ�
void free_3d(Matrix3D* m3d);
// ����ά����׷�Ӷ�ά���󣨲���չ��
int append_layer(Matrix3D* m3d, Matrix* layer);
int append_layer_flexible(Matrix3D* m3d, Matrix* layer);
// ��ƴ�Ӻ����������¾��󣬾ɾ�����ͷţ�
Matrix* vcat_step(Matrix* current, Matrix* to_add);
// ��ʼ���ն�ά����ָ��������
Matrix* init_2d(int cols);
// ��������
Matrix* matrix_inverse(Matrix* mat);
// ����ά�����ĳһ�㸳ֵ����ά����
Matrix* assign_layer_to_matrix(Matrix3D* m3d, int layer_index);
Matrix* copy_layer_to_matrix(Matrix3D* m3d, int layer_index);
// ��ȡ�����е����ֵ
double get_matrix_max(Matrix* mat);

// Function to remove a specific row from a matrix
void remove_matrix_row(Matrix* m, int row_index);

// Function to get a specific layer from Matrix3D
Matrix* get_layer(Matrix3D* m3d, int layer_index);

// Function to print matrix contents
void print_matrix(Matrix* mat);

// Function to combine two matrices vertically (stacking)
Matrix* combine_matrices_vertically(Matrix* top, Matrix* bottom);

// Function to invert a matrix (create inverse matrix)
Matrix* invert_matrix(Matrix* mat);

// Function to multiply two matrices
Matrix* multiply_matrices(Matrix* a, Matrix* b);

#endif