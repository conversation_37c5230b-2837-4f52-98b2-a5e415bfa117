% function [Theta] = QC_Inverse_Kinematics_SDH(T,h)
function [Theta,Valid_Theta,theta] = QC_Inverse_Kinematics_CCR10_1450_SDH(T,h)
T=T*[1 0 0 0;
    0 1 0 0;
    0 0 1 -0.147;
    0 0 0 1];

% T=[0.786242893550002	0.617917561121136	4.77573691676225e-07	0.339246653956328;
% -0.617917561121321	0.786242893549767	6.07668478168344e-07	-0.270230654625500;
% 1.02778554530838e-13	-7.72876193394732e-07	0.999999999999702	0.813576293945341;
% 0	0	0	1];
% h=[-38.4240212825014	3.02105786611295	-16.3076649081494	-4.72540635309523e-05	-103.286606755110	179.642516281841];


%% 初始值
h=h*pi/180;                                                      %%yz：在调用h函数的时候会自动输入上一时刻的关节角，并命名为h
a1=0.18;a2=0.615;a3=0.02;d1=0.483;d4=0.655;
ax=T(1,1);bx=T(1,2);cx=T(1,3);px=T(1,4);
ay=T(2,1);by=T(2,2);cy=T(2,3);py=T(2,4);
az=T(3,1);bz=T(3,2);cz=T(3,3);pz=T(3,4);
% 测试值
% T=[ 0.800516846534867	-0.00293406062321217	0.599303069992247	0.295273177650814
% -0.00533872540215767	-0.999983250186866	0.00223547686018558	0.337175274929681
% 0.599286472753184	-0.00498905140997611	-0.800519102171257	0.298024505727982
% 0	0	0	1];
% ax=T(1,1);bx=T(1,2);cx=T(1,3);px=T(1,4);
% ay=T(2,1);by=T(2,2);cy=T(2,3);py=T(2,4);
% az=T(3,1);bz=T(3,2);cz=T(3,3);pz=T(3,4);
%% 求解theta1
theta1_1 = atan2(0,1)+atan2(py,px);
theta1_2 = atan2(0,-1)+atan2(py,px);
% 误差检测
if abs(theta1_1) < eps
        theta1_1=0;
    elseif abs(theta1_2) < eps
        theta1_2=0;
end
%% 求解theta3
X_1=2*a2*a3;Y_1=2*a2*d4;
Z_1=px^2*cos(theta1_1)^2+a1^2+py^2*sin(theta1_1)^2-2*px*cos(theta1_1)*a1+2*px*cos(theta1_1)*py*sin(theta1_1)-2*a1*py*sin(theta1_1)+pz^2+d1^2-2*pz*d1-a2^2-a3^2-d4^2;
Z_2=px^2*cos(theta1_2)^2+a1^2+py^2*sin(theta1_2)^2-2*px*cos(theta1_2)*a1+2*px*cos(theta1_2)*py*sin(theta1_2)-2*a1*py*sin(theta1_2)+pz^2+d1^2-2*pz*d1-a2^2-a3^2-d4^2;
if X_1^2+Y_1^2-Z_1^2 >= 0
    theta3_1_1=atan2(Z_1,sqrt(X_1^2+Y_1^2-Z_1^2))-atan2(X_1,Y_1);
    theta3_1_2=atan2(Z_1,-sqrt(X_1^2+Y_1^2-Z_1^2))-atan2(X_1,Y_1);
else
    theta3_1_1=NaN;theta3_1_2=NaN;%%yz：NaN是Not-a-Number的缩写，代表一个非数字值。它通常用于表示缺失的或无法定义的数值结果。当你进行数学运算时，如果遇到无法得到正常数值结果的情况，比如除以零、对负数开平方根等，MATLAB会返回NaN
end
if X_1^2+Y_1^2-Z_2^2 >= 0
    theta3_2_3=atan2(Z_2,sqrt(X_1^2+Y_1^2-Z_2^2))-atan2(X_1,Y_1);
    theta3_2_4=atan2(Z_2,-sqrt(X_1^2+Y_1^2-Z_2^2))-atan2(X_1,Y_1);
else
    theta3_2_3=NaN;theta3_2_4=NaN;
end
% 误差检测
if abs(theta3_1_1) < eps
        theta3_1_1=0;
elseif abs(theta3_1_2) < eps
    theta3_1_2=0;
elseif abs(theta3_2_3) < eps
    theta3_2_3=0;
elseif abs(theta3_2_4) < eps
    theta3_2_4=0;
end
%% 求解theta2
m3_1=((a3+a2*cos(theta3_1_1))*(pz-d1)/(cos(theta1_1)*px+sin(theta1_1)*py-a1)+d4+a2*sin(theta3_1_1))*(cos(theta1_1)*px+sin(theta1_1)*py-a1);
m3_2=((a3+a2*cos(theta3_1_2))*(pz-d1)/(cos(theta1_1)*px+sin(theta1_1)*py-a1)+d4+a2*sin(theta3_1_2))*(cos(theta1_1)*px+sin(theta1_1)*py-a1);
m3_3=((a3+a2*cos(theta3_2_3))*(pz-d1)/(cos(theta1_2)*px+sin(theta1_2)*py-a1)+d4+a2*sin(theta3_2_3))*(cos(theta1_2)*px+sin(theta1_2)*py-a1);
m3_4=((a3+a2*cos(theta3_2_4))*(pz-d1)/(cos(theta1_2)*px+sin(theta1_2)*py-a1)+d4+a2*sin(theta3_2_4))*(cos(theta1_2)*px+sin(theta1_2)*py-a1);
m4_1=((a3+a2*cos(theta3_1_1))*(cos(theta1_1)*px+sin(theta1_1)*py-a1)/(pz-d1)-a2*sin(theta3_1_1)-d4)*(pz-d1);
m4_2=((a3+a2*cos(theta3_1_2))*(cos(theta1_1)*px+sin(theta1_1)*py-a1)/(pz-d1)-a2*sin(theta3_1_2)-d4)*(pz-d1);
m4_3=((a3+a2*cos(theta3_2_3))*(cos(theta1_2)*px+sin(theta1_2)*py-a1)/(pz-d1)-a2*sin(theta3_2_3)-d4)*(pz-d1);
m4_4=((a3+a2*cos(theta3_2_4))*(cos(theta1_2)*px+sin(theta1_2)*py-a1)/(pz-d1)-a2*sin(theta3_2_4)-d4)*(pz-d1);
theta2_1_1 = atan2(m3_1,m4_1)-theta3_1_1;
theta2_1_2 = atan2(m3_2,m4_2)-theta3_1_2;
theta2_2_3 = atan2(m3_3,m4_3)-theta3_2_3;
theta2_2_4 = atan2(m3_4,m4_4)-theta3_2_4;
% 误差检测
if abs(theta2_1_1) < eps
        theta2_1_1=0;
elseif abs(theta2_1_2) < eps
    theta2_1_2=0;
elseif abs(theta2_2_3) < eps
    theta2_2_3=0;
elseif abs(theta2_2_4) < eps
    theta2_2_4=0;
end
%% 四元数求解theta4、theta5、theta6
% 构造末端至腕部的旋转矩阵
A1=[cos(theta1_1)*cos(theta2_1_1+theta3_1_1), sin(theta1_1)*cos(theta2_1_1+theta3_1_1),  sin(theta2_1_1+theta3_1_1);
               sin(theta1_1)                ,              -cos(theta1_1)             ,                0           ;
    sin(theta2_1_1+theta3_1_1)*cos(theta1_1), sin(theta2_1_1+theta3_1_1)*sin(theta1_1), -cos(theta2_1_1+theta3_1_1)];
A2=[cos(theta1_1)*cos(theta2_1_2+theta3_1_2), sin(theta1_1)*cos(theta2_1_2+theta3_1_2),  sin(theta2_1_2+theta3_1_2);
               sin(theta1_1)                ,              -cos(theta1_1)             ,                0           ;
    sin(theta2_1_2+theta3_1_2)*cos(theta1_1), sin(theta2_1_2+theta3_1_2)*sin(theta1_1), -cos(theta2_1_2+theta3_1_2)];
A3=[cos(theta1_2)*cos(theta2_2_3+theta3_2_3), sin(theta1_2)*cos(theta2_2_3+theta3_2_3),  sin(theta2_2_3+theta3_2_3);
               sin(theta1_2)                ,              -cos(theta1_2)             ,                0           ;
    sin(theta2_2_3+theta3_2_3)*cos(theta1_2), sin(theta2_2_3+theta3_2_3)*sin(theta1_2), -cos(theta2_2_3+theta3_2_3)];
A4=[cos(theta1_2)*cos(theta2_2_4+theta3_2_4), sin(theta1_2)*cos(theta2_2_4+theta3_2_4),  sin(theta2_2_4+theta3_2_4);
               sin(theta1_2)                ,              -cos(theta1_2)             ,                0           ;
    sin(theta2_2_4+theta3_2_4)*cos(theta1_2), sin(theta2_2_4+theta3_2_4)*sin(theta1_2), -cos(theta2_2_4+theta3_2_4)];
A1=A1*T(1:3,1:3);A2=A2*T(1:3,1:3);A3=A3*T(1:3,1:3);A4=A4*T(1:3,1:3);
% 腕部旋转矩阵转单位四元数
% q1=rotationMatrixToQuaternion(A1);q1=q1/norm(q1);
% q2=rotationMatrixToQuaternion(A2);q2=q2/norm(q2);
% q3=rotationMatrixToQuaternion(A3);q3=q3/norm(q3);
% q4=rotationMatrixToQuaternion(A4);q4=q4/norm(q4);
% % % 列方程组求解三个未知数（腕部三个角度）
% thetaA=solveEquations(q1(1),q1(2),q1(3),q1(4));
% thetaB=solveEquations(q2(1),q2(2),q2(3),q2(4));
% thetaC=solveEquations(q3(1),q3(2),q3(3),q3(4));
% thetaD=solveEquations(q4(1),q4(2),q4(3),q4(4));
% % % 角度分配
% theta6_1_1=thetaA(1);theta5_1_1=thetaA(2);theta4_1_1=thetaA(3);
% theta6_1_2=thetaB(1);theta5_1_2=thetaB(2);theta4_1_2=thetaB(3);
% theta6_2_3=thetaC(1);theta5_2_3=thetaC(2);theta4_2_3=thetaC(3);
% theta6_2_4=thetaD(1);theta5_2_4=thetaD(2);theta4_2_4=thetaD(3);
% 
% theta4_1_1=atan2(2*(q1(3)*q1(4)-q1(2)*q1(1)),2*(q1(2)*q1(3)+q1(4)*q1(1)));theta5_1_1=acos(1-2*(q1(2)^2+q1(3)^2));theta6_1_1=atan2(2*(q1(3)*q1(4)-q1(2)*q1(1)),2*(q1(2)*q1(4)-q1(3)*q1(1)));
% theta4_1_2=atan2(2*(q2(3)*q2(4)-q2(2)*q2(1)),2*(q2(2)*q2(3)+q2(4)*q2(1)));theta5_1_2=acos(1-2*(q2(2)^2+q2(3)^2));theta6_1_2=atan2(2*(q2(3)*q2(4)-q2(2)*q2(1)),2*(q2(2)*q2(4)-q2(3)*q2(1)));
% theta4_2_3=atan2(2*(q3(3)*q3(4)-q3(2)*q3(1)),2*(q3(2)*q3(3)+q3(4)*q3(1)));theta5_2_3=acos(1-2*(q3(2)^2+q3(3)^2));theta6_2_3=atan2(2*(q3(3)*q3(4)-q3(2)*q3(1)),2*(q3(2)*q3(4)-q3(3)*q3(1)));
% theta4_2_4=atan2(2*(q4(3)*q4(4)-q4(2)*q4(1)),2*(q4(2)*q4(3)+q4(4)*q4(1)));theta5_2_4=acos(1-2*(q4(2)^2+q4(3)^2));theta6_2_4=atan2(2*(q4(3)*q4(4)-q4(2)*q4(1)),2*(q4(2)*q4(4)-q4(3)*q4(1)));
%% 欧拉角求解theta4、theta5、theta6
beta_1=atan2(sqrt(A1(3,1)^2+A1(3,2)^2),A1(3,3));
beta_2=atan2(sqrt(A2(3,1)^2+A2(3,2)^2),A2(3,3));
beta_3=atan2(sqrt(A3(3,1)^2+A3(3,2)^2),A3(3,3));
beta_4=atan2(sqrt(A4(3,1)^2+A4(3,2)^2),A4(3,3));
if beta_1 ~= 0                                                   %%yz：如果角5不等于0
    alp_1=atan2(A1(2,3)/sin(beta_1),A1(1,3)/sin(beta_1));
    gama_1=atan2(A1(3,2)/sin(beta_1),-A1(3,1)/sin(beta_1));
    theta4_1_1=alp_1;
else                                                             %%yz：如果角5等于0
    alp_1=h(1,4);theta4_1_1=alp_1;                               %%yz：角4等于上一空间角度
    n_1=cos(theta4_1_1)*sin(theta1_1) - cos(theta1_1)*cos(theta2_1_1)*cos(theta3_1_1)*sin(theta4_1_1) + cos(theta1_1)*sin(theta2_1_1)*sin(theta3_1_1)*sin(theta4_1_1);
    m_1=sin(theta1_1)*sin(theta2_1_1)*sin(theta3_1_1)*sin(theta4_1_1) - cos(theta2_1_1)*cos(theta3_1_1)*sin(theta1_1)*sin(theta4_1_1) - cos(theta1_1)*cos(theta4_1_1);
    gama_1=atan2(n_1,m_1);                                       %%yz：此时用机器人学导论p31（2.75）公式
end
if beta_2 ~= 0
    alp_2=atan2(A2(2,3)/sin(beta_2),A2(1,3)/sin(beta_2));
    gama_2=atan2(A2(3,2)/sin(beta_2),-A2(3,1)/sin(beta_2));
    theta4_1_2=alp_2;
else
    alp_2=h(1,4);theta4_1_2=alp_2;
    n_2=cos(theta4_1_2)*sin(theta1_1) - cos(theta1_1)*cos(theta2_1_2)*cos(theta3_1_2)*sin(theta4_1_2) + cos(theta1_1)*sin(theta2_1_2)*sin(theta3_1_2)*sin(theta4_1_2);
    m_2=sin(theta1_1)*sin(theta2_1_2)*sin(theta3_1_2)*sin(theta4_1_2) - cos(theta2_1_2)*cos(theta3_1_2)*sin(theta1_1)*sin(theta4_1_2) - cos(theta1_1)*cos(theta4_1_2);
    gama_2=atan2(n_2,m_2);
end
if beta_3 ~= 0
    alp_3=atan2(A3(2,3)/sin(beta_3),A3(1,3)/sin(beta_3));
    gama_3=atan2(A3(3,2)/sin(beta_3),-A3(3,1)/sin(beta_3));
    theta4_2_3=alp_3;
else
    alp_3=h(1,4);theta4_2_3=alp_3;
    n_3=cos(theta4_2_3)*sin(theta1_2) - cos(theta1_2)*cos(theta2_2_3)*cos(theta3_2_3)*sin(theta4_2_3) + cos(theta1_2)*sin(theta2_2_3)*sin(theta3_2_3)*sin(theta4_2_3);
    m_3=sin(theta1_2)*sin(theta2_2_3)*sin(theta3_2_3)*sin(theta4_2_3) - cos(theta2_2_3)*cos(theta3_2_3)*sin(theta1_2)*sin(theta4_2_3) - cos(theta1_2)*cos(theta4_2_3);
    gama_3=atan2(n_3,m_3);
end
if beta_4 ~= 0
    alp_4=atan2(A4(2,3)/sin(beta_4),A4(1,3)/sin(beta_4));
    gama_4=atan2(A4(3,2)/sin(beta_4),-A4(3,1)/sin(beta_4));
    theta4_2_4=alp_4;
else
    alp_4=h(1,4);theta4_2_4=alp_4;
    n_4=cos(theta4_2_4)*sin(theta1_2) - cos(theta1_2)*cos(theta2_2_4)*cos(theta3_2_4)*sin(theta4_2_4) + cos(theta1_2)*sin(theta2_2_4)*sin(theta3_2_4)*sin(theta4_2_4);
    m_4=sin(theta1_2)*sin(theta2_2_4)*sin(theta3_2_4)*sin(theta4_2_4) - cos(theta2_2_4)*cos(theta3_2_4)*sin(theta1_2)*sin(theta4_2_4) - cos(theta1_2)*cos(theta4_2_4);
    gama_4=atan2(n_4,m_4);
end
theta5_1_1=-beta_1;theta5_1_2=-beta_2;theta5_2_3=-beta_3;theta5_2_4=-beta_4;
theta6_1_1=gama_1;theta6_1_2=gama_2;theta6_2_3=gama_3;theta6_2_4=gama_4;
% 误差检测
if abs(theta4_1_1) < eps
        theta4_1_1=0;
elseif abs(theta4_1_2) < eps
    theta4_1_2=0;
elseif abs(theta4_2_3) < eps
    theta4_2_3=0;
elseif abs(theta4_2_4) < eps
    theta4_2_4=0;
end
if abs(theta5_1_1) < eps
        theta5_1_1=0;
elseif abs(theta5_1_2) < eps
    theta5_1_2=0;
elseif abs(theta5_2_3) < eps
    theta5_2_3=0;
elseif abs(theta5_2_4) < eps
    theta5_2_4=0;
end
if abs(theta6_1_1) < eps
        theta6_1_1=0;
elseif abs(theta6_1_2) < eps
    theta6_1_2=0;
elseif abs(theta6_2_3) < eps
    theta6_2_3=0;
elseif abs(theta6_2_4) < eps
    theta6_2_4=0;
end
%% theta2偏移量恢复
theta2_1_1=theta2_1_1-pi/2;theta2_1_2=theta2_1_2-pi/2;theta2_2_3=theta2_2_3-pi/2;theta2_2_4=theta2_2_4-pi/2;
if abs(theta2_1_1) < 1e-12
        theta2_1_1=0;
elseif abs(theta2_1_2) < 1e-12
    theta2_1_2=0;
elseif abs(theta2_2_3) < 1e-12
    theta2_2_3=0;
elseif abs(theta2_2_4) < 1e-12
    theta2_2_4=0;
end
%% 整理得到4组运动学逆解
theta_MOD1 = [ 
               theta1_1,theta2_1_1,theta3_1_1,theta4_1_1,theta5_1_1,theta6_1_1;
 			   theta1_1,theta2_1_2,theta3_1_2,theta4_1_2,theta5_1_2,theta6_1_2;
 			   theta1_2,theta2_2_3,theta3_2_3,theta4_2_3,theta5_2_3,theta6_2_3;
 			   theta1_2,theta2_2_4,theta3_2_4,theta4_2_4,theta5_2_4,theta6_2_4;
              ];
% 将操作关节‘翻转’可得到另外4组解
theta_MOD2 = [ 
               theta1_1,theta2_1_1,theta3_1_1,theta4_1_1+pi,-theta5_1_1,theta6_1_1+pi;
 			   theta1_1,theta2_1_2,theta3_1_2,theta4_1_2+pi,-theta5_1_2,theta6_1_2+pi;
 			   theta1_2,theta2_2_3,theta3_2_3,theta4_2_3+pi,-theta5_2_3,theta6_2_3+pi;
 			   theta1_2,theta2_2_4,theta3_2_4,theta4_2_4+pi,-theta5_2_4,theta6_2_4+pi;
              ];
    
theta = [theta_MOD1;theta_MOD2];
%% 检测逆解角度是否套圈
% for i = 1:8
%     for j = 1:6
%         if abs(theta(i,j)) > pi + eps
%             theta(i,j) = theta(i,j) - 2 * sign(theta(i,j)) * pi;
%         end
%     end
% end

%% 选解
% 防止正负180°跳变
for i=1:8
    for j=1:6
        if abs(theta(i,j)-h(1,j))>3/2*pi
          theta(i,j)=theta(i,j)-2*sign(theta(i,j))*pi;
        end
    end
end
joint_min_angles = [-170, -150, -65, -170, -120, -360]/180*pi;   %%yz：定义六个关节的最小允许角度（以度为单位，然后转换为弧度）
joint_max_angles = [170, 70, 232, 170, 120, 360]/180*pi;        %%yz：定义六个关节的最大允许角度（以度为单位，然后转换为弧度）
j=0;                                                             %%yz：用于记录有效解的数量
Valid_Theta=zeros(8,6);                                          %%yz：初始化为8行6列的零矩阵，用于存储符合条件（位于joint_min_angles和joint_max_angles之间）的关节角度解
for i=1:8                                                        %%yz：遍历8组关节角度解
    current_solution = theta(i, :);                              %%yz：用于暂时存储当前步骤要进行比较的那一组关节角度解，当前关节角度解
    if all(current_solution >= joint_min_angles) && all(current_solution <= joint_max_angles)%%yz：将当前关节角度值与规定的关节角度范围进行比较，如果满足
        j=j+1;                                                   %%yz：有效解计数器j+1
        Valid_Theta(j,:)=current_solution;                       %%yz：并将这个有效解存储起来
    end
end
% 以上一关节空间角度为基准
angle_diff = @(a, b) atan2(sin(a - b), cos(a - b));
D = zeros(j,1);
for i = 1:j
    dist = 0;
    for k = 1:6
        diff = angle_diff(Valid_Theta(i,k), h(1,k));
        dist = dist + diff^2;
    end
    D(i) = dist;
end
g=1;
for k=2:j                                                        %%yz：遍历矩阵D
    if D(k)<D(g)                                                     %%yz：找到矩阵D中距离平方和最小的解的索引g
        g=k;
    end
end
Theta=Valid_Theta(g,:);                                          %%yz：将找到的最优解（即最小距离对应的角度解）赋值给Theta
for k = 1:6
    delta = Theta(k) - h(1,k);
    Theta(k) = h(1,k) + atan2(sin(delta), cos(delta));
end
% Theta=Theta*180/pi;
% Valid_Theta=Valid_Theta*180/pi;
% 以关节空间角度0为基准
% h=20;
% for i=1:8
%     
%     hi=abs(theta(i,1))+abs(theta(i,2))+abs(theta(i,3))+abs(theta(i,4))+abs(theta(i,5))+abs(theta(i,6));
%     
%     if hi<h
%         h=hi;
%         g=i;
%     end
%                       
% end
% Theta=theta(g,:);
end

