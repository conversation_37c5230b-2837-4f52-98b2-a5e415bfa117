function [position_inter,Z_inter]=CCCC_Offline_Trajectory_Planning(Raw_Data,ori_smooth_Parameter,pos_smooth_Parameter,threshold,Running_Speed,Vmax,Amax,Jmax)
data=Raw_Data;
%% 数据滤波
newMatrix=shujulvbo(data);
%% 
pos=newMatrix(:,1:3);
orient=newMatrix(:,4:6);
%% 姿态欧拉角预处理——使欧拉角连续
for i=2:size(orient,1) %将欧拉角转为连续状态，不限制区间
    if orient(i,1)-orient(i-1,1)>=180
        orient(i:end,1)=orient(i:end,1)-360;
    elseif orient(i,1)-orient(i-1,1)<=-180
        orient(i:end,1)=orient(i:end,1)+360;
    end
    if orient(i,2)-orient(i-1,2)>=180
        orient(i:end,2)=orient(i:end,2)-360;
    elseif orient(i,2)-orient(i-1,2)<=-180
        orient(i:end,2)=orient(i:end,2)+360;
    end
    if orient(i,3)-orient(i-1,3)>=180
        orient(i:end,3)=orient(i:end,3)-360;
    elseif orient(i,3)-orient(i-1,3)<=-180
        orient(i:end,3)=orient(i:end,3)+360;
    end
end
% orient(:,1)=-orient(:,1);
% orient(:,2)=-orient(:,2)+180;
% orient(:,3)=-orient(:,3);

%% 姿态预处理顺滑
% 查看姿态是否有跳变
orient_0=orient; 
[~,arclength] = calculateArcLength(orient_0');
ori_diff=diff(arclength);
max_index = find(ori_diff > 10);
interval_length=floor(ori_smooth_Parameter / 0.05);

if isempty(max_index)
    orient=orient_0;
else 
    for i=1:length(max_index)
        if max_index(i)<interval_length %姿态跳变在段首
            ori_fit_qujian=max_index(i):max_index(i)+interval_length;
            data_ori=orient(ori_fit_qujian,:);
            [p_u_a,p_u_b,p_u_c,~,~,~,~,~,~]=cunihe_01(data_ori);
            ori_smoothing=[p_u_a,p_u_b,p_u_c];
            selected_points = select_points_uniformly(ori_smoothing, size(data_ori,1));
            orient(ori_fit_qujian,:)=selected_points;%预处理后姿态
        elseif max_index(i)+interval_length<size(orient_0,1) %姿态跳变在段尾
            ori_fit_qujian=max_index(i)-interval_length:max_index(i);
            data_ori=orient(ori_fit_qujian,:);
            [p_u_a,p_u_b,p_u_c,~,~,~,~,~,~]=cunihe_01(data_ori);
            ori_smoothing=[p_u_a,p_u_b,p_u_c];
            selected_points = select_points_uniformly(ori_smoothing, size(data_ori,1));
            orient(ori_fit_qujian,:)=selected_points;%预处理后姿态
        else
            ori_fit_qujian=max_index(i)-interval_length:max_index(i)+interval_length;
            data_ori=orient(ori_fit_qujian,:);
            [p_u_a,p_u_b,p_u_c,~,~,~,~,~,~]=cunihe_01(data_ori);
            ori_smoothing=[p_u_a,p_u_b,p_u_c];
            selected_points = select_points_uniformly(ori_smoothing, size(data_ori,1));
            orient(ori_fit_qujian,:)=selected_points;%预处理后姿态
        end
    end
end


%% 路径粗拟合
data=[pos(:,1),pos(:,2),pos(:,3)];
[p_u_x,p_u_y,p_u_z,K,~,~,~,~,~]=cunihe_01(data);
fitting_point=[p_u_x,p_u_y,p_u_z];
%% 寻找曲线直线的分段点
% === 曲率的极值 ===
 
yuzhi=threshold*100;
[pks, locs] = find_peaks_manual(K, yuzhi);
%[pks, locs] = detect_curvature_breaks(K, yuzhi);

K_max=[locs,pks];
length(locs)
if  isempty(K_max)
    jizhi=[1;length(pos)];
else
    for i=1:size(K_max,1)
        target_point=fitting_point(K_max(i,1),:);
        [index_start, ~] = find_nearest_point(pos, target_point);  
        jizhi(i,:)=index_start;
    end
    jizhi=[1;jizhi;length(pos)];
end

POS=[];
POS_ori=[];
fit_over_pos =cell(1, length(jizhi)-1);
fit_over_pos_Nikx=cell(1, length(jizhi)-1);
fit_over_pos_Niky=cell(1, length(jizhi)-1);
fit_over_pos_Nikz=cell(1, length(jizhi)-1);
fit_over_pos_X=cell(1, length(jizhi)-1);
for i=2:length(jizhi)
    will_fit_pos=pos(jizhi(i-1):jizhi(i),:);
    [pos_fit,Nikx,Niky,Nikz,X]=fine_fitting(will_fit_pos);  

    will_fit_ori=orient(jizhi(i-1):jizhi(i),:);
    orient_1=QUAT(will_fit_ori);
    [ori_fit,oriNikx,oriNiky,oriNikz,ori_X]=fine_fitting(orient_1);
    
    for j=1:(size(X,1)/21)-1
        ra=21*j-(j-1);
        X(ra,:)=[];
    end
    
    fit_over_pos{i-1}=pos_fit;
    fit_over_pos_Nikx{i-1}=Nikx;
    fit_over_pos_Niky{i-1}=Niky;
    fit_over_pos_Nikz{i-1}=Nikz;
    fit_over_pos_X{i-1}=X;
    POS=[POS;pos_fit];

    fit_over_ori{i-1}=ori_fit;
    fit_over_ori_Nikx{i-1}=oriNikx;
    fit_over_ori_Niky{i-1}=oriNiky;
    fit_over_ori_Nikz{i-1}=oriNikz;
    fit_over_ori_X{i-1}=ori_X;
    POS_ori=[POS_ori;ori_fit];
end
%% 姿态规划——匀顺


% orient_1=QUAT(orient);
% [~,oriNikx,oriNiky,oriNikz,~]=fine_fitting(orient_1);
% ori_control_point=Nik_to_control(oriNikx,oriNiky,oriNikz);


%% 
if size(jizhi,1)==2
        vmax=Vmax;
        amax=Amax;
        jmax=Jmax;
        P1_CX=fit_over_pos_Nikx{1};
        P1_CY=fit_over_pos_Niky{1};
        P1_CZ=fit_over_pos_Nikz{1};
        P1_oriX=fit_over_ori_Nikx{1};
        P1_oriY=fit_over_ori_Niky{1};
        P1_oriZ=fit_over_ori_Nikz{1};
        control_point=Nik_to_control(P1_CX,P1_CY,P1_CZ);
        control_point_ori=Nik_to_control(P1_oriX,P1_oriY,P1_oriZ);
        vs=0;
        ve=0;
        nik_u=0;
        end_u=size(control_point,1)-3;
        [position,U] = bsplan_pf_1(control_point, vs, ve, vmax, amax, jmax, nik_u, end_u);
        U_ori=(U-U(1))/(U(end)-U(1))*(size(control_point_ori,1)-3);
        ORI_con=control_point_ori;        
        [~,~,~,p_u_x_ori,p_u_y_ori,p_u_z_ori]=Velocity_Bspline_u(ORI_con,U_ori);
        ori=[p_u_x_ori,p_u_y_ori,p_u_z_ori];
        Z_pos=position;
        Z_ori=ori;
        Z_inter=[Z_pos,Z_ori];
else
    %% 转接
    P_bridge=[];
    P_bridge_con={};
    P_bridge_X={};
    indixi_trunc={};
    P_bridge_ori=[];
    transfer_error=pos_smooth_Parameter;%取值0~1,1表示误差较大，0表示误差较小
    quotient_floor = floor(transfer_error / 0.2)*0.2;
    end_u1=quotient_floor*0.25+0.1;
    nik_u1=0.9-quotient_floor*0.25;
    
    for i=2:length(jizhi)-1
        P1=fit_over_pos{i-1};
        P1_C=fit_over_pos_X{i-1};
        P2=fit_over_pos{i};
        P2_C=fit_over_pos_X{i};
        C1=1;C2=1;

        P1_C_ori=fit_over_ori_X{i-1};
        P2_C_ori=fit_over_ori_X{i};

        ARC_1=P1_C(end-20:end,:);%参数间距0.05
        ARC_2=P2_C(1:21,:);

        ARC_1_ori=P1_C_ori(end-20:end,:);%参数间距0.05
        ARC_2_ori=P2_C_ori(1:21,:);

        [P,control_point_bridge,X,P0,P4]=Bridge_arc_arc_1(C1,C2,ARC_1,ARC_2,nik_u1,end_u1);

        [P_ori,control_point_bridge_ori,~,~,~]=Bridge_arc_arc_2(C1,C2,ARC_1_ori,ARC_2_ori,nik_u1,end_u1);
        [index_bridge_start, ~] = find_nearest_point(pos, P0);  
        [index_bridge_end, ~] = find_nearest_point(pos, P4);  
        if index_bridge_end==index_bridge_start
            index_bridge_end=index_bridge_end+1;
        end
        indixi_trunc{i-1}=[index_bridge_start,index_bridge_end];
        P_bridge_con{i-1}=control_point_bridge;
        P_bridge_X{i-1}=X;
        P_bridge=[P_bridge;P];

        P_bridge_con_ori{i-1}=control_point_bridge_ori;
        P_bridge_ori=[P_bridge_ori;P_ori];
    end
    %% 确定转接路径的过渡速度
    V_d=Running_Speed;   %给定的运行速度
    for i=1:length(P_bridge_X)
        K0=P_bridge_X{i}(:,1);
        Kmax=max(K0);
        V0(i)=sqrt(Amax*(1/Kmax));
        V1(i) = nthroot(Jmax*(1/Kmax), 3);
    end
    V_B = [0 min(V0, V1) 0];
    for i=1:length(V_B)
        if V_d<V_B(i)
            V_B(i)=V_d;
        end
    end
    %% 数据整理
    % fit_over_pos_Nikx
    % fit_over_pos_Niky
    % fit_over_pos_Nikz
    % P_bridge_con
    % 这四个为运动单元与转接单元的控制点矩阵
    %% 速度规划部分
    vmax=Vmax;
    amax=Amax;
    jmax=Jmax;
    poss=[];
    ori_chabu=[];
    A_pos={};
    B_pos={};
    A_ori={};
    B_ori={};
    UU={};
    for i=1:length(jizhi)-1
        if i==1 %第一单元
            P1_CX=fit_over_pos_Nikx{i};
            P1_CY=fit_over_pos_Niky{i};
            P1_CZ=fit_over_pos_Nikz{i};
            P1_oriX=fit_over_ori_Nikx{i};
            P1_oriY=fit_over_ori_Niky{i};
            P1_oriZ=fit_over_ori_Nikz{i};
            control_point=Nik_to_control(P1_CX,P1_CY,P1_CZ);
            control_point_ori=Nik_to_control(P1_oriX,P1_oriY,P1_oriZ);
            vs=V_B(i);
            ve=V_B(i+1);
            nik_u=0;
            end_u=nik_u1-1+size(control_point,1)-3;
            [position,U] = bsplan_pf_1(control_point, vs, ve, vmax, amax, jmax, nik_u, end_u);
            [~,~,~,p_u_x_ori,p_u_y_ori,p_u_z_ori]=Velocity_Bspline_u(control_point_ori,U);
            ori=[p_u_x_ori,p_u_y_ori,p_u_z_ori]; 
        elseif i==length(jizhi)-1 %最后一个单元
            P1_CX=fit_over_pos_Nikx{i};
            P1_CY=fit_over_pos_Niky{i};
            P1_CZ=fit_over_pos_Nikz{i};
            P1_oriX=fit_over_ori_Nikx{i};
            P1_oriY=fit_over_ori_Niky{i};
            P1_oriZ=fit_over_ori_Nikz{i};
            control_point=Nik_to_control(P1_CX,P1_CY,P1_CZ);
            control_point_ori=Nik_to_control(P1_oriX,P1_oriY,P1_oriZ);
            vs=V_B(i);
            ve=V_B(i+1);
            nik_u=end_u1;
            end_u=1-1+size(control_point,1)-3;
            [position,U] = bsplan_pf_1(control_point, vs, ve, vmax, amax, jmax, nik_u, end_u);
            [~,~,~,p_u_x_ori,p_u_y_ori,p_u_z_ori]=Velocity_Bspline_u(control_point_ori,U);
            ori=[p_u_x_ori,p_u_y_ori,p_u_z_ori]; 
        else
            P1_CX=fit_over_pos_Nikx{i};
            P1_CY=fit_over_pos_Niky{i};
            P1_CZ=fit_over_pos_Nikz{i};
            P1_oriX=fit_over_ori_Nikx{i};
            P1_oriY=fit_over_ori_Niky{i};
            P1_oriZ=fit_over_ori_Nikz{i};
            control_point=Nik_to_control(P1_CX,P1_CY,P1_CZ);
            control_point_ori=Nik_to_control(P1_oriX,P1_oriY,P1_oriZ);
            vs=V_B(i);
            ve=V_B(i+1);
            nik_u=end_u1;
            end_u=nik_u1-1+size(control_point,1)-3;
            [position,U] = bsplan_pf_1(control_point, vs, ve, vmax, amax, jmax, nik_u, end_u);
            [~,~,~,p_u_x_ori,p_u_y_ori,p_u_z_ori]=Velocity_Bspline_u(control_point_ori,U);
            ori=[p_u_x_ori,p_u_y_ori,p_u_z_ori];
        end
        A_pos{i}=position;
        A_ori{i}=ori;
        poss=[poss;position];
        ori_chabu=[ori_chabu;ori];
        if i<length(jizhi)-1
            control_point=P_bridge_con{i};
            control_point_ori=P_bridge_con_ori{i};
            nik_u=0;
            end_u=2;
            V0=V_B(i+1);
            [U,B_position]=Velocity_Taylor_1(V0,control_point,nik_u,end_u);
            [~,~,~,p_u_x_ori,p_u_y_ori,p_u_z_ori]=Velocity_Bspline_u(control_point_ori,U);
            ori=[p_u_x_ori,p_u_y_ori,p_u_z_ori];
            B_pos{i}=B_position;
            B_ori{i}=ori;
            poss=[poss;B_position];
            ori_chabu=[ori_chabu;ori];
        end
    end
    %% 
    Z_pos=[];
    Z_ori=[];
    for i=1:length(B_pos)
        p1=A_pos{i};
        p2=B_pos{i};
        o1=A_ori{i};
        o2=B_ori{i};
        p1(end,:)=[];
        p2(end,:)=[];
        o1(end,:)=[];
        o2(end,:)=[];
        Z_pos=[Z_pos;p1;p2];
        Z_ori=[Z_ori;o1;o2];
    end
    p1=A_pos{end};
    o1=A_ori{end};
    Z_pos=[Z_pos;p1];
    Z_ori=[Z_ori;o1];
    %% 插补点
    for i=1:size(Z_ori,1)
        if Z_ori(i,1)>180
            Z_ori(i,1)=Z_ori(i,1)-360;
        elseif Z_ori(i,1)<-180
            Z_ori(i,1)=Z_ori(i,1)+360;
        end
        if Z_ori(i,2)>180
            Z_ori(i,2)=Z_ori(i,2)-360;
        elseif Z_ori(i,2)<-180
            Z_ori(i,2)=Z_ori(i,2)+360;
        end
        if Z_ori(i,3)>180
            Z_ori(i,3)=Z_ori(i,3)-360;
        elseif Z_ori(i,3)<-180
            Z_ori(i,3)=Z_ori(i,3)+360;
        end
    end
    Z_inter=[Z_pos,Z_ori];
end
Z_inter(:,4:6)=Z_inter(:,4:6)*180/pi;
%% 逆解
for i=1:size(Z_ori,1)
    Q_ori=R3toS3(Z_ori(i,1),Z_ori(i,2),Z_ori(i,3));
    RRR(:,:,i)= Trans_quatoMatrix(Q_ori');
    OOO=rotm2eul(RRR(:,:,i),"ZYX")*180/pi;
    Z_ori(i,:)=[OOO(3),OOO(2),OOO(1)];
end
Z_inter=[Z_pos,Z_ori];%%笛卡尔空间


P=Z_pos*0.001;
P1=P(1,:)';
T1=[RRR(:,:,1) P1];
T1=[T1;0 0 0 1];
% 
T_ET=[0.939427912228202	-0.0237342489540220	0.341923797289908	0.151507000000000
-0.0223028986019901	-0.999718303036705	-0.00811759122898171	0.0217120000000000
0.342020143325669	0	-0.939692620785908	0.349984000000000
0	0	0	1];
T1=T1*T_ET^-1;

h=[0 0 0 0 0 0];

[Theta,~,~] = QC_Inverse_Kinematics_CCR10_1450_SDH(T1,h);
Theta=Theta*180/pi;
 
for i=1:size(Z_ori,1)
    P2=P(i,:)';
    T=[RRR(:,:,i) P2];
    T=[T;0 0 0 1];

    T=T*T_ET^-1;     

    [Theta,~,~] = QC_Inverse_Kinematics_CCR10_1450_SDH(T,Theta);
    Theta=Theta*180/pi;
    theta(i,:)=Theta;
end
position_inter=theta;
