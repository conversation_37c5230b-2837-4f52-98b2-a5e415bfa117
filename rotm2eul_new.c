#include "rotm2eul_new.h"
#include <math.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

void rotm2eul(const RotationMatrix* R, EulerAngles* angles) {
    // Implementation based on MATLAB's rotm2eul for ZYX sequence
    // Reference: https://www.mathworks.com/help/robotics/ref/rotm2eul.html
    
    if (fabs(R->matrix[2][0]) > 0.99999) {
        // Gimbal lock case
        angles->z = 0;  // yaw
        if (R->matrix[2][0] > 0) {
            angles->y = -M_PI/2;  // pitch
            angles->x = -atan2(R->matrix[0][1], R->matrix[0][2]);  // roll
        } else {
            angles->y = M_PI/2;  // pitch
            angles->x = atan2(R->matrix[0][1], R->matrix[0][2]);  // roll
        }
    } else {
        angles->y = -asin(R->matrix[2][0]);  // pitch
        angles->z = atan2(R->matrix[1][0], R->matrix[0][0]);  // yaw
        angles->x = atan2(R->matrix[2][1], R->matrix[2][2]);  // roll
    }
}

void rotm2eul_deg(const RotationMatrix* R, EulerAngles* angles) {
    // First get angles in radians
    rotm2eul(R, angles);
    
    // Convert to degrees
    angles->x = angles->x * 180.0 / M_PI;
    angles->y = angles->y * 180.0 / M_PI;
    angles->z = angles->z * 180.0 / M_PI;
} 