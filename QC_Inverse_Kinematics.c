﻿#include "QC_Inverse_Kinematics.h"

// 辅助函数：矩阵乘法 4x4
void matrix_multiply_4x4(double A[4][4], double B[4][4], double C[4][4]) {
	int i, j, k;
	double temp[4][4] = { 0 };

	for (i = 0; i < 4; i++) {
		for (j = 0; j < 4; j++) {
			temp[i][j] = 0;
			for (k = 0; k < 4; k++) {
				temp[i][j] += A[i][k] * B[k][j];
			}
		}
	}

	for (i = 0; i < 4; i++) {
		for (j = 0; j < 4; j++) {
			C[i][j] = temp[i][j];
		}
	}
}




// 辅助函数：矩阵乘法 3x3
void matrix_multiply_3x3(double A[3][3], double B[3][3], double C[3][3]) {
	int i, j, k;
	double temp[3][3] = { 0 };

	for (i = 0; i < 3; i++) {
		for (j = 0; j < 3; j++) {
			temp[i][j] = 0;
			for (k = 0; k < 3; k++) {
				temp[i][j] += A[i][k] * B[k][j];
			}
		}
	}

	for (i = 0; i < 3; i++) {
		for (j = 0; j < 3; j++) {
			C[i][j] = temp[i][j];
		}
	}
}

// 辅助函数：角度差
static double angle_diff(double a, double b) {
	return atan2(sin(a - b), cos(a - b));
}

double QC_Inverse_Kinematics_SDH(double* TIn, double* hIn, double* Theta, double* Valid_Theta, double* theta)
{
	double T[4][4];
	double h[6];
	double TTemp[4][4];
	int i, j, k;
	int TInLen = 4;
	int hInLen = 6;
	double a1, a2, a3, d1, d4;
	double ax, ay, az;
	double bx, by, bz;
	double cx, cy, cz;
	double px, py, pz;


	//	double TTemp[4][4] = { {0.786242893550002,	0.617917561121136,	4.77573691676225e-07,	0.339246653956328},
	//{-0.617917561121321,	0.786242893549767,	6.07668478168344e-07,	-0.270230654625500},
	//{1.02778554530838e-13,	-7.72876193394732e-07,	0.999999999999702,	0.813576293945341},
	//{0,	0,	0,	1} };
/*
	double T[4][4] = { {0.786242893550002,	0.617917561121136,	4.77573691676225e-07,	0.339246653956328},
{-0.617917561121321,	0.786242893549767,	6.07668478168344e-07,	-0.270230654625500},
{1.02778554530838e-13,	-7.72876193394732e-07,	0.999999999999702,	0.813576293945341},
{0,	0,	0,	1} };
	double h[6] = { -38.4240212825014,	3.02105786611295, -16.3076649081494, -4.72540635309523e-05, -103.286606755110,	179.642516281841 };

	// only for test
	for (i = 0; i < hInLen; i++)
	{
		h[i] = h[i] * pi / 180;
	}

	*/



	for (i = 0; i < TInLen; i++)
	{
		for (j = 0; j < TInLen; j++)
		{
			TTemp[i][j] = *(TIn + i * TInLen + j);

		}
	}
	////打印
	//for (i = 0; i < TInLen; i++)
	//{
	//	for (j = 0; j < TInLen; j++)
	//	{
	//		printf("%f ", TTemp[i][j]);
	//	}
	//	printf("\n");
	//}

	for (i = 0; i < hInLen; i++)
	{
		h[i] = *(hIn + i) * pi / 180;
	}


	double TTrans[4][4] = { { 1, 0, 0, 0 },
	{ 0, 1, 0, 0 },
	{ 0, 0, 1, -0.147 },
	{ 0, 0, 0, 1 }
	};

	for (i = 0; i < 4; i++)
	{
		for (j = 0; j < 4; j++)
		{
			double tempsum1;
			tempsum1 = 0;

			for (k = 0; k < 4; k++)
			{
				tempsum1 += TTemp[i][k] * TTrans[k][j];
			}
			T[i][j] = tempsum1;

		}
	}

	a1 = 0.18;
	a2 = 0.615;
	a3 = 0.02;
	d1 = 0.483;
	d4 = 0.655;

	ax = T[0][0];
	bx = T[0][1];
	cx = T[0][2];
	px = T[0][3];
	ay = T[1][0];
	by = T[1][1];
	cy = T[1][2];
	py = T[1][3];
	az = T[2][0];
	bz = T[2][1];
	cz = T[2][2];
	pz = T[2][3];

	double theta1_1, theta1_2;
	double X_1, Y_1, Z_1, Z_2;

	theta1_1 = atan2(py, px);
	theta1_2 = theta1_1 + pi;


	if (fabs(theta1_1) < eps)
	{
		theta1_1 = 0.0;
		//elseif abs(theta1_2) < eps
		//	theta1_2 = 0;
	}
	else if (fabs(theta1_2) < eps)
	{
		theta1_2 = 0.0;
	}
	else
	{
		;
	}


	// ���theta3
	X_1 = 2 * a2 * a3;
	Y_1 = 2 * a2 * d4;
	Z_1 = px * px * cos(theta1_1) * cos(theta1_1) + a1 * a1 + py * py * sin(theta1_1) * sin(theta1_1) - 2 * px * cos(theta1_1) * a1 + 2 * px * cos(theta1_1) * py * sin(theta1_1) - 2 * a1 * py * sin(theta1_1) + pz * pz + d1 * d1 - 2 * pz * d1 - a2 * a2 - a3 * a3 - d4 * d4;
	Z_2 = px * px * cos(theta1_2) * cos(theta1_2) + a1 * a1 + py * py * sin(theta1_2) * sin(theta1_2) - 2 * px * cos(theta1_2) * a1 + 2 * px * cos(theta1_2) * py * sin(theta1_2) - 2 * a1 * py * sin(theta1_2) + pz * pz + d1 * d1 - 2 * pz * d1 - a2 * a2 - a3 * a3 - d4 * d4;


	double theta3_1_1, theta3_1_2, theta3_2_3, theta3_2_4;
	//theta3_1_1 = sqrt(X_1 * X_1 + Y_1 * Y_1 - Z_1 * Z_1);
	if ((X_1 * X_1 + Y_1 * Y_1 - Z_1 * Z_1) >= 0)
	{
		theta3_1_1 = atan2(Z_1, sqrt(X_1 * X_1 + Y_1 * Y_1 - Z_1 * Z_1)) - atan2(X_1, Y_1);
		theta3_1_2 = atan2(Z_1, -sqrt(X_1 * X_1 + Y_1 * Y_1 - Z_1 * Z_1)) - atan2(X_1, Y_1);
	}
	else
	{
		theta3_1_1 = NaN;
		theta3_1_2 = NaN;

	}

	if ((X_1 * X_1 + Y_1 * Y_1 - Z_2 * Z_2) >= 0)
	{
		theta3_2_3 = atan2(Z_2, sqrt(X_1 * X_1 + Y_1 * Y_1 - Z_2 * Z_2)) - atan2(X_1, Y_1);
		theta3_2_4 = atan2(Z_2, -sqrt(X_1 * X_1 + Y_1 * Y_1 - Z_2 * Z_2)) - atan2(X_1, Y_1);
	}
	else
	{
		theta3_2_3 = NaN;
		theta3_2_4 = NaN;
	}

	double m3_1, m3_2, m3_3, m3_4, m4_1, m4_2, m4_3, m4_4;
	double theta2_1_1, theta2_1_2, theta2_2_3, theta2_2_4;

	//���theta2
	m3_1 = ((a3 + a2 * cos(theta3_1_1)) * (pz - d1) / (cos(theta1_1) * px + sin(theta1_1) * py - a1) + d4 + a2 * sin(theta3_1_1)) * (cos(theta1_1) * px + sin(theta1_1) * py - a1);
	m3_2 = ((a3 + a2 * cos(theta3_1_2)) * (pz - d1) / (cos(theta1_1) * px + sin(theta1_1) * py - a1) + d4 + a2 * sin(theta3_1_2)) * (cos(theta1_1) * px + sin(theta1_1) * py - a1);
	m3_3 = ((a3 + a2 * cos(theta3_2_3)) * (pz - d1) / (cos(theta1_2) * px + sin(theta1_2) * py - a1) + d4 + a2 * sin(theta3_2_3)) * (cos(theta1_2) * px + sin(theta1_2) * py - a1);
	m3_4 = ((a3 + a2 * cos(theta3_2_4)) * (pz - d1) / (cos(theta1_2) * px + sin(theta1_2) * py - a1) + d4 + a2 * sin(theta3_2_4)) * (cos(theta1_2) * px + sin(theta1_2) * py - a1);
	m4_1 = ((a3 + a2 * cos(theta3_1_1)) * (cos(theta1_1) * px + sin(theta1_1) * py - a1) / (pz - d1) - a2 * sin(theta3_1_1) - d4) * (pz - d1);
	m4_2 = ((a3 + a2 * cos(theta3_1_2)) * (cos(theta1_1) * px + sin(theta1_1) * py - a1) / (pz - d1) - a2 * sin(theta3_1_2) - d4) * (pz - d1);
	m4_3 = ((a3 + a2 * cos(theta3_2_3)) * (cos(theta1_2) * px + sin(theta1_2) * py - a1) / (pz - d1) - a2 * sin(theta3_2_3) - d4) * (pz - d1);
	m4_4 = ((a3 + a2 * cos(theta3_2_4)) * (cos(theta1_2) * px + sin(theta1_2) * py - a1) / (pz - d1) - a2 * sin(theta3_2_4) - d4) * (pz - d1);
	theta2_1_1 = atan2(m3_1, m4_1) - theta3_1_1;
	theta2_1_2 = atan2(m3_2, m4_2) - theta3_1_2;
	theta2_2_3 = atan2(m3_3, m4_3) - theta3_2_3;
	theta2_2_4 = atan2(m3_4, m4_4) - theta3_2_4;


	// �����
	if (fabs(theta2_1_1) < eps)
	{
		theta2_1_1 = 0;
	}
	else if (fabs(theta2_1_2) < eps)
	{
		theta2_1_2 = 0;
	}
	else if (fabs(theta2_2_3) < eps)
	{
		theta2_2_3 = 0;
	}
	else if (fabs(theta2_2_4) < eps)
	{
		theta2_2_4 = 0;
	}
	else
	{
		;
	}


	double A11[3][3], A22[3][3], A33[3][3], A44[3][3];
	double A1[3][3], A2[3][3], A3[3][3], A4[3][3];


	A11[0][0] = cos(theta1_1) * cos(theta2_1_1 + theta3_1_1);
	A11[0][1] = sin(theta1_1) * cos(theta2_1_1 + theta3_1_1);
	A11[0][2] = sin(theta2_1_1 + theta3_1_1);
	A11[1][0] = sin(theta1_1);
	A11[1][1] = -cos(theta1_1);
	A11[1][2] = 0;
	A11[2][0] = sin(theta2_1_1 + theta3_1_1) * cos(theta1_1);
	A11[2][1] = sin(theta2_1_1 + theta3_1_1) * sin(theta1_1);
	A11[2][2] = -cos(theta2_1_1 + theta3_1_1);


	A22[0][0] = cos(theta1_1) * cos(theta2_1_2 + theta3_1_2);
	A22[0][1] = sin(theta1_1) * cos(theta2_1_2 + theta3_1_2);
	A22[0][2] = sin(theta2_1_2 + theta3_1_2);
	A22[1][0] = sin(theta1_1);
	A22[1][1] = -cos(theta1_1);
	A22[1][2] = 0.0;
	A22[2][0] = sin(theta2_1_2 + theta3_1_2) * cos(theta1_1);
	A22[2][1] = sin(theta2_1_2 + theta3_1_2) * sin(theta1_1);
	A22[2][2] = -cos(theta2_1_2 + theta3_1_2);

	A33[0][0] = cos(theta1_2) * cos(theta2_2_3 + theta3_2_3);
	A33[0][1] = sin(theta1_2) * cos(theta2_2_3 + theta3_2_3);
	A33[0][2] = sin(theta2_2_3 + theta3_2_3);
	A33[1][0] = sin(theta1_2);
	A33[1][1] = -cos(theta1_2);
	A33[1][2] = 0.0;
	A33[2][0] = sin(theta2_2_3 + theta3_2_3) * cos(theta1_2);
	A33[2][1] = sin(theta2_2_3 + theta3_2_3) * sin(theta1_2);
	A33[2][2] = -cos(theta2_2_3 + theta3_2_3);

	A44[0][0] = cos(theta1_2) * cos(theta2_2_4 + theta3_2_4);
	A44[0][1] = sin(theta1_2) * cos(theta2_2_4 + theta3_2_4);
	A44[0][2] = sin(theta2_2_4 + theta3_2_4);
	A44[1][0] = sin(theta1_2);
	A44[1][1] = -cos(theta1_2);
	A44[1][2] = 0.0;
	A44[2][0] = sin(theta2_2_4 + theta3_2_4) * cos(theta1_2);
	A44[2][1] = sin(theta2_2_4 + theta3_2_4) * sin(theta1_2);
	A44[2][2] = -cos(theta2_2_4 + theta3_2_4);


	///////A1 = A1 * T(1:3, 1 : 3);A2 = A2 * T(1:3, 1 : 3);A3 = A3 * T(1:3, 1 : 3);A4 = A4 * T(1:3, 1 : 3);
	for (i = 0; i < 3; i++)
	{
		for (j = 0; j < 3; j++)
		{
			double tempsum1, tempsum2, tempsum3, tempsum4;
			tempsum1 = 0.0;
			tempsum2 = 0.0;
			tempsum3 = 0.0;
			tempsum4 = 0.0;

			for (k = 0; k < 3; k++)
			{
				tempsum1 += A11[i][k] * T[k][j];
				tempsum2 += A22[i][k] * T[k][j];
				tempsum3 += A33[i][k] * T[k][j];
				tempsum4 += A44[i][k] * T[k][j];
			}
			A1[i][j] = tempsum1;
			A2[i][j] = tempsum2;
			A3[i][j] = tempsum3;
			A4[i][j] = tempsum4;

		}
	}

	double beta_1, beta_2, beta_3, beta_4;

	beta_1 = atan2(sqrt(A1[2][0] * A1[2][0] + A1[2][1] * A1[2][1]), A1[2][2]);
	beta_2 = atan2(sqrt(A2[2][0] * A2[2][0] + A2[2][1] * A2[2][1]), A2[2][2]);
	beta_3 = atan2(sqrt(A3[2][0] * A3[2][0] + A3[2][1] * A3[2][1]), A3[2][2]);
	beta_4 = atan2(sqrt(A4[2][0] * A4[2][0] + A4[2][1] * A4[2][1]), A4[2][2]);


	double alp_1, gama_1, theta4_1_1;
	double alp_2, gama_2, theta4_1_2;
	if (beta_1 != 0)
	{
		theta4_1_1 = atan2(A1[1][2], A1[0][2]);
		gama_1 = atan2(A1[2][1], -A1[2][0]);
	}
	else
	{
		theta4_1_1 = h[3];
		double n_1 = cos(theta4_1_1) * sin(theta1_1) -
			cos(theta1_1) * cos(theta2_1_1) * cos(theta3_1_1) * sin(theta4_1_1) +
			cos(theta1_1) * sin(theta2_1_1) * sin(theta3_1_1) * sin(theta4_1_1);
		double m_1 = sin(theta1_1) * sin(theta2_1_1) * sin(theta3_1_1) * sin(theta4_1_1) -
			cos(theta2_1_1) * cos(theta3_1_1) * sin(theta1_1) * sin(theta4_1_1) -
			cos(theta1_1) * cos(theta4_1_1);
		gama_1 = atan2(n_1, m_1);
	}

	if (beta_2 != 0)
	{
		theta4_1_2 = atan2(A2[1][2], A2[0][2]);
		gama_2 = atan2(A2[2][1], -A2[2][0]);
	}
	else
	{
		theta4_1_2 = h[3];
		double n_2 = cos(theta4_1_2) * sin(theta1_1) -
			cos(theta1_1) * cos(theta2_1_2) * cos(theta3_1_2) * sin(theta4_1_2) +
			cos(theta1_1) * sin(theta2_1_2) * sin(theta3_1_2) * sin(theta4_1_2);
		double m_2 = sin(theta1_1) * sin(theta2_1_2) * sin(theta3_1_2) * sin(theta4_1_2) -
			cos(theta2_1_2) * cos(theta3_1_2) * sin(theta1_1) * sin(theta4_1_2) -
			cos(theta1_1) * cos(theta4_1_2);
		gama_2 = atan2(n_2, m_2);
	}


	double alp_3, gama_3, theta4_2_3;
	double alp_4, gama_4, theta4_2_4;

	if (beta_3 != 0)
	{
		theta4_2_3 = atan2(A3[1][2], A3[0][2]);
		gama_3 = atan2(A3[2][1], -A3[2][0]);
	}
	else
	{
		theta4_2_3 = h[3];
		double n_3 = cos(theta4_2_3) * sin(theta1_2) -
			cos(theta1_2) * cos(theta2_2_3) * cos(theta3_2_3) * sin(theta4_2_3) +
			cos(theta1_2) * sin(theta2_2_3) * sin(theta3_2_3) * sin(theta4_2_3);
		double m_3 = sin(theta1_2) * sin(theta2_2_3) * sin(theta3_2_3) * sin(theta4_2_3) -
			cos(theta2_2_3) * cos(theta3_2_3) * sin(theta1_2) * sin(theta4_2_3) -
			cos(theta1_2) * cos(theta4_2_3);
		gama_3 = atan2(n_3, m_3);
	}
	if (beta_4 != 0)
	{
		theta4_2_4 = atan2(A4[1][2], A4[0][2]);
		gama_4 = atan2(A4[2][1], -A4[2][0]);
	}
	else
	{
		theta4_2_4 = h[3];
		double n_4 = cos(theta4_2_4) * sin(theta1_2) -
			cos(theta1_2) * cos(theta2_2_4) * cos(theta3_2_4) * sin(theta4_2_4) +
			cos(theta1_2) * sin(theta2_2_4) * sin(theta3_2_4) * sin(theta4_2_4);
		double m_4 = sin(theta1_2) * sin(theta2_2_4) * sin(theta3_2_4) * sin(theta4_2_4) -
			cos(theta2_2_4) * cos(theta3_2_4) * sin(theta1_2) * sin(theta4_2_4) -
			cos(theta1_2) * cos(theta4_2_4);
		gama_4 = atan2(n_4, m_4);
	}

	double theta5_1_1, theta5_1_2, theta5_2_3, theta5_2_4;
	double theta6_1_1, theta6_1_2, theta6_2_3, theta6_2_4;

	theta5_1_1 = -beta_1;
	theta5_1_2 = -beta_2;
	theta5_2_3 = -beta_3;
	theta5_2_4 = -beta_4;
	theta6_1_1 = atan2(A1[2][1], -A1[2][0]);
	theta6_1_2 = atan2(A2[2][1], -A2[2][0]);
	theta6_2_3 = atan2(A3[2][1], -A3[2][0]);
	theta6_2_4 = atan2(A4[2][1], -A4[2][0]);

	// 
	if (fabs(theta4_1_1) < eps)
	{
		theta4_1_1 = 0;
	}
	else if (fabs(theta4_1_2) < eps)
	{
		theta4_1_2 = 0;
	}
	else if (fabs(theta4_2_3) < eps)
	{
		theta4_2_3 = 0;
	}
	else if (fabs(theta4_2_4) < eps)
	{
		theta4_2_4 = 0;
	}
	else
	{
		;
	}




	if (fabs(theta5_1_1) < eps)
	{
		theta5_1_1 = 0;
	}
	else if (fabs(theta5_1_2) < eps)
	{
		theta5_1_2 = 0;
	}
	else if (fabs(theta5_2_3) < eps)
	{
		theta5_2_3 = 0;
	}
	else if (fabs(theta5_2_4) < eps)
	{
		theta5_2_4 = 0;
	}
	else
	{
		;
	}



	if (fabs(theta6_1_1) < eps)
	{
		theta6_1_1 = 0;
	}
	else if (fabs(theta6_1_2) < eps)
	{
		theta6_1_2 = 0;
	}

	else if (fabs(theta6_2_3) < eps)
	{
		theta6_2_3 = 0;
	}
	else if (fabs(theta6_2_4) < eps)
	{
		theta6_2_4 = 0;
	}
	else
	{
		;
	}

	// theta2ƫָ

	theta2_1_1 = theta2_1_1 - pi / 2;
	theta2_1_2 = theta2_1_2 - pi / 2;
	theta2_2_3 = theta2_2_3 - pi / 2;
	theta2_2_4 = theta2_2_4 - pi / 2;

	if (fabs(theta2_1_1) < 1e-12)
	{
		theta2_1_1 = 0;
	}
	else if (fabs(theta2_1_2) < 1e-12)
	{
		theta2_1_2 = 0;
	}
	else if (fabs(theta2_2_3) < 1e-12)
	{
		theta2_2_3 = 0;
	}
	else if (fabs(theta2_2_4) < 1e-12)
	{
		theta2_2_4 = 0;
	}
	else
	{
		;
	}



	double theta_MOD[8][6];

	// õ4˶ѧ

	// ؽڡתɵõ4
	theta_MOD[0][0] = theta1_1;
	theta_MOD[0][1] = theta2_1_1;
	theta_MOD[0][2] = theta3_1_1;
	theta_MOD[0][3] = theta4_1_1;
	theta_MOD[0][4] = theta5_1_1;
	theta_MOD[0][5] = theta6_1_1;
	theta_MOD[1][0] = theta1_1;
	theta_MOD[1][1] = theta2_1_2;
	theta_MOD[1][2] = theta3_1_2;
	theta_MOD[1][3] = theta4_1_2;
	theta_MOD[1][4] = theta5_1_2;
	theta_MOD[1][5] = theta6_1_2;
	theta_MOD[2][0] = theta1_2;
	theta_MOD[2][1] = theta2_2_3;
	theta_MOD[2][2] = theta3_2_3;
	theta_MOD[2][3] = theta4_2_3;
	theta_MOD[2][4] = theta5_2_3;
	theta_MOD[2][5] = theta6_2_3;
	theta_MOD[3][0] = theta1_2;
	theta_MOD[3][1] = theta2_2_4;
	theta_MOD[3][2] = theta3_2_4;
	theta_MOD[3][3] = theta4_2_4;
	theta_MOD[3][4] = theta5_2_4;
	theta_MOD[3][5] = theta6_2_4;
	theta_MOD[4][0] = theta1_1;
	theta_MOD[4][1] = theta2_1_1;
	theta_MOD[4][2] = theta3_1_1;
	theta_MOD[4][3] = theta4_1_1 + pi;
	theta_MOD[4][4] = -theta5_1_1;
	theta_MOD[4][5] = theta6_1_1 + pi;
	theta_MOD[5][0] = theta1_1;
	theta_MOD[5][1] = theta2_1_2;
	theta_MOD[5][2] = theta3_1_2;
	theta_MOD[5][3] = theta4_1_2 + pi;
	theta_MOD[5][4] = -theta5_1_2;
	theta_MOD[5][5] = theta6_1_2 + pi;
	theta_MOD[6][0] = theta1_2;
	theta_MOD[6][1] = theta2_2_3;
	theta_MOD[6][2] = theta3_2_3;
	theta_MOD[6][3] = theta4_2_3 + pi;
	theta_MOD[6][4] = -theta5_2_3;
	theta_MOD[6][5] = theta6_2_3 + pi;
	theta_MOD[7][0] = theta1_2;
	theta_MOD[7][1] = theta2_2_4;
	theta_MOD[7][2] = theta3_2_4;
	theta_MOD[7][3] = theta4_2_4 + pi;
	theta_MOD[7][4] = -theta5_2_4;
	theta_MOD[7][5] = theta6_2_4 + pi;



	for (i = 0; i < 8; i++)
	{
		for (j = 0; j < 6; j++)
		{
			if (fabs(theta_MOD[i][j] - h[j]) > 1.5 * pi)
			{
				int tempsign = (theta_MOD[i][j] > 0) ? 1 : ((theta_MOD[i][j] < 0) ? -1 : 0);
				theta_MOD[i][j] = theta_MOD[i][j] - 2 * tempsign * pi;
			}
		}
	}

	double joint_min_angles1[6] = { -170, -135, -65, -170, -120, -360 };
	double joint_max_angles1[6] = { 170, 100, 200, 170, 120, 360 };

	double joint_min_angles[6], joint_max_angles[6];
	double Valid_ThetaTemp[8][6] = { 0 };

	int effectJ;
	effectJ = 0;

	for (i = 0; i < 6; i++)
	{
		joint_min_angles[i] = joint_min_angles1[i] / 180 * pi;
		joint_max_angles[i] = joint_max_angles1[i] / 180 * pi;
	}


	for (i = 0; i < 8; i++)
	{
		int  cond1, cond2;
		cond1 = 0;
		cond2 = 0;
		for (j = 0; j < 6; j++)
		{
			if ((theta_MOD[i][j] >= joint_min_angles[j]) && (theta_MOD[i][j] <= joint_max_angles[j]))
			{
				cond1 = cond1 + 1;
			}
		}

		if (cond1 == 6)
		{
			for (j = 0; j < 6; j++)
			{
				Valid_ThetaTemp[effectJ][j] = theta_MOD[i][j];
			}
			effectJ = effectJ + 1;
		}
	}

	double D[8];
	double ThetaTemp[6];
	double tempSum;
	// --- BEGIN MODIFIED SECTION ---
	// Use angle_diff for distance in joint space
	for (i = 0; i < effectJ; i++)
	{
		tempSum = 0;
		for (j = 0; j < 6; j++)
		{
			double diff = angle_diff(Valid_ThetaTemp[i][j], h[j]);
			tempSum += diff * diff;
		}
		D[i] = tempSum;
	}
	k = 0;
	tempSum = D[0];
	for (i = 1; i < effectJ; i++) // start from 1
	{
		if (D[i] < tempSum)
		{
			k = i;
			tempSum = D[i];
		}
	}
	for (j = 0; j < 6; j++)
	{
		ThetaTemp[j] = Valid_ThetaTemp[k][j];
	}
	// Adjust ThetaTemp to be closest to h[j] using angle_diff
	for (j = 0; j < 6; j++) {
		double delta = ThetaTemp[j] - h[j];
		ThetaTemp[j] = h[j] + atan2(sin(delta), cos(delta));
	}
	// --- END MODIFIED SECTION ---

	// output Theta
	for (j = 0; j < 6; j++)
	{
		*(Theta + j) = ThetaTemp[j];
	}
	////打印ThetaTemp
	//printf("ThetaTemp: ");
	//for (j = 0; j < 6; j++) {
	//	printf("%f ", ThetaTemp[j]);
	//}
	//printf("\n");

	// output Valid_Theta;
	for (i = 0; i < effectJ; i++)
	{
		for (j = 0; j < 6; j++)
		{
			*(Valid_Theta + i * 6 + j) = Valid_ThetaTemp[i][j];
		}
	}

	// output theta
	for (i = 0; i < effectJ; i++)
	{
		for (j = 0; j < 6; j++)
		{
			*(theta + i * 6 + j) = theta_MOD[i][j];
		}
	}
	// theta_MOD Valid_ThetaTemp effectJ ThetaTemp
	return effectJ;


}