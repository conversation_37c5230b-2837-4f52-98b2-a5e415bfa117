%% 弧线段细拟合程序
% 输入：arc点位信息
% 输出：pos拟合路径点位
%       control控制点
%       X：K,D1,D2,N,Bt,p_u_x,p_u_y,p_u_z
function [pos,Nikx,Niky,Nikz,X]=fine_fitting(arc)
x_with_noise=arc(:,1);
y_with_noise=arc(:,2);
z_with_noise=arc(:,3);
num=length(x_with_noise);
[quotient, remainder] = my_divmod(num, 4);
%% 隔点采样取控制点
if remainder<1
    for i=1:quotient-1
        Px(i,1)=x_with_noise(i*4);
        Py(i,1)=y_with_noise(i*4);
        Pz(i,1)=z_with_noise(i*4);
    end
else
    for i=1:quotient
        Px(i,1)=x_with_noise(i*4);
        Py(i,1)=y_with_noise(i*4);
        Pz(i,1)=z_with_noise(i*4);
    end
end
Px=[x_with_noise(1);Px;x_with_noise(end)];
Py=[y_with_noise(1);Py;y_with_noise(end)];
Pz=[z_with_noise(1);Pz;z_with_noise(end)];
P= [Px,Py,Pz]; % 简单的直线数据点
M= [Px,Py,Pz];%第一代选取的离散点
p(:,1)=x_with_noise;
p(:,2)=y_with_noise;
% p(:,3)=z_with_noise;


for g=1:10%迭代10次 
    n=length(M(:,1));
    [Nikx,Niky,Nikz,p_u_x,p_u_y,p_u_z]=zhunjunyunsanci_2(n,P);
    %% 观测数据用的差值——用所有噪音点对拟合曲线上的点求解记录
%     for m=1:length(x_with_noise(1,:))
%         for i=1:length(p_u_x)
%             chazhi(i,m)=sqrt((x_with_noise(1,m)-p_u_x(i,1))^2+(y_with_noise(1,m)-p_u_y(i,1))^2+(z_with_noise(1,m)-p_u_z(i,1))^2);
%         end
%     end
%     [columnMinValues0, columnIndexes0] = min(chazhi, [], 1);%计算差值最小值以及其对应的i索引，型值点索引：i*s
%     columnMinValues0=columnMinValues0';
%     columnIndexes0=columnIndexes0';
%     averagex=1:length(columnMinValues0);
%     averagey(g)= sum(columnMinValues0)/length(columnMinValues0);%计算平均误差
%     variance = fangcha(columnMinValues0,averagey(g));%计算方差
%     fangcha_averagey(g)=variance;
    % maxcolumnMinValues0=max(columnMinValues0);%计算最大误差
    %% 迭代用的差值——用第一代离散点与拟合曲线上的点求解调整向量
    for m=1:n
        for i=1:length(p_u_x)
            chazhi(i,m)=sqrt((M(m,1)-p_u_x(i,1))^2+(M(m,2)-p_u_y(i,1))^2+(M(m,3)-p_u_z(i,1))^2);
        end
    end
    [~, columnIndexes] = min(chazhi, [], 1);%计算差值最小值以及其对应的i索引，型值点索引：i*s
%     columnMinValues=columnMinValues';
    columnIndexes=columnIndexes';
     
    %% 求位移
    for t=1:n
        s_x(t,1)=M(t,1)-p_u_x(columnIndexes(t),1);
        s_y(t,1)=M(t,2)-p_u_y(columnIndexes(t),1);
        s_z(t,1)=M(t,3)-p_u_z(columnIndexes(t),1);
    end
    s_x_y_z=[s_x,s_y,s_z];
    [p_u_x_1,p_u_y_1,p_u_z_1]=zhunjunyunsanci_3(n, s_x_y_z);
    for t=1:n
        P(t,1)=P(t,1)+0.4*p_u_x_1( columnIndexes(t),1);
        P(t,2)=P(t,2)+0.4*p_u_y_1( columnIndexes(t),1);
        P(t,3)=P(t,3)+0.4*p_u_z_1( columnIndexes(t),1);
    end

end
pos=[p_u_x,p_u_y,p_u_z];
[K,D1,D2,N,Bt]=shujuchuli(P,n);
X=[K,D1,D2,N,Bt,p_u_x,p_u_y,p_u_z];
end