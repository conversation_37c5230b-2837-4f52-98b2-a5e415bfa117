﻿#include "Bridge_arc_arc_2.h"
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <stdio.h>
#include "projectPointsOnPlane.h"
#include "findMinIndex.h"
#include "U_matrix.h"
#include "Quasi_Bspline_K_1.h"

// �������Ƶ����麯��
double** create_control_point_array_Bridge_arc_arc_2(const ControlPoint3_Bridge_arc_arc_2* control_point_3) {
    if (control_point_3 == NULL) {
        return NULL;
    }

    // ����5�е��ڴ�
    double** control_point_array = (double**)malloc(5 * sizeof(double*));
    if (control_point_array == NULL) {
        return NULL;
    }

    // Ϊÿһ�з���3�е��ڴ�
    for (int i = 0; i < 5; i++) {
        control_point_array[i] = (double*)malloc(3 * sizeof(double));
        if (control_point_array[i] == NULL) {
            // �ͷ��ѷ�����ڴ�
            for (int j = 0; j < i; j++) {
                free(control_point_array[j]);
            }
            free(control_point_array);
            return NULL;
        }
    }

    // �����Ƶ�����
    // P0
    control_point_array[0][0] = control_point_3->P0.x;
    control_point_array[0][1] = control_point_3->P0.y;
    control_point_array[0][2] = control_point_3->P0.z;

    // P1
    control_point_array[1][0] = control_point_3->P1.x;
    control_point_array[1][1] = control_point_3->P1.y;
    control_point_array[1][2] = control_point_3->P1.z;

    // P2
    control_point_array[2][0] = control_point_3->P2.x;
    control_point_array[2][1] = control_point_3->P2.y;
    control_point_array[2][2] = control_point_3->P2.z;

    // P3
    control_point_array[3][0] = control_point_3->P3.x;
    control_point_array[3][1] = control_point_3->P3.y;
    control_point_array[3][2] = control_point_3->P3.z;

    // P4
    control_point_array[4][0] = control_point_3->P4.x;
    control_point_array[4][1] = control_point_3->P4.y;
    control_point_array[4][2] = control_point_3->P4.z;

    return control_point_array;
}

// �ͷſ��Ƶ������ڴ�
void free_control_point_array_Bridge_arc_arc_2(double** control_point_array) {
    if (control_point_array == NULL) {
        return;
    }

    // �ͷ�ÿһ��
    for (int i = 0; i < 5; i++) {
        if (control_point_array[i] != NULL) {
            free(control_point_array[i]);
        }
    }

    // �ͷ�����ָ��
    free(control_point_array);
}

// ��ʼ��BridgeResult�ṹ�壬�����ڴ�
void init_bridge_result_Bridge_arc_arc_2(BridgeResult_Bridge_arc_arc_2* result, int max_iterations) {
    if (result == NULL) {
        return;
    }

    // ��ʼ�������ֶ�Ϊ0
    memset(result, 0, sizeof(BridgeResult_Bridge_arc_arc_2));

    // ��������������
    result->max_iterations = max_iterations;

    // �̶�ͶӰ������Ϊ5���������㷨�����ģ�
    result->num_projected_points = 5;

    // Ϊ��̬��������ڴ�
    result->error_values = (double*)malloc(max_iterations * sizeof(double));
    result->M = (double*)malloc(max_iterations * sizeof(double));
    result->M_imag = (double*)malloc(max_iterations * sizeof(double));
    result->M_is_complex = (int*)malloc(max_iterations * sizeof(int));
    result->ER = (double*)malloc(max_iterations * sizeof(double));

    // ΪͶӰ����������ڴ�
    result->projected_points = (double**)malloc(result->num_projected_points * sizeof(double*));
    if (result->projected_points != NULL) {
        for (int i = 0; i < result->num_projected_points; i++) {
            result->projected_points[i] = (double*)malloc(3 * sizeof(double));
            if (result->projected_points[i] != NULL) {
                // ��ʼ��Ϊ0
                result->projected_points[i][0] = 0.0;
                result->projected_points[i][1] = 0.0;
                result->projected_points[i][2] = 0.0;
            }
        }
    }

    // ��ʼ��control_point_3�ṹ��
    result->control_point_3.P0.x = 0.0;
    result->control_point_3.P0.y = 0.0;
    result->control_point_3.P0.z = 0.0;

    result->control_point_3.P1.x = 0.0;
    result->control_point_3.P1.y = 0.0;
    result->control_point_3.P1.z = 0.0;

    result->control_point_3.P2.x = 0.0;
    result->control_point_3.P2.y = 0.0;
    result->control_point_3.P2.z = 0.0;

    result->control_point_3.P3.x = 0.0;
    result->control_point_3.P3.y = 0.0;
    result->control_point_3.P3.z = 0.0;

    result->control_point_3.P4.x = 0.0;
    result->control_point_3.P4.y = 0.0;
    result->control_point_3.P4.z = 0.0;

    // ����ڴ�����Ƿ�ɹ�
    if (result->error_values == NULL || result->M == NULL ||
        result->M_imag == NULL || result->M_is_complex == NULL ||
        result->ER == NULL || result->projected_points == NULL) {
        // �ͷ��ѷ�����ڴ�
        free_bridge_result_Bridge_arc_arc_2(result);
        return;
    }

    // ��ʼ������
    for (int i = 0; i < max_iterations; i++) {
        result->error_values[i] = 0.0;
        result->M[i] = 0.0;
        result->M_imag[i] = 0.0;
        result->M_is_complex[i] = 0;
        result->ER[i] = 0.0;
    }
}

// �ͷ�BridgeResult�ṹ���еĶ�̬�ڴ�
void free_bridge_result_Bridge_arc_arc_2(BridgeResult_Bridge_arc_arc_2* result) {
    if (result == NULL) {
        return;
    }

    // �ͷŶ�̬������ڴ�
    if (result->error_values) free(result->error_values);
    if (result->M) free(result->M);
    if (result->M_imag) free(result->M_imag);
    if (result->M_is_complex) free(result->M_is_complex);
    if (result->ER) free(result->ER);

    // �ͷ�ͶӰ������
    if (result->projected_points) {
        for (int i = 0; i < result->num_projected_points; i++) {
            if (result->projected_points[i]) {
                free(result->projected_points[i]);
            }
        }
        free(result->projected_points);
    }

    // �ͷ�P����
    if (result->P) {
        for (int i = 0; i < result->P_rows; i++) {
            if (result->P[i]) {
                free(result->P[i]);
            }
        }
        free(result->P);
    }

    // �ͷ�X����
    if (result->X) {
        for (int i = 0; i < result->X_rows; i++) {
            if (result->X[i]) {
                free(result->X[i]);
            }
        }
        free(result->X);
    }

    // ��ָ����ΪNULL����ֹ����ָ��
    result->error_values = NULL;
    result->M = NULL;
    result->M_imag = NULL;
    result->M_is_complex = NULL;
    result->ER = NULL;
    result->projected_points = NULL;
    result->P = NULL;
    result->X = NULL;
}

// ������������һ����ά����
static Vector3D_Bridge_arc_arc_2 normalize_vector_Bridge_arc_arc_2(Vector3D_Bridge_arc_arc_2 v) {
    double length = sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
    if (length > 0) {
        v.x /= length;
        v.y /= length;
        v.z /= length;
    }
    return v;
}

// ����������ʹ�ñ任����ת������
static Vector3D_Bridge_arc_arc_2 transform_vector_Bridge_arc_arc_2(const double C[3][3], Vector3D_Bridge_arc_arc_2 v) {
    Vector3D_Bridge_arc_arc_2 result;
    if (C == NULL) {
        result.x = v.x;
        result.y = v.y;
        result.z = v.z;
        return result;
    }
    result.x = C[0][0] * v.x + C[0][1] * v.y + C[0][2] * v.z;
    result.y = C[1][0] * v.x + C[1][1] * v.y + C[1][2] * v.z;
    result.z = C[2][0] * v.x + C[2][1] * v.y + C[2][2] * v.z;
    return result;
}

// �����������������������ĵ��
static double dot_product_Bridge_arc_arc_2(Vector3D_Bridge_arc_arc_2 v1, Vector3D_Bridge_arc_arc_2 v2) {
    return v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
}

// �����������������������Ĳ��
static Vector3D_Bridge_arc_arc_2 cross_product_Bridge_arc_arc_2(Vector3D_Bridge_arc_arc_2 v1, Vector3D_Bridge_arc_arc_2 v2) {
    Vector3D_Bridge_arc_arc_2 result;
    result.x = v1.y * v2.z - v1.z * v2.y;
    result.y = v1.z * v2.x - v1.x * v2.z;
    result.z = v1.x * v2.y - v1.y * v2.x;
    return result;
}

// ���ŽӺ���
BridgeResult_Bridge_arc_arc_2 Bridge_arc_arc_2(const double C1[3][3], const double C2[3][3],
    double** arc_1, int arc_1_rows, int arc_1_cols,
    double** arc_2, int arc_2_rows, int arc_2_cols,
    double u1, double u2) {
    BridgeResult_Bridge_arc_arc_2 result;

    // ʹ���µĳ�ʼ��������Ĭ��100�ε���
    init_bridge_result_Bridge_arc_arc_2(&result, 100);

    // ����ָ��
    if (arc_1 == NULL || arc_2 == NULL || C1 == NULL || C2 == NULL ||
        result.error_values == NULL || result.M == NULL ||
        result.M_imag == NULL || result.M_is_complex == NULL ||
        result.ER == NULL) {
        // ������κ�ָ��ΪNULL���ͷ���Դ������
        free_bridge_result_Bridge_arc_arc_2(&result);
        return result;
    }

    // ���㻡������
    int arc_num_1 = (int)(u1 / 0.05);
    int arc_num_2 = (int)(u2 / 0.05);

    // ȷ�������ڷ�Χ��
    if (arc_num_1 >= arc_1_rows) arc_num_1 = arc_1_rows - 1;
    if (arc_num_2 >= arc_2_rows) arc_num_2 = arc_2_rows - 1;

    // ��鵱ǰ��ָ���Ƿ�Ϊ��
    if (arc_1[arc_num_1] == NULL || arc_2[arc_num_2] == NULL) {
        free_bridge_result_Bridge_arc_arc_2(&result);
        return result;
    }

    // ��arc_1��ȡ����
    result.K1 = arc_1[arc_num_1][0];
    result.D1.x = arc_1[arc_num_1][1];
    result.D1.y = arc_1[arc_num_1][2];
    result.D1.z = arc_1[arc_num_1][3];
    result.N1.x = arc_1[arc_num_1][7];
    result.N1.y = arc_1[arc_num_1][8];
    result.N1.z = arc_1[arc_num_1][9];
    result.B1.x = arc_1[arc_num_1][10];
    result.B1.y = arc_1[arc_num_1][11];
    result.B1.z = arc_1[arc_num_1][12];
    result.P0.x = arc_1[arc_num_1][13];
    result.P0.y = arc_1[arc_num_1][14];
    result.P0.z = arc_1[arc_num_1][15];
    result.U1 = u1;

    // ��arc_2��ȡ����
    result.K2 = arc_2[arc_num_2][0];
    result.D2.x = arc_2[arc_num_2][1];
    result.D2.y = arc_2[arc_num_2][2];
    result.D2.z = arc_2[arc_num_2][3];
    result.N2.x = arc_2[arc_num_2][7];
    result.N2.y = arc_2[arc_num_2][8];
    result.N2.z = arc_2[arc_num_2][9];
    result.B2.x = arc_2[arc_num_2][10];
    result.B2.y = arc_2[arc_num_2][11];
    result.B2.z = arc_2[arc_num_2][12];
    result.P4.x = arc_2[arc_num_2][13];
    result.P4.y = arc_2[arc_num_2][14];
    result.P4.z = arc_2[arc_num_2][15];
    result.U2 = u2;

    // ���arc_1�����һ���Ƿ�Ϊ��
    if (arc_1[arc_1_rows - 1] == NULL) {
        return result;
    }

    // ��arc_1�����һ����ȡDe
    result.De.x = arc_1[arc_1_rows - 1][1];
    result.De.y = arc_1[arc_1_rows - 1][2];
    result.De.z = arc_1[arc_1_rows - 1][3];

    // ���arc_2�ĵ�һ���Ƿ�Ϊ��
    if (arc_2[0] == NULL) {
        return result;
    }

    // ��arc_2�ĵ�һ����ȡDs
    result.Ds.x = arc_2[0][1];
    result.Ds.y = arc_2[0][2];
    result.Ds.z = arc_2[0][3];

    // ��arc_1�����һ����ȡPq
    result.Pq.x = arc_1[arc_1_rows - 1][13];
    result.Pq.y = arc_1[arc_1_rows - 1][14];
    result.Pq.z = arc_1[arc_1_rows - 1][15];

    // ��һ������
    result.D1 = normalize_vector_Bridge_arc_arc_2(result.D1);
    result.D1 = normalize_vector_Bridge_arc_arc_2(result.D1);  // ����MATLAB�����е�˫�ع�һ��
    result.N1 = normalize_vector_Bridge_arc_arc_2(result.N1);
    result.N1 = normalize_vector_Bridge_arc_arc_2(result.N1);  // ����MATLAB�����е�˫�ع�һ��
    result.B1 = normalize_vector_Bridge_arc_arc_2(result.B1);
    result.B1 = normalize_vector_Bridge_arc_arc_2(result.B1);  // ����MATLAB�����е�˫�ع�һ��

    result.D2 = normalize_vector_Bridge_arc_arc_2(result.D2);
    result.D2 = normalize_vector_Bridge_arc_arc_2(result.D2);  // ����MATLAB�����е�˫�ع�һ��
    result.N2 = normalize_vector_Bridge_arc_arc_2(result.N2);
    result.N2 = normalize_vector_Bridge_arc_arc_2(result.N2);  // ����MATLAB�����е�˫�ع�һ��
    result.B2 = normalize_vector_Bridge_arc_arc_2(result.B2);
    result.B2 = normalize_vector_Bridge_arc_arc_2(result.B2);  // ����MATLAB�����е�˫�ع�һ��

    // ʹ�ñ任����任������
    result.N1 = transform_vector_Bridge_arc_arc_2(C1, result.N1);
    result.N2 = transform_vector_Bridge_arc_arc_2(C2, result.N2);

    // �����Ž�����
    if (result.K2 != 0) {
        // ��ʼ��m��nֵ
        result.m = 0.01; // ��Сֵ��ʼm (alpha)

        // ����Ѱ�Һ���ֵ������MATLAB�������һ�ε�����
        for (int count = 1; count <= 100; count++) {
            result.m = count * 0.01; // ����mֵ

            // ����P1, P3, P2, P22��Pc����MATLAB����һ��
            // P1 = P0 + m*D1
            result.P1.x = result.P0.x + result.m * result.D1.x;
            result.P1.y = result.P0.y + result.m * result.D1.y;
            result.P1.z = result.P0.z + result.m * result.D1.z;

            // ����P2���м�ֵ
            Vector3D_Bridge_arc_arc_2 vec_diff;
            vec_diff.x = result.P4.x - result.P0.x;
            vec_diff.y = result.P4.y - result.P0.y;
            vec_diff.z = result.P4.z - result.P0.z;

            Vector3D_Bridge_arc_arc_2 K1_term;
            K1_term.x = 3 * result.K1 * (result.m * result.m) * result.N1.x;
            K1_term.y = 3 * result.K1 * (result.m * result.m) * result.N1.y;
            K1_term.z = 3 * result.K1 * (result.m * result.m) * result.N1.z;

            Vector3D_Bridge_arc_arc_2 vec_term;
            vec_term.x = vec_diff.x - K1_term.x;
            vec_term.y = vec_diff.y - K1_term.y;
            vec_term.z = vec_diff.z - K1_term.z;

            // ��������
            double dot_term1 = dot_product_Bridge_arc_arc_2(vec_term, result.B2);
            double dot_term2 = dot_product_Bridge_arc_arc_2(result.D1, result.B2);

            // ���� sy1 = P0 + 3*K1*(m^2)*N1 + D1*((P4-P0) - 3*K1*(m^2)*N1)*B2')/(D1*B2')
            Vector3D_Bridge_arc_arc_2 sy1;
            sy1.x = result.P0.x + K1_term.x + result.D1.x * (dot_term1 / dot_term2);
            sy1.y = result.P0.y + K1_term.y + result.D1.y * (dot_term1 / dot_term2);
            sy1.z = result.P0.z + K1_term.z + result.D1.z * (dot_term1 / dot_term2);

            // �洢P2��ԭʼ��sy1
            result.P2 = sy1;
            Vector3D_Bridge_arc_arc_2 original_sy1 = sy1;

            // ���� sy1 = sy1 - P4
            sy1.x = sy1.x - result.P4.x;
            sy1.y = sy1.y - result.P4.y;
            sy1.z = sy1.z - result.P4.z;

            // ���� z1 = 3*K2*N2
            Vector3D_Bridge_arc_arc_2 z1;
            z1.x = 3 * result.K2 * result.N2.x;
            z1.y = 3 * result.K2 * result.N2.y;
            z1.z = 3 * result.K2 * result.N2.z;

            // ���� z2 = (P4-P0)*B1' ��Ϊ����
            double dot_vec_diff_B1 = dot_product_Bridge_arc_arc_2(vec_diff, result.B1);

            // ���� z2 = (P4-P0)*B1'*D2
            // ��MATLAB�У�����һ����������
            Vector3D_Bridge_arc_arc_2 z2;
            z2.x = dot_vec_diff_B1 * result.D2.x;
            z2.y = dot_vec_diff_B1 * result.D2.y;
            z2.z = dot_vec_diff_B1 * result.D2.z;

            // ���� z3 = z1*B1'*D2
            // ��MATLAB�У���Ӧ����һ����������
            double dot_z1_B1 = dot_product_Bridge_arc_arc_2(z1, result.B1);
            Vector3D_Bridge_arc_arc_2 z3;
            z3.x = dot_z1_B1 * result.D2.x;
            z3.y = dot_z1_B1 * result.D2.y;
            z3.z = dot_z1_B1 * result.D2.z;

            // ���� z4 = D2*B1'
            double z4 = dot_product_Bridge_arc_arc_2(result.D2, result.B1);

            // ���� sy1 = sy1 + z2/z4
            sy1.x = sy1.x + z2.x / z4;
            sy1.y = sy1.y + z2.y / z4;
            sy1.z = sy1.z + z2.z / z4;

            // �洢������sy1
            result.sy1 = sy1;

            // ��MATLAB�У�ȷʵѡ����sy1.y��ֵ������sy2
            // ���㵥һ����ֵsy2 = sy1.y/((z1.y)-(z3.y/z4))
            double z3_div_z4 = z3.y / z4;
            double denominator = z1.y - z3_div_z4;

            // ����sy2��Ϊ��һ����
            double sy2_scalar = 0.0;
            if (fabs(denominator) > 1e-10) {
                sy2_scalar = sy1.y / denominator;
            }

            // Ϊ�����������ӡƥ�䣬������ֵ�洢��sy2��y������
            result.sy2.x = 0.0;
            result.sy2.y = sy2_scalar;  // ʵ����MATLAB���Ǳ������������Ǵ洢��y������
            result.sy2.z = 0.0;

            // ���� beta = sqrt(sy2)
            // ��MATLAB�У�sy2��һ�������������Ǹ����������������
            double beta;
            int is_complex = 0;  // ����Ƿ���Ҫʹ�ø�������
            double complex_part = 0.0;  // �洢�����鲿ֵ

            if (sy2_scalar < 0) {
                // ���ڸ�����MATLAB��sqrt�᷵������
                // �������Ǽ����鲿��ֵ sqrt(|sy2_scalar|)
                is_complex = 1;
                beta = 0.0;  // ʵ��Ϊ0
                complex_part = sqrt(fabs(sy2_scalar));

                // ��ǽ��Ϊ����
                result.is_complex = 1;


            }
            else {
                beta = sqrt(sy2_scalar);
                result.is_complex = 0;
            }

            // ��beta��ֵ��result.n
            result.n = beta;

            // ���� P3 = P4 - n*D2
            if (is_complex) {
                // ���ڸ�����ʽ: P3 = P4 - (0 + i*complex_part)*D2
                // ��MATLAB�У���ᵼ��P3Ҳ�Ǹ�����ʵ�����鲿�ֿ�����

                // ʵ�� = P4 (��Ϊ0*D2=0)
                result.P3.x = result.P4.x;
                result.P3.y = result.P4.y;
                result.P3.z = result.P4.z;

                // �洢������ʽ��P3
                result.P3_complex.real_x = result.P4.x;
                result.P3_complex.real_y = result.P4.y;
                result.P3_complex.real_z = result.P4.z;

                // �鲿 = -complex_part*D2��ע�⸺�ţ���MATLABһ�£�
                result.P3_complex.imag_x = -complex_part * result.D2.x;
                result.P3_complex.imag_y = -complex_part * result.D2.y;
                result.P3_complex.imag_z = -complex_part * result.D2.z;


            }
            else {
                result.P3.x = result.P4.x - result.n * result.D2.x;
                result.P3.y = result.P4.y - result.n * result.D2.y;
                result.P3.z = result.P4.z - result.n * result.D2.z;

                // ��ո����ṹ
                result.P3_complex.real_x = result.P3.x;
                result.P3_complex.real_y = result.P3.y;
                result.P3_complex.real_z = result.P3.z;
                result.P3_complex.imag_x = 0.0;
                result.P3_complex.imag_y = 0.0;
                result.P3_complex.imag_z = 0.0;
            }

            // ���� P22 = P4 + 3*K2*(n^2)*N2 - D2*((P4-P0+3*K2*(n^2)*N2)*B1')/(D2*B1')
            Vector3D_Bridge_arc_arc_2 K2_term;
            K2_term.x = 3 * result.K2 * (result.n * result.n) * result.N2.x;
            K2_term.y = 3 * result.K2 * (result.n * result.n) * result.N2.y;
            K2_term.z = 3 * result.K2 * (result.n * result.n) * result.N2.z;

            Vector3D_Bridge_arc_arc_2 vec_diff2;
            vec_diff2.x = result.P4.x - result.P0.x + K2_term.x;
            vec_diff2.y = result.P4.y - result.P0.y + K2_term.y;
            vec_diff2.z = result.P4.z - result.P0.z + K2_term.z;

            double dot_term3 = dot_product_Bridge_arc_arc_2(vec_diff2, result.B1);
            double dot_term4 = dot_product_Bridge_arc_arc_2(result.D2, result.B1);

            result.P22.x = result.P4.x + K2_term.x - result.D2.x * (dot_term3 / dot_term4);
            result.P22.y = result.P4.y + K2_term.y - result.D2.y * (dot_term3 / dot_term4);
            result.P22.z = result.P4.z + K2_term.z - result.D2.z * (dot_term3 / dot_term4);

            // ���� Pc = 0.25*P1 + 0.5*P2 + 0.25*P3
            if (is_complex) {
                // ��MATLAB�У���P3Ϊ����ʱ��Pc�ļ�����ѭ������������
                // Pc = 0.25*P1 + 0.5*P2 + 0.25*(P3_real + i*P3_imag)

                // ʵ������: 0.25*P1 + 0.5*P2 + 0.25*P3_real
                result.Pc.x = 0.25 * result.P1.x + 0.5 * result.P2.x + 0.25 * result.P3.x;
                result.Pc.y = 0.25 * result.P1.y + 0.5 * result.P2.y + 0.25 * result.P3.y;
                result.Pc.z = 0.25 * result.P1.z + 0.5 * result.P2.z + 0.25 * result.P3.z;

                // �鲿����: 0.25*i*complex_part*D1
                result.Pc_complex.real_x = result.Pc.x;
                result.Pc_complex.real_y = result.Pc.y;
                result.Pc_complex.real_z = result.Pc.z;
                result.Pc_complex.imag_x = 0.25 * complex_part * result.D1.x;
                result.Pc_complex.imag_y = 0.25 * complex_part * result.D1.y;
                result.Pc_complex.imag_z = 0.25 * complex_part * result.D1.z;



                // Ϊerror���㣬����ʹ��Pc��Pq�ľ���
                // ��MATLAB�У���Pc�Ǹ���ʱ����ʹ��absֵ����
                // ��������ֻʹ��ʵ������������ƥ��MATLAB����Ϊ
                Vector3D_Bridge_arc_arc_2 diff;
                diff.x = result.Pc.x - result.Pq.x;
                diff.y = result.Pc.y - result.Pq.y;
                diff.z = result.Pc.z - result.Pq.z;
                double current_error = sqrt(diff.x * diff.x + diff.y * diff.y + diff.z * diff.z);

                // ���⣬����MATLAB��error�����洢�������������ҲӦ�ô洢�������ֵĴ�С
                // ����ģ������
                double complex_magnitude = sqrt(
                    result.Pc_complex.imag_x * result.Pc_complex.imag_x +
                    result.Pc_complex.imag_y * result.Pc_complex.imag_y +
                    result.Pc_complex.imag_z * result.Pc_complex.imag_z
                );

                // ����errorֵ
                current_error = sqrt(current_error * current_error + complex_magnitude * complex_magnitude);

                // ���浱ǰ���������ֵ��error_values����
                result.error_values[count - 1] = current_error;
                result.error = current_error;


            }
            else {
                // ������ʵ������
                result.Pc.x = 0.25 * result.P1.x + 0.5 * result.P2.x + 0.25 * result.P3.x;
                result.Pc.y = 0.25 * result.P1.y + 0.5 * result.P2.y + 0.25 * result.P3.y;
                result.Pc.z = 0.25 * result.P1.z + 0.5 * result.P2.z + 0.25 * result.P3.z;

                // ��ո����ṹ
                result.Pc_complex.real_x = result.Pc.x;
                result.Pc_complex.real_y = result.Pc.y;
                result.Pc_complex.real_z = result.Pc.z;
                result.Pc_complex.imag_x = 0.0;
                result.Pc_complex.imag_y = 0.0;
                result.Pc_complex.imag_z = 0.0;

                // ��������ӦMATLAB�е� error(count)=norm(Pc-Pq)
                Vector3D_Bridge_arc_arc_2 diff;
                diff.x = result.Pc.x - result.Pq.x;
                diff.y = result.Pc.y - result.Pq.y;
                diff.z = result.Pc.z - result.Pq.z;
                double current_error = sqrt(diff.x * diff.x + diff.y * diff.y + diff.z * diff.z);

                // ���浱ǰ���������ֵ��error_values����
                result.error_values[count - 1] = current_error;
                result.error = current_error;
            }
            // ���µ�������
            result.num_iterations = count;

            // ΪprojectPointsOnPlane׼������
            double points[5][3] = {
                {result.P0.x, result.P0.y, result.P0.z},
                {result.P1.x, result.P1.y, result.P1.z},
                {result.P2.x, result.P2.y, result.P2.z},
                {result.P3.x, result.P3.y, result.P3.z},
                {result.P4.x, result.P4.y, result.P4.z}
            };

            double De[3] = { result.De.x, result.De.y, result.De.z };
            double Ds[3] = { result.Ds.x, result.Ds.y, result.Ds.z };
            double Pq[3] = { result.Pq.x, result.Pq.y, result.Pq.z };

            // ����projectPointsOnPlane��ȡͶӰ��
            projectPointsOnPlane_dynamic(De, Ds, Pq, points, 5, result.projected_points);

            // ����ͶӰ��֮�����������ӦMATLAB�е�:
            // P1P2=projected_points(3,:)-projected_points(2,:)
            // P3P2=projected_points(4,:)-projected_points(3,:)
            // P4P0=projected_points(5,:)-projected_points(1,:)

            // P1P2 = projected_points[2] - projected_points[1]
            result.P1P2.x = result.projected_points[2][0] - result.projected_points[1][0];
            result.P1P2.y = result.projected_points[2][1] - result.projected_points[1][1];
            result.P1P2.z = result.projected_points[2][2] - result.projected_points[1][2];

            // P3P2 = projected_points[3] - projected_points[2]
            result.P3P2.x = result.projected_points[3][0] - result.projected_points[2][0];
            result.P3P2.y = result.projected_points[3][1] - result.projected_points[2][1];
            result.P3P2.z = result.projected_points[3][2] - result.projected_points[2][2];

            // P4P0 = projected_points[4] - projected_points[0]
            result.P4P0.x = result.projected_points[4][0] - result.projected_points[0][0];
            result.P4P0.y = result.projected_points[4][1] - result.projected_points[0][1];
            result.P4P0.z = result.projected_points[4][2] - result.projected_points[0][2];

            // ���� A=P1P2*P4P0'����P1P2��P4P0�ĵ��
            result.A = dot_product_Bridge_arc_arc_2(result.P1P2, result.P4P0);

            // ���� B=P3P2*P4P0'����P3P2��P4P0�ĵ��
            result.B = dot_product_Bridge_arc_arc_2(result.P3P2, result.P4P0);

            // ���� cross(D1,N1)��D1��N1�Ĳ��
            Vector3D_Bridge_arc_arc_2 D1N1_cross = cross_product_Bridge_arc_arc_2(result.D1, result.N1);

            // ���� cross(P1P2,P3P2)��P1P2��P3P2�Ĳ��
            Vector3D_Bridge_arc_arc_2 P1P2P3P2_cross = cross_product_Bridge_arc_arc_2(result.P1P2, result.P3P2);

            // ���� C=cross(D1,N1)*cross(P1P2,P3P2)'��������˽���ĵ��
            result.C = dot_product_Bridge_arc_arc_2(D1N1_cross, P1P2P3P2_cross);

            // ���� cross(D2,N2)��D2��N2�Ĳ��
            Vector3D_Bridge_arc_arc_2 D2N2_cross = cross_product_Bridge_arc_arc_2(result.D2, result.N2);

            // ���� D=cross(D2,N2)*cross(P1P2,P3P2)'��������˽���ĵ��
            result.D = dot_product_Bridge_arc_arc_2(D2N2_cross, P1P2P3P2_cross);

            // �洢��ǰ������nֵ��M����
            if (is_complex) {
                // ����Ǹ������ֱ�洢ʵ�����鲿
                result.M[count - 1] = 0.0; // ʵ��Ϊ0
                result.M_imag[count - 1] = complex_part; // �鲿
                result.M_is_complex[count - 1] = 1; // ���Ϊ����

            }
            else {
                // �����ʵ����ֻ�洢ʵ��
                result.M[count - 1] = result.n;
                result.M_imag[count - 1] = 0.0;
                result.M_is_complex[count - 1] = 0;
            }



            // ����Ƿ�Ϊʵ���������ʵ����A,B,C,D���κ�һ��Ϊ��ֵ��������ѭ��
            if (!is_complex) {
                if (result.A < 0 || result.B < 0 || result.C < 0 || result.D < 0) {
                    printf("�ڵ��� %d �з������� A<0 || B<0 || C<0 || D<0 ����������ѭ��\n", count);
                    break;
                }
            }
        }

        // ����M������鲿
        int valid_count = 0;
        double* imagPart = (double*)malloc(result.num_iterations * sizeof(double));

        // ת��MATLAB����: imagPart = imag(M)
        // ��MATLAB�У�imag(M)�᷵������Ԫ�ص��鲿�������Ƿ��Ǹ���
        for (int i = 0; i < result.num_iterations; i++) {
            imagPart[i] = result.M_imag[i];
        }

        // ת��MATLAB����: realMatrix = imagPart == 0
        // ��MATLAB�У�����һ����Ԫ�صıȽϣ�����һ����������
        int* realMatrix = (int*)malloc(result.num_iterations * sizeof(int));
        for (int i = 0; i < result.num_iterations; i++) {
            realMatrix[i] = (fabs(imagPart[i]) < 1e-10) ? 1 : 0;
        }

        // ת��MATLAB����: ER = error
        double* ER = (double*)malloc(result.num_iterations * sizeof(double));
        for (int i = 0; i < result.num_iterations; i++) {
            ER[i] = result.error_values[i];
        }

        // ת��MATLAB����: ER(~realMatrix) = 1000
        // ��MATLAB�У�~realMatrix�ǶԲ����������Ԫ��ȡ��
        for (int i = 0; i < result.num_iterations; i++) {
            if (!realMatrix[i]) {  // �������ʵ��
                ER[i] = 1000.0;
            }
        }

        // ��ER����ת��ΪfindMinIndex����ʹ�õĸ�ʽ
        double** ER_matrix = (double**)malloc(1 * sizeof(double*));
        ER_matrix[0] = (double*)malloc(result.num_iterations * sizeof(double));
        for (int i = 0; i < result.num_iterations; i++) {
            ER_matrix[0][i] = ER[i];
        }

        // ����findMinIndex�ҳ���Сֵ������
        int row = 0, col = 0;
        findMinIndex(ER_matrix, 1, result.num_iterations, &row, &col);

        // ����MATLAB��������n��m
        result.m = 0.002;  // �̶�ֵ
        result.n = 0.002;  // �̶�ֵ

        // ���m��nΪ0������Ϊ0.01
        if (result.m == 0) {
            result.m = 0.01;
        }
        if (result.n == 0) {
            result.n = 0.01;
        }

        // �ͷŷ�����ڴ�
        free(imagPart);
        free(realMatrix);
        free(ER);
        free(ER_matrix[0]);
        free(ER_matrix);

        // ʹ����ȷ��m��nֵ����P1��P3
        // P1 = P0 + m*D1
        result.P1.x = result.P0.x + result.m * result.D1.x;
        result.P1.y = result.P0.y + result.m * result.D1.y;
        result.P1.z = result.P0.z + result.m * result.D1.z;

        // P2 = P0 + 3*K1*(m^2)*N1 + D1*((P4-P0-3*K1*(m^2)*N1)*B2')/(D1*B2')
        // ���� 3*K1*(m^2)*N1
        Vector3D_Bridge_arc_arc_2 K1_term;
        K1_term.x = 3 * result.K1 * (result.m * result.m) * result.N1.x;
        K1_term.y = 3 * result.K1 * (result.m * result.m) * result.N1.y;
        K1_term.z = 3 * result.K1 * (result.m * result.m) * result.N1.z;

        // ���� P4-P0-3*K1*(m^2)*N1
        Vector3D_Bridge_arc_arc_2 vec_diff;
        vec_diff.x = result.P4.x - result.P0.x - K1_term.x;
        vec_diff.y = result.P4.y - result.P0.y - K1_term.y;
        vec_diff.z = result.P4.z - result.P0.z - K1_term.z;

        // ���� (P4-P0-3*K1*(m^2)*N1)*B2'
        double dot_term = dot_product_Bridge_arc_arc_2(vec_diff, result.B2);

        // ���� D1*B2'
        double dot_denom = dot_product_Bridge_arc_arc_2(result.D1, result.B2);

        // �������յ�P2
        result.P2.x = result.P0.x + K1_term.x + result.D1.x * (dot_term / dot_denom);
        result.P2.y = result.P0.y + K1_term.y + result.D1.y * (dot_term / dot_denom);
        result.P2.z = result.P0.z + K1_term.z + result.D1.z * (dot_term / dot_denom);

        // P3 = P4 - n*D2
        result.P3.x = result.P4.x - result.n * result.D2.x;
        result.P3.y = result.P4.y - result.n * result.D2.y;
        result.P3.z = result.P4.z - result.n * result.D2.z;

        // ���¼���Pc
        result.Pc.x = 0.25 * result.P1.x + 0.5 * result.P2.x + 0.25 * result.P3.x;
        result.Pc.y = 0.25 * result.P1.y + 0.5 * result.P2.y + 0.25 * result.P3.y;
        result.Pc.z = 0.25 * result.P1.z + 0.5 * result.P2.z + 0.25 * result.P3.z;

        // �����µ����ֵ
        Vector3D_Bridge_arc_arc_2 diff;
        diff.x = result.Pc.x - result.Pq.x;
        diff.y = result.Pc.y - result.Pq.y;
        diff.z = result.Pc.z - result.Pq.z;
        result.error = sqrt(diff.x * diff.x + diff.y * diff.y + diff.z * diff.z);

     
        // �����Ƶ㱣�浽control_point_3�ṹ����
        result.control_point_3.P0 = result.P0;
        result.control_point_3.P1 = result.P1;
        result.control_point_3.P2 = result.P2;
        result.control_point_3.P3 = result.P3;
        result.control_point_3.P4 = result.P4;



        // �������Ƶ�����
        double** control_point_array = create_control_point_array_Bridge_arc_arc_2(&result.control_point_3);

        // �����鲿����
        if (valid_count > 0) {
            // ��վ�ֵ
            for (int i = 0; i < valid_count && i < result.max_iterations; i++) {
                result.M_imag[i] = 0;
            }

            int imag_index = 0;
            for (int i = 0; i < result.num_iterations && imag_index < result.max_iterations; i++) {
                if (result.M_is_complex[i]) {
                    result.M_imag[imag_index] = result.M_imag[i];
                    imag_index++;
                }
            }
        }
    }
    // �ڶ��������K2==0 && K1!=0
    // ... existing code ...
    else if (result.K2 == 0 && result.K1 != 0) {

        // 首先计算P2
        Vector3D_Bridge_arc_arc_2 vec_diff;
        vec_diff.x = result.P4.x - result.P0.x;
        vec_diff.y = result.P4.y - result.P0.y;
        vec_diff.z = result.P4.z - result.P0.z;

        double dot_term_P2 = dot_product_Bridge_arc_arc_2(vec_diff, result.B1);
        double dot_denom_P2 = dot_product_Bridge_arc_arc_2(result.D2, result.B1);

        result.P2.x = result.P4.x - result.D2.x * (dot_term_P2 / dot_denom_P2);
        result.P2.y = result.P4.y - result.D2.y * (dot_term_P2 / dot_denom_P2);
        result.P2.z = result.P4.z - result.D2.z * (dot_term_P2 / dot_denom_P2);

        // 开始迭代过程
        for (int count = 0; count < 100; count++) {
            result.n = (count + 1) * 0.01;

            // 计算sy1 = P2 - P0
            Vector3D_Bridge_arc_arc_2 sy1;
            sy1.x = result.P2.x - result.P0.x;
            sy1.y = result.P2.y - result.P0.y;
            sy1.z = result.P2.z - result.P0.z;

            // 计算z1 = 3*K1*N1
            Vector3D_Bridge_arc_arc_2 z1;
            z1.x = 3 * result.K1 * result.N1.x;
            z1.y = 3 * result.K1 * result.N1.y;
            z1.z = 3 * result.K1 * result.N1.z;

            // 计算z2 = (P4-P0)*B2'*D1
            double P4_P0_dot_B2 = dot_product_Bridge_arc_arc_2(vec_diff, result.B2);
            Vector3D_Bridge_arc_arc_2 z2;
            z2.x = P4_P0_dot_B2 * result.D1.x;
            z2.y = P4_P0_dot_B2 * result.D1.y;
            z2.z = P4_P0_dot_B2 * result.D1.z;

            // 计算z3 = z1*B2'*D1
            double z1_dot_B2 = dot_product_Bridge_arc_arc_2(z1, result.B2);
            Vector3D_Bridge_arc_arc_2 z3;
            z3.x = z1_dot_B2 * result.D1.x;
            z3.y = z1_dot_B2 * result.D1.y;
            z3.z = z1_dot_B2 * result.D1.z;

            // 计算z4 = D1*B2'
            double z4 = dot_product_Bridge_arc_arc_2(result.D1, result.B2);

            // 计算sy1 = sy1 - z2/z4
            sy1.x = sy1.x - z2.x / z4;
            sy1.y = sy1.y - z2.y / z4;
            sy1.z = sy1.z - z2.z / z4;

            // 计算分母项 z1 + (z3/z4)
            Vector3D_Bridge_arc_arc_2 denominator_vec;
            denominator_vec.x = z1.x + z3.x / z4;
            denominator_vec.y = z1.y + z3.y / z4;
            denominator_vec.z = z1.z + z3.z / z4;

            // 计算sy2 = sy1/(z1 + (z3/z4))
            double sy1_dot_denom = dot_product_Bridge_arc_arc_2(sy1, denominator_vec);
            double denom_dot_denom = dot_product_Bridge_arc_arc_2(denominator_vec, denominator_vec);
            double sy2 = 0.0;
            if (fabs(denom_dot_denom) > 1e-10) {
                sy2 = sy1_dot_denom / denom_dot_denom;
            }

            // 计算m = sqrt(sy2)
            double current_m;
            int is_complex = 0;
            double complex_part = 0.0;

            if (sy2 < 0) {
                is_complex = 1;
                current_m = 0.0;
                complex_part = sqrt(fabs(sy2));
            }
            else {
                current_m = sqrt(sy2);
            }

            // 存储当前迭代的m值到M数组
            result.M[count] = current_m;
            result.M_imag[count] = complex_part;
            result.M_is_complex[count] = is_complex;

            // 计算P1, P3, P22和Pc
            result.P1.x = result.P0.x + current_m * result.D1.x;
            result.P1.y = result.P0.y + current_m * result.D1.y;
            result.P1.z = result.P0.z + current_m * result.D1.z;

            result.P3.x = result.P4.x - result.n * result.D2.x;
            result.P3.y = result.P4.y - result.n * result.D2.y;
            result.P3.z = result.P4.z - result.n * result.D2.z;

            // 计算P22 = P0 + 3*K1*(m^2)*N1 + D1*((P4-P0-3*K1*(m^2)*N1)*B2')/(D1*B2')
            Vector3D_Bridge_arc_arc_2 K1_term;
            K1_term.x = 3 * result.K1 * (current_m * current_m) * result.N1.x;
            K1_term.y = 3 * result.K1 * (current_m * current_m) * result.N1.y;
            K1_term.z = 3 * result.K1 * (current_m * current_m) * result.N1.z;

            Vector3D_Bridge_arc_arc_2 vec_diff2;
            vec_diff2.x = result.P4.x - result.P0.x - K1_term.x;
            vec_diff2.y = result.P4.y - result.P0.y - K1_term.y;
            vec_diff2.z = result.P4.z - result.P0.z - K1_term.z;

            double dot_term3 = dot_product_Bridge_arc_arc_2(vec_diff2, result.B2);
            double dot_term4 = dot_product_Bridge_arc_arc_2(result.D1, result.B2);

            result.P22.x = result.P0.x + K1_term.x + result.D1.x * (dot_term3 / dot_term4);
            result.P22.y = result.P0.y + K1_term.y + result.D1.y * (dot_term3 / dot_term4);
            result.P22.z = result.P0.z + K1_term.z + result.D1.z * (dot_term3 / dot_term4);

            // 计算Pc
            result.Pc.x = 0.25 * result.P1.x + 0.5 * result.P2.x + 0.25 * result.P3.x;
            result.Pc.y = 0.25 * result.P1.y + 0.5 * result.P2.y + 0.25 * result.P3.y;
            result.Pc.z = 0.25 * result.P1.z + 0.5 * result.P2.z + 0.25 * result.P3.z;

            // 计算误差
            Vector3D_Bridge_arc_arc_2 diff;
            diff.x = result.Pc.x - result.Pq.x;
            diff.y = result.Pc.y - result.Pq.y;
            diff.z = result.Pc.z - result.Pq.z;
            double current_error = sqrt(diff.x * diff.x + diff.y * diff.y + diff.z * diff.z);

            result.error_values[count] = current_error;
            result.error = current_error;

            // 更新迭代次数
            result.num_iterations = count + 1;

            // 为projectPointsOnPlane准备数据
            double points[5][3] = {
                {result.P0.x, result.P0.y, result.P0.z},
                {result.P1.x, result.P1.y, result.P1.z},
                {result.P2.x, result.P2.y, result.P2.z},
                {result.P3.x, result.P3.y, result.P3.z},
                {result.P4.x, result.P4.y, result.P4.z}
            };

            double De[3] = { result.De.x, result.De.y, result.De.z };
            double Ds[3] = { result.Ds.x, result.Ds.y, result.Ds.z };
            double Pq[3] = { result.Pq.x, result.Pq.y, result.Pq.z };

            // 调用projectPointsOnPlane获取投影点
            projectPointsOnPlane_dynamic(De, Ds, Pq, points, 5, result.projected_points);

            // 计算投影点之间的向量
            result.P1P2.x = result.projected_points[2][0] - result.projected_points[1][0];
            result.P1P2.y = result.projected_points[2][1] - result.projected_points[1][1];
            result.P1P2.z = result.projected_points[2][2] - result.projected_points[1][2];

            result.P3P2.x = result.projected_points[3][0] - result.projected_points[2][0];
            result.P3P2.y = result.projected_points[3][1] - result.projected_points[2][1];
            result.P3P2.z = result.projected_points[3][2] - result.projected_points[2][2];

            result.P4P0.x = result.projected_points[4][0] - result.projected_points[0][0];
            result.P4P0.y = result.projected_points[4][1] - result.projected_points[0][1];
            result.P4P0.z = result.projected_points[4][2] - result.projected_points[0][2];

            // 计算A, B, C
            result.A = dot_product_Bridge_arc_arc_2(result.P1P2, result.P4P0);
            result.B = dot_product_Bridge_arc_arc_2(result.P3P2, result.P4P0);
            result.C = dot_product_Bridge_arc_arc_2(cross_product_Bridge_arc_arc_2(result.D1, result.N1), cross_product_Bridge_arc_arc_2(result.P1P2, result.P3P2));

            // 检查是否需要退出循环
            if (!is_complex) {
                if (result.A < 0 || result.B < 0 || result.C < 0) {
                    printf("在迭代 %d 中发现条件 A<0 || B<0 || C<0 满足，退出循环\n", count + 1);
                    break;
                }
            }
        }

        // 处理M数组的虚部
        int valid_count = 0;
        double* imagPart = (double*)malloc(result.num_iterations * sizeof(double));

        // 转换MATLAB代码: imagPart = imag(M)
        for (int i = 0; i < result.num_iterations; i++) {
            imagPart[i] = result.M_imag[i];
        }

        // 转换MATLAB代码: realMatrix = imagPart == 0
        int* realMatrix = (int*)malloc(result.num_iterations * sizeof(int));
        for (int i = 0; i < result.num_iterations; i++) {
            realMatrix[i] = (fabs(imagPart[i]) < 1e-10) ? 1 : 0;
        }

        // 转换MATLAB代码: ER = error
        double* ER = (double*)malloc(result.num_iterations * sizeof(double));
        for (int i = 0; i < result.num_iterations; i++) {
            ER[i] = result.error_values[i];
        }

        // 转换MATLAB代码: ER(~realMatrix) = 1000
        for (int i = 0; i < result.num_iterations; i++) {
            if (!realMatrix[i]) {  // 如果不是实数
                ER[i] = 1000.0;
            }
        }

        // 将ER数组转换为findMinIndex函数使用的格式
        double** ER_matrix = (double**)malloc(1 * sizeof(double*));
        ER_matrix[0] = (double*)malloc(result.num_iterations * sizeof(double));
        for (int i = 0; i < result.num_iterations; i++) {
            ER_matrix[0][i] = ER[i];
        }

        // 调用findMinIndex找出最小值位置
        int row = 0, col = 0;
        findMinIndex(ER_matrix, 1, result.num_iterations, &row, &col);

        // 根据MATLAB代码设置n和m
        result.n = col * 0.01;  // 使用找到的最小值位置
        result.m = result.M[col];  // 使用对应的M值



        // 释放分配的内存
        free(imagPart);
        free(realMatrix);
        free(ER);
        free(ER_matrix[0]);
        free(ER_matrix);

        // 使用最终的m和n值重新计算控制点
        result.P1.x = result.P0.x + result.m * result.D1.x;
        result.P1.y = result.P0.y + result.m * result.D1.y;
        result.P1.z = result.P0.z + result.m * result.D1.z;

        result.P3.x = result.P4.x - result.n * result.D2.x;
        result.P3.y = result.P4.y - result.n * result.D2.y;
        result.P3.z = result.P4.z - result.n * result.D2.z;

        // 重新计算Pc
        result.Pc.x = 0.25 * result.P1.x + 0.5 * result.P2.x + 0.25 * result.P3.x;
        result.Pc.y = 0.25 * result.P1.y + 0.5 * result.P2.y + 0.25 * result.P3.y;
        result.Pc.z = 0.25 * result.P1.z + 0.5 * result.P2.z + 0.25 * result.P3.z;

        // 计算最终误差
        Vector3D_Bridge_arc_arc_2 diff;
        diff.x = result.Pc.x - result.Pq.x;
        diff.y = result.Pc.y - result.Pq.y;
        diff.z = result.Pc.z - result.Pq.z;
        result.error = sqrt(diff.x * diff.x + diff.y * diff.y + diff.z * diff.z);

        // 更新control_point_3结构体
        result.control_point_3.P0 = result.P0;
        result.control_point_3.P1 = result.P1;
        result.control_point_3.P2 = result.P2;
        result.control_point_3.P3 = result.P3;
        result.control_point_3.P4 = result.P4;
    }
    // ... existing code ...
// 处理K1==0 && K2==0
    else {

        // 构建矩阵 MM = [D1',-D2'] 和 b = (P4-P0)'
        double MM[3][2];
        double b[3];
        double a[2];

        // 构建 MM = [D1',-D2']
        MM[0][0] = result.D1.x;
        MM[1][0] = result.D1.y;
        MM[2][0] = result.D1.z;

        MM[0][1] = -result.D2.x;
        MM[1][1] = -result.D2.y;
        MM[2][1] = -result.D2.z;

        // 构建 b = (P4-P0)'
        b[0] = result.P4.x - result.P0.x;
        b[1] = result.P4.y - result.P0.y;
        b[2] = result.P4.z - result.P0.z;
        // 解方程 MM * a = b
        // 先求 (MM^T * MM) * a = MM^T * b

        // 计算 MM^T * MM (2x2矩阵)
        double MTM[2][2] = { 0 };
        for (int i = 0; i < 2; i++) {
            for (int j = 0; j < 2; j++) {
                for (int k = 0; k < 3; k++) {
                    MTM[i][j] += MM[k][i] * MM[k][j];
                }
            }
        }

        // 计算 MM^T * b (2x1矩阵)
        double MTb[2] = { 0 };
        for (int i = 0; i < 2; i++) {
            for (int k = 0; k < 3; k++) {
                MTb[i] += MM[k][i] * b[k];
            }
        }

        // 解 MTM * a = MTb
        double det = MTM[0][0] * MTM[1][1] - MTM[0][1] * MTM[1][0];
        if (fabs(det) < 1e-10) {
            printf("矩阵不可逆，使用默认值\n");
            a[0] = 0.01; // m
            a[1] = 0.01; // n
        }
        else {
            a[0] = (MTM[1][1] * MTb[0] - MTM[0][1] * MTb[1]) / det;
            a[1] = (MTM[0][0] * MTb[1] - MTM[1][0] * MTb[0]) / det;
        }
        // 设置m和n
        result.m = a[0];
        result.n = a[1];

        // 计算P2，即中点
        result.P2.x = result.P0.x + a[0] * result.D1.x;
        result.P2.y = result.P0.y + a[0] * result.D1.y;
        result.P2.z = result.P0.z + a[0] * result.D1.z;
        // 为双循环准备error矩阵 (50x50)
        double** error_matrix = (double**)malloc(50 * sizeof(double*));
        if (error_matrix == NULL) {
            printf("内存分配失败\n");
            return result;
        }
        for (int i = 0; i < 50; i++) {
            error_matrix[i] = (double*)malloc(50 * sizeof(double));
            if (error_matrix[i] == NULL) {
                // 释放已分配的内存
                for (int j = 0; j < i; j++) {
                    free(error_matrix[j]);
                }
                free(error_matrix);
                printf("内存分配失败\n");
                return result;
            }
        }

        // 使用双循环计算
        for (int count_m = 0; count_m < 50; count_m++) {
            for (int count_n = 0; count_n < 50; count_n++) {
                double m = 0.01 * (count_m + 1);  // MATLAB从1开始
                double n = 0.01 * (count_n + 1);

                // 计算P1 = P0 + m*D1
                Vector3D_Bridge_arc_arc_2 P1;
                P1.x = result.P0.x + m * result.D1.x;
                P1.y = result.P0.y + m * result.D1.y;
                P1.z = result.P0.z + m * result.D1.z;

                // 计算P3 = P4 - n*D2
                Vector3D_Bridge_arc_arc_2 P3;
                P3.x = result.P4.x - n * result.D2.x;
                P3.y = result.P4.y - n * result.D2.y;
                P3.z = result.P4.z - n * result.D2.z;

                // 计算Pc = 0.25*P1 + 0.5*P2 + 0.25*P3
                Vector3D_Bridge_arc_arc_2 Pc;
                Pc.x = 0.25 * P1.x + 0.5 * result.P2.x + 0.25 * P3.x;
                Pc.y = 0.25 * P1.y + 0.5 * result.P2.y + 0.25 * P3.y;
                Pc.z = 0.25 * P1.z + 0.5 * result.P2.z + 0.25 * P3.z;

                // 计算 norm(Pq-Pc)
                Vector3D_Bridge_arc_arc_2 diff;
                diff.x = result.Pq.x - Pc.x;
                diff.y = result.Pq.y - Pc.y;
                diff.z = result.Pq.z - Pc.z;
                error_matrix[count_m][count_n] = sqrt(diff.x * diff.x + diff.y * diff.y + diff.z * diff.z);
            }
        }

        // 调用findMinIndex找出最小值位置
        int row = 0, col = 0;
        findMinIndex((double**)error_matrix, 50, 50, &row, &col);

        // 根据MATLAB代码设置m和n
        result.m = (row + 1) * 0.01;  // MATLAB从1开始
        result.n = (col + 1) * 0.01;  // MATLAB从1开始

        // 使用得到的m和n值计算
        result.P1.x = result.P0.x + result.m * result.D1.x;
        result.P1.y = result.P0.y + result.m * result.D1.y;
        result.P1.z = result.P0.z + result.m * result.D1.z;

        result.P3.x = result.P4.x - result.n * result.D2.x;
        result.P3.y = result.P4.y - result.n * result.D2.y;
        result.P3.z = result.P4.z - result.n * result.D2.z;

        // 计算得到的Pc
        result.Pc.x = 0.25 * result.P1.x + 0.5 * result.P2.x + 0.25 * result.P3.x;
        result.Pc.y = 0.25 * result.P1.y + 0.5 * result.P2.y + 0.25 * result.P3.y;
        result.Pc.z = 0.25 * result.P1.z + 0.5 * result.P2.z + 0.25 * result.P3.z;

        // 计算误差
        Vector3D_Bridge_arc_arc_2 final_diff;
        final_diff.x = result.Pc.x - result.Pq.x;
        final_diff.y = result.Pc.y - result.Pq.y;
        final_diff.z = result.Pc.z - result.Pq.z;
        result.error = sqrt(final_diff.x * final_diff.x + final_diff.y * final_diff.y + final_diff.z * final_diff.z);

        // 更新control_point_3结构体
        result.control_point_3.P0 = result.P0;
        result.control_point_3.P1 = result.P1;
        result.control_point_3.P2 = result.P2;
        result.control_point_3.P3 = result.P3;
        result.control_point_3.P4 = result.P4;

        // 释放error_matrix内存
        for (int i = 0; i < 50; i++) {
            free(error_matrix[i]);
        }
        free(error_matrix);
    }

    // 返回result前做一些处理
    // 准备 u=0:0.01:1
    int u_length = 101;  // 0到1，每0.01一个，一共101个
    double* u = (double*)malloc(u_length * sizeof(double));
    if (u == NULL) {
        printf("内存分配失败\n");
        return result;
    }
    for (int i = 0; i < u_length; i++) {
        u[i] = i * 0.01;
    }

    // 计算U矩阵
    double* U = (double*)malloc(u_length * 4 * sizeof(double));
    if (U == NULL) {
        free(u);
        printf("内存分配失败\n");
        return result;
    }
    U_matrix(u, u_length, U);

    // 准备BB1矩阵
    double BB1[4][4] = {
        {1.0, 0.0, 0.0, 0.0},
        {-3.0, 3.0, 0.0, 0.0},
        {3.0, -4.5, 1.5, 0.0},
        {-1.0, 7.0 / 4.0, -1.0, 0.25}
    };

    // 准备control_point_3前4个点
    double control_points[4][3] = {
        {result.control_point_3.P0.x, result.control_point_3.P0.y, result.control_point_3.P0.z},
        {result.control_point_3.P1.x, result.control_point_3.P1.y, result.control_point_3.P1.z},
        {result.control_point_3.P2.x, result.control_point_3.P2.y, result.control_point_3.P2.z},
        {result.control_point_3.P3.x, result.control_point_3.P3.y, result.control_point_3.P3.z}
    };

    // 计算PP1 = U * BB1 * control_points
    // 先计算 BB1 * control_points
    double temp[4][3];
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 3; j++) {
            temp[i][j] = 0.0;
            for (int k = 0; k < 4; k++) {
                temp[i][j] += BB1[i][k] * control_points[k][j];
            }
        }
    }

    // 然后计算 U * (BB1 * control_points)
    double* PP1 = (double*)malloc(u_length * 3 * sizeof(double));
    if (PP1 == NULL) {
        free(u);
        free(U);
        printf("内存分配失败\n");
        return result;
    }

    for (int i = 0; i < u_length; i++) {
        for (int j = 0; j < 3; j++) {
            PP1[i * 3 + j] = 0.0;
            for (int k = 0; k < 4; k++) {
                PP1[i * 3 + j] += U[i * 4 + k] * temp[k][j];
            }
        }
    }

    // 准备BB2矩阵
    double BB2[4][4] = {
        {0.25, 0.5, 0.25, 0.0},
        {-0.75, 0.0, 0.75, 0.0},
        {0.75, -1.5, 0.75, 0.0},
        {-0.25, 1.0, -1.75, 1.0}
    };

    // 准备control_point_3后4个点(2:5)
    double control_points_2[4][3] = {
        {result.control_point_3.P1.x, result.control_point_3.P1.y, result.control_point_3.P1.z},
        {result.control_point_3.P2.x, result.control_point_3.P2.y, result.control_point_3.P2.z},
        {result.control_point_3.P3.x, result.control_point_3.P3.y, result.control_point_3.P3.z},
        {result.control_point_3.P4.x, result.control_point_3.P4.y, result.control_point_3.P4.z}
    };

    // 计算PP2 = U * BB2 * control_points_2
    // 先计算 BB2 * control_points_2
    double temp2[4][3];
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 3; j++) {
            temp2[i][j] = 0.0;
            for (int k = 0; k < 4; k++) {
                temp2[i][j] += BB2[i][k] * control_points_2[k][j];
            }
        }
    }

    // 然后计算 U * (BB2 * control_points_2)
    double* PP2 = (double*)malloc(u_length * 3 * sizeof(double));
    if (PP2 == NULL) {
        free(u);
        free(U);
        free(PP1);
        printf("内存分配失败\n");
        return result;
    }

    for (int i = 0; i < u_length; i++) {
        for (int j = 0; j < 3; j++) {
            PP2[i * 3 + j] = 0.0;
            for (int k = 0; k < 4; k++) {
                PP2[i * 3 + j] += U[i * 4 + k] * temp2[k][j];
            }
        }
    }



    // PP1(end,:)=[]; 去掉PP1最后一行
    int PP1_rows = u_length - 1;  // 去掉最后一行

    // P=[PP1;PP2]; 合并PP1(去掉最后一行)和PP2
    int total_rows = PP1_rows + u_length;  // 总行数
    double* P = (double*)malloc(total_rows * 3 * sizeof(double));
    if (P == NULL) {
        free(u);
        free(U);
        free(PP1);
        free(PP2);
        printf("内存分配失败\n");
        return result;
    }

    // 复制PP1(去掉最后一行)到P
    for (int i = 0; i < PP1_rows; i++) {
        P[i * 3] = PP1[i * 3];
        P[i * 3 + 1] = PP1[i * 3 + 1];
        P[i * 3 + 2] = PP1[i * 3 + 2];
    }

    // 复制PP2到P后面
    for (int i = 0; i < u_length; i++) {
        P[(PP1_rows + i) * 3] = PP2[i * 3];
        P[(PP1_rows + i) * 3 + 1] = PP2[i * 3 + 1];
        P[(PP1_rows + i) * 3 + 2] = PP2[i * 3 + 2];
    }

 

    // 为Quasi_Bspline_K_1准备数据
    // 为第一段准备control_points_1 (1:4)
    double control_points_quasi_1[4][3] = {
        {result.control_point_3.P0.x, result.control_point_3.P0.y, result.control_point_3.P0.z},
        {result.control_point_3.P1.x, result.control_point_3.P1.y, result.control_point_3.P1.z},
        {result.control_point_3.P2.x, result.control_point_3.P2.y, result.control_point_3.P2.z},
        {result.control_point_3.P3.x, result.control_point_3.P3.y, result.control_point_3.P3.z}
    };

    // 为第二段准备control_points_2 (2:5)
    double control_points_quasi_2[4][3] = {
        {result.control_point_3.P1.x, result.control_point_3.P1.y, result.control_point_3.P1.z},
        {result.control_point_3.P2.x, result.control_point_3.P2.y, result.control_point_3.P2.z},
        {result.control_point_3.P3.x, result.control_point_3.P3.y, result.control_point_3.P3.z},
        {result.control_point_3.P4.x, result.control_point_3.P4.y, result.control_point_3.P4.z}
    };

    // 分配内存
    double* K_1 = (double*)malloc(u_length * sizeof(double));
    double* K_2 = (double*)malloc(u_length * sizeof(double));
    double (*D11)[3] = malloc(u_length * sizeof(*D11));
    double (*D12)[3] = malloc(u_length * sizeof(*D12));
    double (*D21)[3] = malloc(u_length * sizeof(*D21));
    double (*D22)[3] = malloc(u_length * sizeof(*D22));
    double (*N11)[3] = malloc(u_length * sizeof(*N11));
    double (*N22)[3] = malloc(u_length * sizeof(*N22));
    double (*B11)[3] = malloc(u_length * sizeof(*B11));
    double (*B22)[3] = malloc(u_length * sizeof(*B22));

    if (!K_1 || !K_2 || !D11 || !D12 || !D21 || !D22 || !N11 || !N22 || !B11 || !B22) {
        // 内存分配失败，释放已分配的内存
        if (K_1) free(K_1);
        if (K_2) free(K_2);
        if (D11) free(D11);
        if (D12) free(D12);
        if (D21) free(D21);
        if (D22) free(D22);
        if (N11) free(N11);
        if (N22) free(N22);
        if (B11) free(B11);
        if (B22) free(B22);
        free(u);
        free(U);
        free(PP1);
        free(PP2);
        free(P);
        printf("内存分配失败\n");
        return result;
    }

    // 调用Quasi_Bspline_K_1计算第一段
    Quasi_Bspline_K_1(control_points_quasi_1, BB1, u, u_length, K_1, D11, D12, N11, B11);

    // 调用Quasi_Bspline_K_1计算第二段
    Quasi_Bspline_K_1(control_points_quasi_2, BB2, u, u_length, K_2, D21, D22, N22, B22);



    // 计算X
    // K = [K_1,K_2]
    double* K = (double*)malloc(2 * u_length * sizeof(double));
    if (K == NULL) {
        printf("内存分配失败\n");
        // 释放之前分配的内存
        free(K_1);
        free(K_2);
        free(D11);
        free(D12);
        free(D21);
        free(D22);
        free(N11);
        free(N22);
        free(B11);
        free(B22);
        free(u);
        free(U);
        free(PP1);
        free(PP2);
        free(P);
        return result;
    }
    // 复制K_1和K_2到K
    memcpy(K, K_1, u_length * sizeof(double));
    memcpy(K + u_length, K_2, u_length * sizeof(double));

    // D1 = [D11;D21]
    double (*D1)[3] = malloc(2 * u_length * sizeof(*D1));
    if (D1 == NULL) {
        printf("内存分配失败\n");
        free(K);
        // 释放之前分配的内存...
        return result;
    }
    // 复制D11和D21到D1
    memcpy(D1, D11, u_length * sizeof(*D1));
    memcpy(D1 + u_length, D21, u_length * sizeof(*D1));

    // D2 = [D12;D22]
    double (*D2)[3] = malloc(2 * u_length * sizeof(*D2));
    if (D2 == NULL) {
        printf("内存分配失败\n");
        free(K);
        free(D1);
        // 释放之前分配的内存...
        return result;
    }
    // 复制D12和D22到D2
    memcpy(D2, D12, u_length * sizeof(*D2));
    memcpy(D2 + u_length, D22, u_length * sizeof(*D2));

    // N = [N11;N22]
    double (*N)[3] = malloc(2 * u_length * sizeof(*N));
    if (N == NULL) {
        printf("内存分配失败\n");
        free(K);
        free(D1);
        free(D2);
        // 释放之前分配的内存...
        return result;
    }
    // 复制N11和N22到N
    memcpy(N, N11, u_length * sizeof(*N));
    memcpy(N + u_length, N22, u_length * sizeof(*N));

    // B = [B11;B22]
    double (*B)[3] = malloc(2 * u_length * sizeof(*B));
    if (B == NULL) {
        printf("内存分配失败\n");
        free(K);
        free(D1);
        free(D2);
        free(N);
        // 释放之前分配的内存...
        return result;
    }
    // 复制B11和B22到B
    memcpy(B, B11, u_length * sizeof(*B));
    memcpy(B + u_length, B22, u_length * sizeof(*B));

    // X = [K',D1,D2,N,B]
    // 这里需要为X分配内存，因为它的大小是 (2*u_length) 行 13 (K转置1列，D13列，D23列，N3列，B3列)
    double (*X)[13] = malloc(2 * u_length * sizeof(*X));
    if (X == NULL) {
        printf("内存分配失败\n");
        free(K);
        free(D1);
        free(D2);
        free(N);
        free(B);
        // 释放之前分配的内存...
        return result;
    }

    // 填充X
    for (int i = 0; i < 2 * u_length; i++) {
        // K'
        X[i][0] = K[i];
        // D1
        X[i][1] = D1[i][0];
        X[i][2] = D1[i][1];
        X[i][3] = D1[i][2];
        // D2
        X[i][4] = D2[i][0];
        X[i][5] = D2[i][1];
        X[i][6] = D2[i][2];
        // N
        X[i][7] = N[i][0];
        X[i][8] = N[i][1];
        X[i][9] = N[i][2];
        // B
        X[i][10] = B[i][0];
        X[i][11] = B[i][1];
        X[i][12] = B[i][2];
    }

  

    // 保存P数组到result结构体
    result.P_rows = total_rows;
    result.P_cols = 3;
    result.P = (double**)malloc(total_rows * sizeof(double*));
    if (result.P != NULL) {
        for (int i = 0; i < total_rows; i++) {
            result.P[i] = (double*)malloc(3 * sizeof(double));
            if (result.P[i] != NULL) {
                result.P[i][0] = P[i * 3];
                result.P[i][1] = P[i * 3 + 1];
                result.P[i][2] = P[i * 3 + 2];
            }
        }
    }

    // 保存X数组到result结构体
    result.X_rows = 2 * u_length;
    result.X_cols = 13;
    result.X = (double**)malloc(result.X_rows * sizeof(double*));
    if (result.X != NULL) {
        for (int i = 0; i < result.X_rows; i++) {
            result.X[i] = (double*)malloc(13 * sizeof(double));
            if (result.X[i] != NULL) {
                for (int j = 0; j < 13; j++) {
                    result.X[i][j] = X[i][j];
                }
            }
        }
    }

    // 释放临时内存
    free(u);
    free(U);
    free(PP1);
    free(PP2);
    free(P);
    free(K_1);
    free(K_2);
    free(D11);
    free(D12);
    free(D21);
    free(D22);
    free(N11);
    free(N22);
    free(B11);
    free(B22);
    free(K);
    free(D1);
    free(D2);
    free(N);
    free(B);
    free(X);


    return result;
}