#include <math.h>
#include "Trans_ZYXeulertoquat.h"
#include "quaternProd.h"

Quaternion_quaternProd Trans_ZYXeulertoquat(EulerAngle_quaternProd O1) {
    // ��ʼ����Ԫ���ṹ�壨ʹ���µĲ������ͣ�
    Quaternion_quaternProd q_z11 = {
        .w = cos(O1.z / 2),
        .x = 0.0,
        .y = 0.0,
        .z = sin(O1.z / 2)
    };
    Quaternion_quaternProd q_y11 = {
        .w = cos(O1.y / 2),
        .x = 0.0,
        .y = sin(O1.y / 2),
        .z = 0.0
    };
    Quaternion_quaternProd q_z12 = {
        .w = cos(O1.x / 2),
        .x = sin(O1.x / 2),
        .y = 0.0,
        .z = 0.0
    };

    // ��Ԫ���˷���ʹ��ȡ��ַ������ָ�룩
    Quaternion_quaternProd temp = quaternProd(&q_y11, &q_z12);
    Quaternion_quaternProd quat = quaternProd(&q_z11, &temp);

    // �������˳�� [w, z, y, x]
    return (Quaternion_quaternProd) {
        .w = quat.w,
            .x = quat.z,
            .y = quat.y,
            .z = quat.x
    };
}