//#define _CRT_SECURE_NO_WARNINGS
//
//#include <stdio.h>
//#include <stdlib.h>
//#include <math.h>
//#include "fine_fitting.h"
//
//// Utility function to save matrix to file for inspection
//void save_matrix_to_file(Matrix* mat, const char* filename) {
//    FILE* file = fopen(filename, "w");
//    if (file == NULL) {
//        printf("Error opening file %s\n", filename);
//        return;
//    }
//    
//    fprintf(file, "Matrix dimensions: %d x %d\n", mat->rows, mat->cols);
//    for (int i = 0; i < mat->rows; i++) {
//        for (int j = 0; j < mat->cols; j++) {
//            fprintf(file, "%f ", get_matrix_element(mat, i, j));
//        }
//        fprintf(file, "\n");
//    }
//    
//    fclose(file);
//    printf("Matrix saved to %s\n", filename);
//}
//
//// Read arc data from file
//Matrix* read_arc_from_file(const char* filename) {
//    FILE* file = fopen(filename, "r");
//    if (file == NULL) {
//        printf("Error opening file %s\n", filename);
//        return NULL;
//    }
//    
//    // First determine the number of points in the file
//    int num_points = 0;
//    char line[1024];
//    while (fgets(line, sizeof(line), file)) {
//        // Skip empty lines and comment lines
//        if (line[0] == '#' || line[0] == '\n' || strlen(line) < 2) {
//            continue;
//        }
//        num_points++;
//    }
//    
//    // Reset file pointer to beginning
//    rewind(file);
//    
//    // Create matrix to hold the points
//    Matrix* arc = create_matrix(num_points, 3);
//    if (!arc) {
//        printf("Failed to create matrix for arc data\n");
//        fclose(file);
//        return NULL;
//    }
//    
//    // Read points into matrix
//    int row = 0;
//    while (fgets(line, sizeof(line), file) && row < num_points) {
//        // Skip empty lines and comment lines
//        if (line[0] == '#' || line[0] == '\n' || strlen(line) < 2) {
//            continue;
//        }
//        
//        double x, y, z;
//        if (sscanf(line, "%lf %lf %lf", &x, &y, &z) == 3) {
//            set_matrix_element(arc, row, 0, x);
//            set_matrix_element(arc, row, 1, y);
//            set_matrix_element(arc, row, 2, z);
//            row++;
//        }
//    }
//    
//    fclose(file);
//    printf("Read %d points from file %s\n", arc->rows, filename);
//    return arc;
//}
//
//int main() {
//    printf("Fine fitting test program starting...\n");
//    
//    // Read arc data from file
//    Matrix* arc = read_arc_from_file("will_fit_pos.txt");
//    if (!arc) {
//        printf("Failed to read arc data. Exiting.\n");
//        return 1;
//    }
//    
//    printf("Read arc data with %d points\n", arc->rows);
//    
//    // Save input arc to file for verification
//    save_matrix_to_file(arc, "test_arc_input.txt");
//    
//    // Call fine_fitting to process the arc
//    Matrix* pos = NULL;
//    Matrix* Nikx = NULL;
//    Matrix* Niky = NULL;
//    Matrix* Nikz = NULL;
//    Matrix* X = NULL;
//    
//    printf("Calling fine_fitting...\n");
//    fine_fitting(arc, &pos, &Nikx, &Niky, &Nikz, &X);
//    printf("fine_fitting completed\n");
//    
//    // Save results to files for inspection
//    save_matrix_to_file(pos, "test_pos_output.txt");
//    save_matrix_to_file(Nikx, "test_Nikx_output.txt");
//    save_matrix_to_file(Niky, "test_Niky_output.txt");
//    save_matrix_to_file(Nikz, "test_Nikz_output.txt");
//    save_matrix_to_file(X, "test_X_output.txt");
//    
//    // Free allocated memory
//    free_matrix(arc);
//    free_matrix(pos);
//    free_matrix(Nikx);
//    free_matrix(Niky);
//    free_matrix(Nikz);
//    free_matrix(X);
//    
//    printf("Test completed successfully!\n");
//    return 0;
//} 